package com.wzsec.modules.oc.service.mapper;

import com.wzsec.modules.oc.domain.OcCommand;
import com.wzsec.modules.oc.service.dto.OcCommandDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:30+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class OcCommandMapperImpl implements OcCommandMapper {

    @Override
    public OcCommand toEntity(OcCommandDto dto) {
        if ( dto == null ) {
            return null;
        }

        OcCommand ocCommand = new OcCommand();

        ocCommand.setId( dto.getId() );
        ocCommand.setCommand( dto.getCommand() );
        ocCommand.setRegexps( dto.getRegexps() );
        ocCommand.setDescription( dto.getDescription() );
        ocCommand.setDetailed( dto.getDetailed() );
        ocCommand.setCommandtype( dto.getCommandtype() );
        ocCommand.setRisk( dto.getRisk() );
        ocCommand.setApptype( dto.getApptype() );
        ocCommand.setNote( dto.getNote() );
        ocCommand.setCreateuser( dto.getCreateuser() );
        ocCommand.setCreatetime( dto.getCreatetime() );
        ocCommand.setUpdateuser( dto.getUpdateuser() );
        ocCommand.setUpdatetime( dto.getUpdatetime() );
        ocCommand.setSparefield1( dto.getSparefield1() );
        ocCommand.setSparefield2( dto.getSparefield2() );
        ocCommand.setSparefield3( dto.getSparefield3() );
        ocCommand.setSparefield4( dto.getSparefield4() );

        return ocCommand;
    }

    @Override
    public OcCommandDto toDto(OcCommand entity) {
        if ( entity == null ) {
            return null;
        }

        OcCommandDto ocCommandDto = new OcCommandDto();

        ocCommandDto.setId( entity.getId() );
        ocCommandDto.setCommand( entity.getCommand() );
        ocCommandDto.setRegexps( entity.getRegexps() );
        ocCommandDto.setDescription( entity.getDescription() );
        ocCommandDto.setDetailed( entity.getDetailed() );
        ocCommandDto.setCommandtype( entity.getCommandtype() );
        ocCommandDto.setRisk( entity.getRisk() );
        ocCommandDto.setApptype( entity.getApptype() );
        ocCommandDto.setNote( entity.getNote() );
        ocCommandDto.setCreateuser( entity.getCreateuser() );
        ocCommandDto.setCreatetime( entity.getCreatetime() );
        ocCommandDto.setUpdateuser( entity.getUpdateuser() );
        ocCommandDto.setUpdatetime( entity.getUpdatetime() );
        ocCommandDto.setSparefield1( entity.getSparefield1() );
        ocCommandDto.setSparefield2( entity.getSparefield2() );
        ocCommandDto.setSparefield3( entity.getSparefield3() );
        ocCommandDto.setSparefield4( entity.getSparefield4() );

        return ocCommandDto;
    }

    @Override
    public List<OcCommand> toEntity(List<OcCommandDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<OcCommand> list = new ArrayList<OcCommand>( dtoList.size() );
        for ( OcCommandDto ocCommandDto : dtoList ) {
            list.add( toEntity( ocCommandDto ) );
        }

        return list;
    }

    @Override
    public List<OcCommandDto> toDto(List<OcCommand> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<OcCommandDto> list = new ArrayList<OcCommandDto>( entityList.size() );
        for ( OcCommand ocCommand : entityList ) {
            list.add( toDto( ocCommand ) );
        }

        return list;
    }
}
