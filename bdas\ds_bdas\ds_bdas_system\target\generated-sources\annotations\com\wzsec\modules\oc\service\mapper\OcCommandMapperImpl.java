package com.wzsec.modules.oc.service.mapper;

import com.wzsec.modules.oc.domain.OcCommand;
import com.wzsec.modules.oc.service.dto.OcCommandDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:28+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class OcCommandMapperImpl implements OcCommandMapper {

    @Override
    public OcCommandDto toDto(OcCommand entity) {
        if ( entity == null ) {
            return null;
        }

        OcCommandDto ocCommandDto = new OcCommandDto();

        ocCommandDto.setApptype( entity.getApptype() );
        ocCommandDto.setCommand( entity.getCommand() );
        ocCommandDto.setCommandtype( entity.getCommandtype() );
        ocCommandDto.setCreatetime( entity.getCreatetime() );
        ocCommandDto.setCreateuser( entity.getCreateuser() );
        ocCommandDto.setDescription( entity.getDescription() );
        ocCommandDto.setDetailed( entity.getDetailed() );
        ocCommandDto.setId( entity.getId() );
        ocCommandDto.setNote( entity.getNote() );
        ocCommandDto.setRegexps( entity.getRegexps() );
        ocCommandDto.setRisk( entity.getRisk() );
        ocCommandDto.setSparefield1( entity.getSparefield1() );
        ocCommandDto.setSparefield2( entity.getSparefield2() );
        ocCommandDto.setSparefield3( entity.getSparefield3() );
        ocCommandDto.setSparefield4( entity.getSparefield4() );
        ocCommandDto.setUpdatetime( entity.getUpdatetime() );
        ocCommandDto.setUpdateuser( entity.getUpdateuser() );

        return ocCommandDto;
    }

    @Override
    public List<OcCommandDto> toDto(List<OcCommand> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<OcCommandDto> list = new ArrayList<OcCommandDto>( entityList.size() );
        for ( OcCommand ocCommand : entityList ) {
            list.add( toDto( ocCommand ) );
        }

        return list;
    }

    @Override
    public OcCommand toEntity(OcCommandDto dto) {
        if ( dto == null ) {
            return null;
        }

        OcCommand ocCommand = new OcCommand();

        ocCommand.setApptype( dto.getApptype() );
        ocCommand.setCommand( dto.getCommand() );
        ocCommand.setCommandtype( dto.getCommandtype() );
        ocCommand.setCreatetime( dto.getCreatetime() );
        ocCommand.setCreateuser( dto.getCreateuser() );
        ocCommand.setDescription( dto.getDescription() );
        ocCommand.setDetailed( dto.getDetailed() );
        ocCommand.setId( dto.getId() );
        ocCommand.setNote( dto.getNote() );
        ocCommand.setRegexps( dto.getRegexps() );
        ocCommand.setRisk( dto.getRisk() );
        ocCommand.setSparefield1( dto.getSparefield1() );
        ocCommand.setSparefield2( dto.getSparefield2() );
        ocCommand.setSparefield3( dto.getSparefield3() );
        ocCommand.setSparefield4( dto.getSparefield4() );
        ocCommand.setUpdatetime( dto.getUpdatetime() );
        ocCommand.setUpdateuser( dto.getUpdateuser() );

        return ocCommand;
    }

    @Override
    public List<OcCommand> toEntity(List<OcCommandDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<OcCommand> list = new ArrayList<OcCommand>( dtoList.size() );
        for ( OcCommandDto ocCommandDto : dtoList ) {
            list.add( toEntity( ocCommandDto ) );
        }

        return list;
    }
}
