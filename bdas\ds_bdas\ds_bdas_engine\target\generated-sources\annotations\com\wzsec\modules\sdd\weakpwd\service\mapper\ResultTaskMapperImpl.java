package com.wzsec.modules.sdd.weakpwd.service.mapper;

import com.wzsec.modules.sdd.weakpwd.domain.ResultTask;
import com.wzsec.modules.sdd.weakpwd.service.dto.ResultTaskDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:02+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ResultTaskMapperImpl implements ResultTaskMapper {

    @Override
    public ResultTask toEntity(ResultTaskDto dto) {
        if ( dto == null ) {
            return null;
        }

        ResultTask resultTask = new ResultTask();

        resultTask.setId( dto.getId() );
        resultTask.setTaskno( dto.getTaskno() );
        resultTask.setDbcount( dto.getDbcount() );
        resultTask.setWpdbcount( dto.getWpdbcount() );
        resultTask.setWpcount( dto.getWpcount() );
        resultTask.setState( dto.getState() );
        resultTask.setStarttime( dto.getStarttime() );
        resultTask.setEndtime( dto.getEndtime() );
        resultTask.setUsetime( dto.getUsetime() );
        resultTask.setCreatetime( dto.getCreatetime() );
        resultTask.setSubmituser( dto.getSubmituser() );
        resultTask.setSparefield1( dto.getSparefield1() );
        resultTask.setSparefield2( dto.getSparefield2() );
        resultTask.setSparefield3( dto.getSparefield3() );
        resultTask.setSparefield4( dto.getSparefield4() );
        resultTask.setSparefield5( dto.getSparefield5() );

        return resultTask;
    }

    @Override
    public ResultTaskDto toDto(ResultTask entity) {
        if ( entity == null ) {
            return null;
        }

        ResultTaskDto resultTaskDto = new ResultTaskDto();

        resultTaskDto.setId( entity.getId() );
        resultTaskDto.setTaskno( entity.getTaskno() );
        resultTaskDto.setDbcount( entity.getDbcount() );
        resultTaskDto.setWpdbcount( entity.getWpdbcount() );
        resultTaskDto.setWpcount( entity.getWpcount() );
        resultTaskDto.setState( entity.getState() );
        resultTaskDto.setStarttime( entity.getStarttime() );
        resultTaskDto.setEndtime( entity.getEndtime() );
        resultTaskDto.setUsetime( entity.getUsetime() );
        resultTaskDto.setCreatetime( entity.getCreatetime() );
        resultTaskDto.setSubmituser( entity.getSubmituser() );
        resultTaskDto.setSparefield1( entity.getSparefield1() );
        resultTaskDto.setSparefield2( entity.getSparefield2() );
        resultTaskDto.setSparefield3( entity.getSparefield3() );
        resultTaskDto.setSparefield4( entity.getSparefield4() );
        resultTaskDto.setSparefield5( entity.getSparefield5() );

        return resultTaskDto;
    }

    @Override
    public List<ResultTask> toEntity(List<ResultTaskDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ResultTask> list = new ArrayList<ResultTask>( dtoList.size() );
        for ( ResultTaskDto resultTaskDto : dtoList ) {
            list.add( toEntity( resultTaskDto ) );
        }

        return list;
    }

    @Override
    public List<ResultTaskDto> toDto(List<ResultTask> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ResultTaskDto> list = new ArrayList<ResultTaskDto>( entityList.size() );
        for ( ResultTask resultTask : entityList ) {
            list.add( toDto( resultTask ) );
        }

        return list;
    }
}
