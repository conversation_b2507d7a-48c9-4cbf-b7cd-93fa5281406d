/*********************************************************************
 *
 * CHINA TELECOM CORPORATION CONFIDENTIAL
 * ______________________________________________________________
 *
 *  [2015] - [2020] China Telecom Corporation Limited,
 *  All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of China Telecom Corporation and its suppliers,
 * if any. The intellectual and technical concepts contained
 * herein are proprietary to China Telecom Corporation and its
 * suppliers and may be covered by China and Foreign Patents,
 * patents in process, and are protected by trade secret  or
 * copyright law. Dissemination of this information or
 * reproduction of this material is strictly forbidden unless prior
 * written permission is obtained from China Telecom Corporation.
 **********************************************************************/
package com.wzsec.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * @ClassName: KeyProduceUtils
 * @Description: 复杂密钥产生工具类
 * <AUTHOR>
 * @date 2018年10月29日
 */
public class KeyProduceUtils {

	private static final String[] DigitValue = new String[] { "7", "6", "4", "8", "9", "3", "0", "2", "5", "1" };
	private static final String[] LowerLetterValue = new String[] { "c", "a", "b", "f", "d", "g", "k", "j", "h", "i",
			"n", "m", "l", "p", "q", "o", "r", "t", "s", "w", "v", "u", "z", "y", "x", "e" };
	private static final String[] CapitalLetterValue = new String[] { "D", "C", "A", "B", "H", "E", "F", "G", "L", "K",
			"I", "J", "Q", "P", "N", "U", "M", "V", "T", "S", "O", "R", "Y", "W", "X", "Z" };
	private static final String[] SpecialCharacter = new String[] { "[", "]", "!", "%", "^", "*", "(", ")", "+", "-",
			"?", "<", ">", "/", "{", "}", "=" };
	private static final String BlankSplitStr = " ";
	private static final int KEY_8_BIT = 8;
	private static final int KEY_16_BIT = 16;
	private static final int KEY_32_BIT = 32;

	private static final String Digit = "数字";
	private static final String LowerLetter = "小写字母";
	private static final String CapitalLetter = "大写字母";
	private static final String SpeCialCharacter = "特殊字符";

	/**
	 * @Description: TODO
	 * <AUTHOR>
	 * @date 2018年10月29日
	 */
	public static void main(String[] args) {
		for (int j = 0; j < 10; j++) {
			System.out.println(getSecretKey("16位", "小写字母,特殊字符,数字"));
			// System.out.println(getMaskAlgoKey(8, true));
		}
	}

	/**
	 * @Description: 产生由数字（不一定包含、大小写字母（不一定包含、特殊字符 （不一定包含）组成的固定长度随机密钥字符串
	 *               1.特殊字符需要的前提下，数字、大小写字母、特殊字符尽量都涉及到 2.密钥符合长度要求，顺序尽量混乱
	 *               3.特殊字符可以不要 备注:位数：8,16,32 特殊字符：1,2,4 其它: 大写字母、小写字母、数字
	 *               规则：各字符生成尽量均匀，不能均匀条件下尽量分给数字、小写字母
	 * <AUTHOR>
	 * @date 2019年4月25日
	 */
	public static String getSecretKey(String bit, String combination) {
		StringBuilder strBuilder = new StringBuilder();
		boolean isDigit = false;
		boolean isLowLetter = false;
		boolean isCapitalLetter = false;
		boolean isSpecialLetter = false;
		int keyLength = Integer.parseInt(bit.substring(0, bit.length() - 1));
		String[] combinations = combination.split(",");
		if (combination.contains(Digit)) {
			isDigit = true;
		}
		if (combination.contains(LowerLetter)) {
			isLowLetter = true;
		}
		if (combination.contains(CapitalLetter)) {
			isCapitalLetter = true;
		}
		if (combination.contains(SpeCialCharacter)) {
			isSpecialLetter = true;
		}

		if (keyLength == KEY_8_BIT) {
			if (combinations.length == 1) {
				if (isDigit) {
					// 随机取出8个数字
					String[] digitArr = getRepetitionRandomArray(DigitValue, 8);
					strBuilder.append(getSplitStrByStrArray(digitArr, BlankSplitStr));
				}
				if (isLowLetter) {
					// 随机取出8个小写字母
					String[] lowLetterArr = getRepetitionRandomArray(LowerLetterValue, 8);
					strBuilder.append(getSplitStrByStrArray(lowLetterArr, BlankSplitStr));
				}
				if (isCapitalLetter) {
					// 随机取出8个大写字母
					String[] capitalLetterArr = getRepetitionRandomArray(CapitalLetterValue, 8);
					strBuilder.append(getSplitStrByStrArray(capitalLetterArr, BlankSplitStr));
				}
				if (isSpecialLetter) {
					// 随机取出8个特殊字符
					String[] specialLetterArr = getRepetitionRandomArray(SpecialCharacter, 8);
					strBuilder.append(getSplitStrByStrArray(specialLetterArr, BlankSplitStr));
				}
			} else if (combinations.length == 2) {
				int count = 8;
				if (isSpecialLetter) {
					// 随机取出1个特殊字符
					String[] specialLetterArr = getRepetitionRandomArray(SpecialCharacter, 1);
					strBuilder.append(specialLetterArr[0]);
					count = count - 1;
				}
				if (count == 7) {
					if (isDigit) {
						// 随机取出7个数字
						String[] digitArr = getRepetitionRandomArray(DigitValue, 7);
						strBuilder.append(getSplitStrByStrArray(digitArr, BlankSplitStr));
					}
					if (isLowLetter) {
						// 随机取出7个小写字母
						String[] lowLetterArr = getRepetitionRandomArray(LowerLetterValue, 7);
						strBuilder.append(getSplitStrByStrArray(lowLetterArr, BlankSplitStr));
					}
					if (isCapitalLetter) {
						// 随机取出7个大写字母
						String[] capitalLetterArr = getRepetitionRandomArray(CapitalLetterValue, 7);
						strBuilder.append(getSplitStrByStrArray(capitalLetterArr, BlankSplitStr));
					}
				} else {
					if (isDigit) {
						// 随机取出4个数字
						String[] digitArr = getRepetitionRandomArray(DigitValue, 4);
						strBuilder.append(getSplitStrByStrArray(digitArr, BlankSplitStr));
					}
					if (isLowLetter) {
						// 随机取出4个小写字母
						String[] lowLetterArr = getRepetitionRandomArray(LowerLetterValue, 4);
						strBuilder.append(getSplitStrByStrArray(lowLetterArr, BlankSplitStr));
					}
					if (isCapitalLetter) {
						// 随机取出4个大写字母
						String[] capitalLetterArr = getRepetitionRandomArray(CapitalLetterValue, 4);
						strBuilder.append(getSplitStrByStrArray(capitalLetterArr, BlankSplitStr));
					}
				}
			} else if (combinations.length == 3) {
				int count = 8;
				if (isSpecialLetter) {
					// 随机取出1个特殊字符
					String[] specialLetterArr = getRepetitionRandomArray(SpecialCharacter, 1);
					strBuilder.append(specialLetterArr[0]);
					count = count - 1;
				}
				if (count == 7) {
					if (isDigit) {
						// 随机取出4个数字
						String[] digitArr = getRepetitionRandomArray(DigitValue, 4);
						strBuilder.append(getSplitStrByStrArray(digitArr, BlankSplitStr));
						count = count - 4;
					}
					if (isLowLetter) {
						if (count < 4) {
							// 随机取出3个小写字母
							String[] lowLetterArr = getRepetitionRandomArray(LowerLetterValue, 3);
							strBuilder.append(getSplitStrByStrArray(lowLetterArr, BlankSplitStr));
						} else {
							// 随机取出4个小写字母
							String[] lowLetterArr = getRepetitionRandomArray(LowerLetterValue, 4);
							strBuilder.append(getSplitStrByStrArray(lowLetterArr, BlankSplitStr));
						}
					}
					if (isCapitalLetter) {
						// 随机取出3个大写字母
						String[] capitalLetterArr = getRepetitionRandomArray(CapitalLetterValue, 3);
						strBuilder.append(getSplitStrByStrArray(capitalLetterArr, BlankSplitStr));
					}
				} else {
					// 随机取出3个数字
					String[] digitArr = getRepetitionRandomArray(DigitValue, 3);
					strBuilder.append(getSplitStrByStrArray(digitArr, BlankSplitStr));
					// 随机取出3个小写字母
					String[] lowLetterArr = getRepetitionRandomArray(LowerLetterValue, 3);
					strBuilder.append(getSplitStrByStrArray(lowLetterArr, BlankSplitStr));
					// 随机取出2个大写字母
					String[] capitalLetterArr = getRepetitionRandomArray(CapitalLetterValue, 2);
					strBuilder.append(getSplitStrByStrArray(capitalLetterArr, BlankSplitStr));
				}
			} else if (combinations.length == 4) {
				// 随机取出1个特殊字符
				String[] specialLetterArr = getRepetitionRandomArray(SpecialCharacter, 1);
				strBuilder.append(specialLetterArr[0]);
				// 随机取出3个数字
				String[] digitArr = getRepetitionRandomArray(DigitValue, 3);
				strBuilder.append(getSplitStrByStrArray(digitArr, BlankSplitStr));
				// 随机取出2个小写字母
				String[] lowLetterArr = getRepetitionRandomArray(LowerLetterValue, 2);
				strBuilder.append(getSplitStrByStrArray(lowLetterArr, BlankSplitStr));
				// 随机取出2个大写字母
				String[] capitalLetterArr = getRepetitionRandomArray(CapitalLetterValue, 2);
				strBuilder.append(getSplitStrByStrArray(capitalLetterArr, BlankSplitStr));
			}
		} else if (keyLength == KEY_16_BIT) {
			if (combinations.length == 1) {
				if (isDigit) {
					// 随机取出16个数字
					String[] digitArr = getRepetitionRandomArray(DigitValue, 16);
					strBuilder.append(getSplitStrByStrArray(digitArr, BlankSplitStr));
				}
				if (isLowLetter) {
					// 随机取出16个小写字母
					String[] lowLetterArr = getRepetitionRandomArray(LowerLetterValue, 16);
					strBuilder.append(getSplitStrByStrArray(lowLetterArr, BlankSplitStr));
				}
				if (isCapitalLetter) {
					// 随机取出16个大写字母
					String[] capitalLetterArr = getRepetitionRandomArray(CapitalLetterValue, 16);
					strBuilder.append(getSplitStrByStrArray(capitalLetterArr, BlankSplitStr));
				}
				if (isSpecialLetter) {
					// 随机取出16个特殊字符
					String[] specialLetterArr = getRepetitionRandomArray(SpecialCharacter, 16);
					strBuilder.append(getSplitStrByStrArray(specialLetterArr, BlankSplitStr));
				}
			} else if (combinations.length == 2) {
				int count = 16;
				if (isSpecialLetter) {
					// 随机取出2个特殊字符
					String[] specialLetterArr = getRepetitionRandomArray(SpecialCharacter, 2);
					strBuilder.append(getSplitStrByStrArray(specialLetterArr, BlankSplitStr));
					count = count - 2;
				}
				if (count == 14) {
					if (isDigit) {
						// 随机取出14个数字
						String[] digitArr = getRepetitionRandomArray(DigitValue, 14);
						strBuilder.append(getSplitStrByStrArray(digitArr, BlankSplitStr));
					}
					if (isLowLetter) {
						// 随机取出14个小写字母
						String[] lowLetterArr = getRepetitionRandomArray(LowerLetterValue, 14);
						strBuilder.append(getSplitStrByStrArray(lowLetterArr, BlankSplitStr));
					}
					if (isCapitalLetter) {
						// 随机取出14个大写字母
						String[] capitalLetterArr = getRepetitionRandomArray(CapitalLetterValue, 14);
						strBuilder.append(getSplitStrByStrArray(capitalLetterArr, BlankSplitStr));
					}
				} else {
					if (isDigit) {
						// 随机取出8个数字
						String[] digitArr = getRepetitionRandomArray(DigitValue, 8);
						strBuilder.append(getSplitStrByStrArray(digitArr, BlankSplitStr));
					}
					if (isLowLetter) {
						// 随机取出8个小写字母
						String[] lowLetterArr = getRepetitionRandomArray(LowerLetterValue, 8);
						strBuilder.append(getSplitStrByStrArray(lowLetterArr, BlankSplitStr));
					}
					if (isCapitalLetter) {
						// 随机取出8个大写字母
						String[] capitalLetterArr = getRepetitionRandomArray(CapitalLetterValue, 8);
						strBuilder.append(getSplitStrByStrArray(capitalLetterArr, BlankSplitStr));
					}
				}
			} else if (combinations.length == 3) {
				int count = 16;
				if (isSpecialLetter) {
					// 随机取出2个特殊字符
					String[] specialLetterArr = getRepetitionRandomArray(SpecialCharacter, 2);
					strBuilder.append(getSplitStrByStrArray(specialLetterArr, BlankSplitStr));
					count = count - 2;
				}
				if (count == 14) {
					if (isDigit) {
						// 随机取出7个数字
						String[] digitArr = getRepetitionRandomArray(DigitValue, 7);
						strBuilder.append(getSplitStrByStrArray(digitArr, BlankSplitStr));
					}
					if (isLowLetter) {
						// 随机取出7个小写字母
						String[] lowLetterArr = getRepetitionRandomArray(LowerLetterValue, 7);
						strBuilder.append(getSplitStrByStrArray(lowLetterArr, BlankSplitStr));
					}
					if (isCapitalLetter) {
						// 随机取出7个大写字母
						String[] capitalLetterArr = getRepetitionRandomArray(CapitalLetterValue, 7);
						strBuilder.append(getSplitStrByStrArray(capitalLetterArr, BlankSplitStr));
					}
				} else {
					// 随机取出6个数字
					String[] digitArr = getRepetitionRandomArray(DigitValue, 6);
					strBuilder.append(getSplitStrByStrArray(digitArr, BlankSplitStr));
					// 随机取出5个小写字母
					String[] lowLetterArr = getRepetitionRandomArray(LowerLetterValue, 5);
					strBuilder.append(getSplitStrByStrArray(lowLetterArr, BlankSplitStr));
					// 随机取出5个大写字母
					String[] capitalLetterArr = getRepetitionRandomArray(CapitalLetterValue, 5);
					strBuilder.append(getSplitStrByStrArray(capitalLetterArr, BlankSplitStr));
				}
			} else if (combinations.length == 4) {
				// 随机取出2个特殊字符
				String[] specialLetterArr = getRepetitionRandomArray(SpecialCharacter, 2);
				strBuilder.append(getSplitStrByStrArray(specialLetterArr, BlankSplitStr));
				// 随机取出5个数字
				String[] digitArr = getRepetitionRandomArray(DigitValue, 5);
				strBuilder.append(getSplitStrByStrArray(digitArr, BlankSplitStr));
				// 随机取出5个小写字母
				String[] lowLetterArr = getRepetitionRandomArray(LowerLetterValue, 5);
				strBuilder.append(getSplitStrByStrArray(lowLetterArr, BlankSplitStr));
				// 随机取出4个大写字母
				String[] capitalLetterArr = getRepetitionRandomArray(CapitalLetterValue, 4);
				strBuilder.append(getSplitStrByStrArray(capitalLetterArr, BlankSplitStr));
			}
		} else if (keyLength == KEY_32_BIT) {
			if (combinations.length == 1) {
				if (isDigit) {
					// 随机取出32个数字
					String[] digitArr = getRepetitionRandomArray(DigitValue, 32);
					strBuilder.append(getSplitStrByStrArray(digitArr, BlankSplitStr));
				}
				if (isLowLetter) {
					// 随机取出32个小写字母
					String[] lowLetterArr = getRepetitionRandomArray(LowerLetterValue, 32);
					strBuilder.append(getSplitStrByStrArray(lowLetterArr, BlankSplitStr));
				}
				if (isCapitalLetter) {
					// 随机取出32个大写字母
					String[] capitalLetterArr = getRepetitionRandomArray(CapitalLetterValue, 32);
					strBuilder.append(getSplitStrByStrArray(capitalLetterArr, BlankSplitStr));
				}
				if (isSpecialLetter) {
					// 随机取出32个特殊字符
					String[] specialLetterArr = getRepetitionRandomArray(SpecialCharacter, 32);
					strBuilder.append(getSplitStrByStrArray(specialLetterArr, BlankSplitStr));
				}
			} else if (combinations.length == 2) {
				int count = 32;
				if (isSpecialLetter) {
					// 随机取出4个特殊字符
					String[] specialLetterArr = getRepetitionRandomArray(SpecialCharacter, 4);
					strBuilder.append(getSplitStrByStrArray(specialLetterArr, BlankSplitStr));
					count = count - 4;
				}
				if (count == 28) {
					if (isDigit) {
						// 随机取出28个数字
						String[] digitArr = getRepetitionRandomArray(DigitValue, 28);
						strBuilder.append(getSplitStrByStrArray(digitArr, BlankSplitStr));
					}
					if (isLowLetter) {
						// 随机取出28个小写字母
						String[] lowLetterArr = getRepetitionRandomArray(LowerLetterValue, 28);
						strBuilder.append(getSplitStrByStrArray(lowLetterArr, BlankSplitStr));
					}
					if (isCapitalLetter) {
						// 随机取出28个大写字母
						String[] capitalLetterArr = getRepetitionRandomArray(CapitalLetterValue, 28);
						strBuilder.append(getSplitStrByStrArray(capitalLetterArr, BlankSplitStr));
					}
				} else {
					if (isDigit) {
						// 随机取出16个数字
						String[] digitArr = getRepetitionRandomArray(DigitValue, 16);
						strBuilder.append(getSplitStrByStrArray(digitArr, BlankSplitStr));
					}
					if (isLowLetter) {
						// 随机取出16个小写字母
						String[] lowLetterArr = getRepetitionRandomArray(LowerLetterValue, 16);
						strBuilder.append(getSplitStrByStrArray(lowLetterArr, BlankSplitStr));
					}
					if (isCapitalLetter) {
						// 随机取出16个大写字母
						String[] capitalLetterArr = getRepetitionRandomArray(CapitalLetterValue, 16);
						strBuilder.append(getSplitStrByStrArray(capitalLetterArr, BlankSplitStr));
					}
				}
			} else if (combinations.length == 3) {
				int count = 32;
				if (isSpecialLetter) {
					// 随机取出4个特殊字符
					String[] specialLetterArr = getRepetitionRandomArray(SpecialCharacter, 4);
					strBuilder.append(getSplitStrByStrArray(specialLetterArr, BlankSplitStr));
					count = count - 4;
				}
				if (count == 28) {
					if (isDigit) {
						// 随机取出14个数字
						String[] digitArr = getRepetitionRandomArray(DigitValue, 14);
						strBuilder.append(getSplitStrByStrArray(digitArr, BlankSplitStr));
					}
					if (isLowLetter) {
						// 随机取出14个小写字母
						String[] lowLetterArr = getRepetitionRandomArray(LowerLetterValue, 14);
						strBuilder.append(getSplitStrByStrArray(lowLetterArr, BlankSplitStr));
					}
					if (isCapitalLetter) {
						// 随机取出14个大写字母
						String[] capitalLetterArr = getRepetitionRandomArray(CapitalLetterValue, 14);
						strBuilder.append(getSplitStrByStrArray(capitalLetterArr, BlankSplitStr));
					}
				} else {
					// 随机取出11个数字
					String[] digitArr = getRepetitionRandomArray(DigitValue, 11);
					strBuilder.append(getSplitStrByStrArray(digitArr, BlankSplitStr));
					// 随机取出11个小写字母
					String[] lowLetterArr = getRepetitionRandomArray(LowerLetterValue, 11);
					strBuilder.append(getSplitStrByStrArray(lowLetterArr, BlankSplitStr));
					// 随机取出10个大写字母
					String[] capitalLetterArr = getRepetitionRandomArray(CapitalLetterValue, 10);
					strBuilder.append(getSplitStrByStrArray(capitalLetterArr, BlankSplitStr));
				}
			} else if (combinations.length == 4) {
				// 随机取出4个特殊字符
				String[] specialLetterArr = getRepetitionRandomArray(SpecialCharacter, 4);
				strBuilder.append(getSplitStrByStrArray(specialLetterArr, BlankSplitStr));
				// 随机取出10个数字
				String[] digitArr = getRepetitionRandomArray(DigitValue, 10);
				strBuilder.append(getSplitStrByStrArray(digitArr, BlankSplitStr));
				// 随机取出9个小写字母
				String[] lowLetterArr = getRepetitionRandomArray(LowerLetterValue, 9);
				strBuilder.append(getSplitStrByStrArray(lowLetterArr, BlankSplitStr));
				// 随机取出9个大写字母
				String[] capitalLetterArr = getRepetitionRandomArray(CapitalLetterValue, 9);
				strBuilder.append(getSplitStrByStrArray(capitalLetterArr, BlankSplitStr));
			}
		}
		String mixDigtalLetterCharStrs = strBuilder.toString();
		if (mixDigtalLetterCharStrs != null && mixDigtalLetterCharStrs.length() > 0) {
			String[] mixDigtalLetterCharArr = mixDigtalLetterCharStrs.split(BlankSplitStr);
			String[] disorderArr = getDisorderArr(mixDigtalLetterCharArr); // 打乱顺序
			return StringUtils.join(disorderArr);
		} else {
			return null;
		}
	}

	/**
	 * @Description: 产生由数字、大小写字母、特殊字符 （不一定包含）组成的固定长度随机密钥字符串
	 *               1.特殊字符需要的前提下，数字、大小写字母、特殊字符尽量都涉及到 2.密钥符合长度要求，顺序尽量混乱
	 *               3.特殊字符可以不要
	 * <AUTHOR>
	 * @date 2018年10月29日
	 */
	public static String getMaskAlgoKey(int keyLength, boolean isExistSpecialCharacter) {
		StringBuilder strBuilder = new StringBuilder();
		if (keyLength == KEY_8_BIT) {
			if (isExistSpecialCharacter) {
				// 随机取出3个数字
				String[] digitArr = getRandomArray(DigitValue, 3);
				strBuilder.append(getSplitStrByStrArray(digitArr, BlankSplitStr));
				// 随机取出2个小写字母
				String[] lowLetterArr = getRandomArray(LowerLetterValue, 2);
				strBuilder.append(getSplitStrByStrArray(lowLetterArr, BlankSplitStr));
				// 随机取出2个大写字母
				String[] capitalLetterArr = getRandomArray(CapitalLetterValue, 2);
				strBuilder.append(getSplitStrByStrArray(capitalLetterArr, BlankSplitStr));
				// 随机取出1个特殊字符
				String[] specialLetterArr = getRandomArray(SpecialCharacter, 1);
				strBuilder.append(specialLetterArr[0]);
			} else {
				// 随机取出3个数字
				String[] digitArr = getRandomArray(DigitValue, 3);
				strBuilder.append(getSplitStrByStrArray(digitArr, BlankSplitStr));
				// 随机取出3个小写字母
				String[] lowLetterArr = getRandomArray(LowerLetterValue, 3);
				strBuilder.append(getSplitStrByStrArray(lowLetterArr, BlankSplitStr));
				// 随机取出2个大写字母
				String[] capitalLetterArr = getRandomArray(CapitalLetterValue, 2);
				strBuilder.append(getSplitStrByStrArray(capitalLetterArr, BlankSplitStr));
			}
		}
		if (keyLength == KEY_16_BIT) {
			if (isExistSpecialCharacter) {
				// 随机取出7个数字
				String[] digitArr = getRandomArray(DigitValue, 7);
				strBuilder.append(getSplitStrByStrArray(digitArr, BlankSplitStr));
				// 随机取出4个小写字母
				String[] lowLetterArr = getRandomArray(LowerLetterValue, 4);
				strBuilder.append(getSplitStrByStrArray(lowLetterArr, BlankSplitStr));
				// 随机取出3个大写字母
				String[] capitalLetterArr = getRandomArray(CapitalLetterValue, 3);
				strBuilder.append(getSplitStrByStrArray(capitalLetterArr, BlankSplitStr));
				// 随机取出2个特殊字符
				String[] specialLetterArr = getRandomArray(SpecialCharacter, 2);
				strBuilder.append(getSplitStrByStrArray(specialLetterArr, BlankSplitStr));
			} else {
				// 随机取出6个数字
				String[] digitArr = getRandomArray(DigitValue, 6);
				strBuilder.append(getSplitStrByStrArray(digitArr, BlankSplitStr));
				// 随机取出6个小写字母
				String[] lowLetterArr = getRandomArray(LowerLetterValue, 6);
				strBuilder.append(getSplitStrByStrArray(lowLetterArr, BlankSplitStr));
				// 随机取出4个大写字母
				String[] capitalLetterArr = getRandomArray(CapitalLetterValue, 4);
				strBuilder.append(getSplitStrByStrArray(capitalLetterArr, BlankSplitStr));
			}
		}
		if (keyLength == KEY_32_BIT) {
			if (isExistSpecialCharacter) {
				// 随机取出7个数字
				String[] digitArr = getRandomArray(DigitValue, 7);
				strBuilder.append(getSplitStrByStrArray(digitArr, BlankSplitStr));
				// 随机取出15个小写字母
				String[] lowLetterArr = getRandomArray(LowerLetterValue, 15);
				strBuilder.append(getSplitStrByStrArray(lowLetterArr, BlankSplitStr));
				// 随机取出7个大写字母
				String[] capitalLetterArr = getRandomArray(CapitalLetterValue, 7);
				strBuilder.append(getSplitStrByStrArray(capitalLetterArr, BlankSplitStr));
				// 随机取出3个特殊字符
				String[] specialLetterArr = getRandomArray(SpecialCharacter, 3);
				strBuilder.append(getSplitStrByStrArray(specialLetterArr, BlankSplitStr));
			} else {
				// 随机取出7个数字
				String[] digitArr = getRandomArray(DigitValue, 7);
				strBuilder.append(getSplitStrByStrArray(digitArr, BlankSplitStr));
				// 随机取出18个小写字母
				String[] lowLetterArr = getRandomArray(LowerLetterValue, 18);
				strBuilder.append(getSplitStrByStrArray(lowLetterArr, BlankSplitStr));
				// 随机取出7个大写字母
				String[] capitalLetterArr = getRandomArray(CapitalLetterValue, 7);
				strBuilder.append(getSplitStrByStrArray(capitalLetterArr, BlankSplitStr));
			}
		}
		String mixDigtalLetterCharStrs = strBuilder.toString();
		if (mixDigtalLetterCharStrs != null && mixDigtalLetterCharStrs.length() > 0) {
			String[] mixDigtalLetterCharArr = mixDigtalLetterCharStrs.split(BlankSplitStr);
			String[] disorderArr = getDisorderArr(mixDigtalLetterCharArr); // 打乱顺序
			return StringUtils.join(disorderArr);
		} else {
			return null;
		}
	}

	/**
	 * @Description: 从字符串数组中随机获取固定长度随机元素组成子串
	 * <AUTHOR>
	 * @date 2019年4月25日
	 */
	public static String[] getRepetitionRandomArray(String[] paramArray, int count) {
		String[] newArray = new String[count];
		Random random = new Random();
		int temp = 0; // 接收产生的随机数
		List<Integer> list = new ArrayList<Integer>(); // 存放随机数
		for (int i = 1; i <= count; i++) {
			temp = random.nextInt(paramArray.length); // 将产生的随机数作为被抽数组的索引
			newArray[i - 1] = paramArray[temp];
			list.add(temp);
		}
		return newArray;
	}

	/**
	 * @Description: 从字符串数组中随机获取固定长度随机不重复元素组成子串
	 * <AUTHOR>
	 * @date 2018年10月29日
	 */
	public static String[] getRandomArray(String[] paramArray, int count) {
		if (paramArray.length < count) {
			return paramArray;
		}
		String[] newArray = new String[count];
		Random random = new Random();
		int temp = 0; // 接收产生的随机数
		List<Integer> list = new ArrayList<Integer>(); // 存放随机数
		for (int i = 1; i <= count; i++) {
			temp = random.nextInt(paramArray.length); // 将产生的随机数作为被抽数组的索引
			if (!(list.contains(temp))) {
				newArray[i - 1] = paramArray[temp];
				list.add(temp);
			} else {
				i--;
			}
		}
		return newArray;
	}

	/**
	 * @Description: 利用随机换位打乱字符数组元素
	 * <AUTHOR>
	 * @date 2018年10月29日
	 */
	public static String[] getDisorderArr(String[] strArrs) {
		Random r = new Random();
		for (int i = strArrs.length - 1; i >= 1; i--) {
			int j = r.nextInt(i); // 取出当前遍历数范围的随机数
			// 随机换位
			String str = strArrs[i];
			strArrs[i] = strArrs[j];
			strArrs[j] = str;
		}
		return strArrs;
	}

	/**
	 * @Description: 将字符串数组转为带分隔符的字符串
	 * <AUTHOR>
	 * @date 2018年10月29日
	 */
	public static String getSplitStrByStrArray(String[] strArray, String splitStr) {
		StringBuilder stringBuilder = new StringBuilder();
		if (strArray != null && strArray.length >= 2 && splitStr != null && splitStr.length() == 1) {
			for (int i = 0; i < strArray.length - 1; i++) {
				stringBuilder.append(strArray[i]);
				stringBuilder.append(splitStr);
			}
			stringBuilder.append(strArray[strArray.length - 1]);

		}
		return stringBuilder.toString();
	}

}
