package com.wzsec.modules.system.service.mapper;

import com.wzsec.modules.system.domain.Dept;
import com.wzsec.modules.system.service.dto.DeptDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:34+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class DeptMapperImpl implements DeptMapper {

    @Override
    public DeptDto toDto(Dept entity) {
        if ( entity == null ) {
            return null;
        }

        DeptDto deptDto = new DeptDto();

        deptDto.setCreateTime( entity.getCreateTime() );
        if ( entity.getEnabled() != null ) {
            deptDto.setEnabled( Boolean.parseBoolean( entity.getEnabled() ) );
        }
        deptDto.setId( entity.getId() );
        deptDto.setName( entity.getName() );
        deptDto.setPid( entity.getPid() );

        return deptDto;
    }

    @Override
    public List<DeptDto> toDto(List<Dept> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<DeptDto> list = new ArrayList<DeptDto>( entityList.size() );
        for ( Dept dept : entityList ) {
            list.add( toDto( dept ) );
        }

        return list;
    }

    @Override
    public Dept toEntity(DeptDto dto) {
        if ( dto == null ) {
            return null;
        }

        Dept dept = new Dept();

        dept.setCreateTime( dto.getCreateTime() );
        if ( dto.getEnabled() != null ) {
            dept.setEnabled( String.valueOf( dto.getEnabled() ) );
        }
        dept.setId( dto.getId() );
        dept.setName( dto.getName() );
        dept.setPid( dto.getPid() );

        return dept;
    }

    @Override
    public List<Dept> toEntity(List<DeptDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Dept> list = new ArrayList<Dept>( dtoList.size() );
        for ( DeptDto deptDto : dtoList ) {
            list.add( toEntity( deptDto ) );
        }

        return list;
    }
}
