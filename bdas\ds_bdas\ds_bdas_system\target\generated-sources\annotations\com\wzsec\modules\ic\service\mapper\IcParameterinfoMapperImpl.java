package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcParameterinfo;
import com.wzsec.modules.ic.service.dto.IcParameterinfoDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:31+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class IcParameterinfoMapperImpl implements IcParameterinfoMapper {

    @Override
    public IcParameterinfoDto toDto(IcParameterinfo entity) {
        if ( entity == null ) {
            return null;
        }

        IcParameterinfoDto icParameterinfoDto = new IcParameterinfoDto();

        icParameterinfoDto.setApicode( entity.getApicode() );
        icParameterinfoDto.setApiname( entity.getApiname() );
        icParameterinfoDto.setChildren( entity.getChildren() );
        icParameterinfoDto.setClassification( entity.getClassification() );
        icParameterinfoDto.setCreatetime( entity.getCreatetime() );
        icParameterinfoDto.setCreateuser( entity.getCreateuser() );
        icParameterinfoDto.setDatacatalogname( entity.getDatacatalogname() );
        icParameterinfoDto.setDataid( entity.getDataid() );
        icParameterinfoDto.setDes( entity.getDes() );
        icParameterinfoDto.setId( entity.getId() );
        icParameterinfoDto.setInterfaceid( entity.getInterfaceid() );
        icParameterinfoDto.setName( entity.getName() );
        icParameterinfoDto.setNamecn( entity.getNamecn() );
        icParameterinfoDto.setNameen( entity.getNameen() );
        icParameterinfoDto.setOpen( entity.getOpen() );
        icParameterinfoDto.setSharetype( entity.getSharetype() );
        icParameterinfoDto.setSparefield1( entity.getSparefield1() );
        icParameterinfoDto.setSparefield2( entity.getSparefield2() );
        icParameterinfoDto.setSparefield3( entity.getSparefield3() );
        icParameterinfoDto.setSparefield4( entity.getSparefield4() );
        icParameterinfoDto.setSparefield5( entity.getSparefield5() );
        icParameterinfoDto.setUpdatetime( entity.getUpdatetime() );
        icParameterinfoDto.setUpdateuser( entity.getUpdateuser() );

        return icParameterinfoDto;
    }

    @Override
    public List<IcParameterinfoDto> toDto(List<IcParameterinfo> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcParameterinfoDto> list = new ArrayList<IcParameterinfoDto>( entityList.size() );
        for ( IcParameterinfo icParameterinfo : entityList ) {
            list.add( toDto( icParameterinfo ) );
        }

        return list;
    }

    @Override
    public IcParameterinfo toEntity(IcParameterinfoDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcParameterinfo icParameterinfo = new IcParameterinfo();

        icParameterinfo.setApicode( dto.getApicode() );
        icParameterinfo.setApiname( dto.getApiname() );
        icParameterinfo.setChildren( dto.getChildren() );
        icParameterinfo.setClassification( dto.getClassification() );
        icParameterinfo.setCreatetime( dto.getCreatetime() );
        icParameterinfo.setCreateuser( dto.getCreateuser() );
        icParameterinfo.setDatacatalogname( dto.getDatacatalogname() );
        icParameterinfo.setDataid( dto.getDataid() );
        icParameterinfo.setDes( dto.getDes() );
        icParameterinfo.setId( dto.getId() );
        icParameterinfo.setInterfaceid( dto.getInterfaceid() );
        icParameterinfo.setName( dto.getName() );
        icParameterinfo.setNamecn( dto.getNamecn() );
        icParameterinfo.setNameen( dto.getNameen() );
        icParameterinfo.setOpen( dto.getOpen() );
        icParameterinfo.setSharetype( dto.getSharetype() );
        icParameterinfo.setSparefield1( dto.getSparefield1() );
        icParameterinfo.setSparefield2( dto.getSparefield2() );
        icParameterinfo.setSparefield3( dto.getSparefield3() );
        icParameterinfo.setSparefield4( dto.getSparefield4() );
        icParameterinfo.setSparefield5( dto.getSparefield5() );
        icParameterinfo.setUpdatetime( dto.getUpdatetime() );
        icParameterinfo.setUpdateuser( dto.getUpdateuser() );

        return icParameterinfo;
    }

    @Override
    public List<IcParameterinfo> toEntity(List<IcParameterinfoDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcParameterinfo> list = new ArrayList<IcParameterinfo>( dtoList.size() );
        for ( IcParameterinfoDto icParameterinfoDto : dtoList ) {
            list.add( toEntity( icParameterinfoDto ) );
        }

        return list;
    }
}
