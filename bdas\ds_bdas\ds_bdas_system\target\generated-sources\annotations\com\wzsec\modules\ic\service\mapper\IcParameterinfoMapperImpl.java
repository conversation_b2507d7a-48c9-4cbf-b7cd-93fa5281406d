package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcParameterinfo;
import com.wzsec.modules.ic.service.dto.IcParameterinfoDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:30+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class IcParameterinfoMapperImpl implements IcParameterinfoMapper {

    @Override
    public IcParameterinfo toEntity(IcParameterinfoDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcParameterinfo icParameterinfo = new IcParameterinfo();

        icParameterinfo.setId( dto.getId() );
        icParameterinfo.setInterfaceid( dto.getInterfaceid() );
        icParameterinfo.setApicode( dto.getApicode() );
        icParameterinfo.setApiname( dto.getApiname() );
        icParameterinfo.setDataid( dto.getDataid() );
        icParameterinfo.setNamecn( dto.getNamecn() );
        icParameterinfo.setNameen( dto.getNameen() );
        icParameterinfo.setClassification( dto.getClassification() );
        icParameterinfo.setSharetype( dto.getSharetype() );
        icParameterinfo.setOpen( dto.getOpen() );
        icParameterinfo.setDatacatalogname( dto.getDatacatalogname() );
        icParameterinfo.setName( dto.getName() );
        icParameterinfo.setDes( dto.getDes() );
        icParameterinfo.setChildren( dto.getChildren() );
        icParameterinfo.setCreateuser( dto.getCreateuser() );
        icParameterinfo.setCreatetime( dto.getCreatetime() );
        icParameterinfo.setUpdateuser( dto.getUpdateuser() );
        icParameterinfo.setUpdatetime( dto.getUpdatetime() );
        icParameterinfo.setSparefield1( dto.getSparefield1() );
        icParameterinfo.setSparefield2( dto.getSparefield2() );
        icParameterinfo.setSparefield3( dto.getSparefield3() );
        icParameterinfo.setSparefield4( dto.getSparefield4() );
        icParameterinfo.setSparefield5( dto.getSparefield5() );

        return icParameterinfo;
    }

    @Override
    public IcParameterinfoDto toDto(IcParameterinfo entity) {
        if ( entity == null ) {
            return null;
        }

        IcParameterinfoDto icParameterinfoDto = new IcParameterinfoDto();

        icParameterinfoDto.setId( entity.getId() );
        icParameterinfoDto.setInterfaceid( entity.getInterfaceid() );
        icParameterinfoDto.setApicode( entity.getApicode() );
        icParameterinfoDto.setApiname( entity.getApiname() );
        icParameterinfoDto.setDataid( entity.getDataid() );
        icParameterinfoDto.setNamecn( entity.getNamecn() );
        icParameterinfoDto.setNameen( entity.getNameen() );
        icParameterinfoDto.setClassification( entity.getClassification() );
        icParameterinfoDto.setSharetype( entity.getSharetype() );
        icParameterinfoDto.setOpen( entity.getOpen() );
        icParameterinfoDto.setDatacatalogname( entity.getDatacatalogname() );
        icParameterinfoDto.setName( entity.getName() );
        icParameterinfoDto.setDes( entity.getDes() );
        icParameterinfoDto.setChildren( entity.getChildren() );
        icParameterinfoDto.setCreateuser( entity.getCreateuser() );
        icParameterinfoDto.setCreatetime( entity.getCreatetime() );
        icParameterinfoDto.setUpdateuser( entity.getUpdateuser() );
        icParameterinfoDto.setUpdatetime( entity.getUpdatetime() );
        icParameterinfoDto.setSparefield1( entity.getSparefield1() );
        icParameterinfoDto.setSparefield2( entity.getSparefield2() );
        icParameterinfoDto.setSparefield3( entity.getSparefield3() );
        icParameterinfoDto.setSparefield4( entity.getSparefield4() );
        icParameterinfoDto.setSparefield5( entity.getSparefield5() );

        return icParameterinfoDto;
    }

    @Override
    public List<IcParameterinfo> toEntity(List<IcParameterinfoDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcParameterinfo> list = new ArrayList<IcParameterinfo>( dtoList.size() );
        for ( IcParameterinfoDto icParameterinfoDto : dtoList ) {
            list.add( toEntity( icParameterinfoDto ) );
        }

        return list;
    }

    @Override
    public List<IcParameterinfoDto> toDto(List<IcParameterinfo> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcParameterinfoDto> list = new ArrayList<IcParameterinfoDto>( entityList.size() );
        for ( IcParameterinfo icParameterinfo : entityList ) {
            list.add( toDto( icParameterinfo ) );
        }

        return list;
    }
}
