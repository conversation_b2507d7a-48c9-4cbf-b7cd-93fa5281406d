package com.wzsec.modules.ic.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.modules.ic.domain.Outputparamresult;
import com.wzsec.modules.ic.service.OutputparamresultService;
import com.wzsec.modules.ic.service.dto.OutputparamresultQueryCriteria;
import com.wzsec.utils.DomainUtil;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
// import io.swagger.annotations.*;
import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
* <AUTHOR>
* @date 2024-04-07
*/
// @Api(tags = "接口出参检测管理")
@RestController
@RequestMapping("/api/outputparamresult")
public class OutputparamresultController {

    private final OutputparamresultService outputparamresultService;

    public OutputparamresultController(OutputparamresultService outputparamresultService) {
        this.outputparamresultService = outputparamresultService;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, OutputparamresultQueryCriteria criteria) throws IOException {

        // 根据申请部门名称分域
        List<String> domainApiCodeList = DomainUtil.queryDomainApiCode();
        criteria.setApicode(domainApiCodeList);

        outputparamresultService.download(outputparamresultService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询接口出参检测")
    // @ApiOperation("查询接口出参检测")
    public ResponseEntity<Object> getOutputparamresults(OutputparamresultQueryCriteria criteria, Pageable pageable) {

        // 根据申请部门名称分域
        List<String> domainApiCodeList = DomainUtil.queryDomainApiCode();
        criteria.setApicode(domainApiCodeList);

        return new ResponseEntity<>(outputparamresultService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增接口出参检测")
    // @ApiOperation("新增接口出参检测")
    public ResponseEntity<Object> create(@Validated @RequestBody Outputparamresult resources){
        return new ResponseEntity<>(outputparamresultService.create(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改接口出参检测")
    // @ApiOperation("修改接口出参检测")
    public ResponseEntity<Object> update(@Validated @RequestBody Outputparamresult resources){
        outputparamresultService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除接口出参检测")
    // @ApiOperation("删除接口出参检测")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        outputparamresultService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
