package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.ApiUsercall;
import com.wzsec.modules.ic.service.dto.ApiUsercallDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:26:46+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ApiUsercallMapperImpl implements ApiUsercallMapper {

    @Override
    public ApiUsercallDto toDto(ApiUsercall entity) {
        if ( entity == null ) {
            return null;
        }

        ApiUsercallDto apiUsercallDto = new ApiUsercallDto();

        apiUsercallDto.setAccount( entity.getAccount() );
        apiUsercallDto.setAk( entity.getAk() );
        apiUsercallDto.setAkapicode( entity.getAkapicode() );
        apiUsercallDto.setApiname( entity.getApiname() );
        apiUsercallDto.setApiurl( entity.getApiurl() );
        apiUsercallDto.setApplyorgname( entity.getApplyorgname() );
        apiUsercallDto.setAvgnum( entity.getAvgnum() );
        apiUsercallDto.setCallcount( entity.getCallcount() );
        apiUsercallDto.setCalldate( entity.getCalldate() );
        apiUsercallDto.setChecktime( entity.getChecktime() );
        apiUsercallDto.setClientip( entity.getClientip() );
        apiUsercallDto.setId( entity.getId() );
        apiUsercallDto.setRisk( entity.getRisk() );
        apiUsercallDto.setSparefield1( entity.getSparefield1() );
        apiUsercallDto.setSparefield2( entity.getSparefield2() );
        apiUsercallDto.setSparefield3( entity.getSparefield3() );
        apiUsercallDto.setSparefield4( entity.getSparefield4() );
        apiUsercallDto.setSystemname( entity.getSystemname() );

        return apiUsercallDto;
    }

    @Override
    public List<ApiUsercallDto> toDto(List<ApiUsercall> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiUsercallDto> list = new ArrayList<ApiUsercallDto>( entityList.size() );
        for ( ApiUsercall apiUsercall : entityList ) {
            list.add( toDto( apiUsercall ) );
        }

        return list;
    }

    @Override
    public ApiUsercall toEntity(ApiUsercallDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiUsercall apiUsercall = new ApiUsercall();

        apiUsercall.setAccount( dto.getAccount() );
        apiUsercall.setAk( dto.getAk() );
        apiUsercall.setAkapicode( dto.getAkapicode() );
        apiUsercall.setApiname( dto.getApiname() );
        apiUsercall.setApiurl( dto.getApiurl() );
        apiUsercall.setApplyorgname( dto.getApplyorgname() );
        apiUsercall.setAvgnum( dto.getAvgnum() );
        apiUsercall.setCallcount( dto.getCallcount() );
        apiUsercall.setCalldate( dto.getCalldate() );
        apiUsercall.setChecktime( dto.getChecktime() );
        apiUsercall.setClientip( dto.getClientip() );
        apiUsercall.setId( dto.getId() );
        apiUsercall.setRisk( dto.getRisk() );
        apiUsercall.setSparefield1( dto.getSparefield1() );
        apiUsercall.setSparefield2( dto.getSparefield2() );
        apiUsercall.setSparefield3( dto.getSparefield3() );
        apiUsercall.setSparefield4( dto.getSparefield4() );
        apiUsercall.setSystemname( dto.getSystemname() );

        return apiUsercall;
    }

    @Override
    public List<ApiUsercall> toEntity(List<ApiUsercallDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiUsercall> list = new ArrayList<ApiUsercall>( dtoList.size() );
        for ( ApiUsercallDto apiUsercallDto : dtoList ) {
            list.add( toEntity( apiUsercallDto ) );
        }

        return list;
    }
}
