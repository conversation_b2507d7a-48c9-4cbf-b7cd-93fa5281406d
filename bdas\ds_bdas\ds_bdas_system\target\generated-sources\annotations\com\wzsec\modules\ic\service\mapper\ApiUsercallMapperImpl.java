package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.ApiUsercall;
import com.wzsec.modules.ic.service.dto.ApiUsercallDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:30+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ApiUsercallMapperImpl implements ApiUsercallMapper {

    @Override
    public ApiUsercall toEntity(ApiUsercallDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiUsercall apiUsercall = new ApiUsercall();

        apiUsercall.setId( dto.getId() );
        apiUsercall.setClientip( dto.getClientip() );
        apiUsercall.setCallcount( dto.getCallcount() );
        apiUsercall.setCalldate( dto.getCalldate() );
        apiUsercall.setRisk( dto.getRisk() );
        apiUsercall.setChecktime( dto.getChecktime() );
        apiUsercall.setAccount( dto.getAccount() );
        apiUsercall.setAvgnum( dto.getAvgnum() );
        apiUsercall.setSparefield1( dto.getSparefield1() );
        apiUsercall.setSparefield2( dto.getSparefield2() );
        apiUsercall.setSparefield3( dto.getSparefield3() );
        apiUsercall.setSparefield4( dto.getSparefield4() );
        apiUsercall.setAk( dto.getAk() );
        apiUsercall.setApplyorgname( dto.getApplyorgname() );
        apiUsercall.setApiname( dto.getApiname() );
        apiUsercall.setApiurl( dto.getApiurl() );
        apiUsercall.setAkapicode( dto.getAkapicode() );
        apiUsercall.setSystemname( dto.getSystemname() );

        return apiUsercall;
    }

    @Override
    public ApiUsercallDto toDto(ApiUsercall entity) {
        if ( entity == null ) {
            return null;
        }

        ApiUsercallDto apiUsercallDto = new ApiUsercallDto();

        apiUsercallDto.setId( entity.getId() );
        apiUsercallDto.setClientip( entity.getClientip() );
        apiUsercallDto.setCallcount( entity.getCallcount() );
        apiUsercallDto.setCalldate( entity.getCalldate() );
        apiUsercallDto.setRisk( entity.getRisk() );
        apiUsercallDto.setChecktime( entity.getChecktime() );
        apiUsercallDto.setAccount( entity.getAccount() );
        apiUsercallDto.setAvgnum( entity.getAvgnum() );
        apiUsercallDto.setSparefield1( entity.getSparefield1() );
        apiUsercallDto.setSparefield2( entity.getSparefield2() );
        apiUsercallDto.setSparefield3( entity.getSparefield3() );
        apiUsercallDto.setSparefield4( entity.getSparefield4() );
        apiUsercallDto.setAk( entity.getAk() );
        apiUsercallDto.setApplyorgname( entity.getApplyorgname() );
        apiUsercallDto.setApiname( entity.getApiname() );
        apiUsercallDto.setApiurl( entity.getApiurl() );
        apiUsercallDto.setAkapicode( entity.getAkapicode() );
        apiUsercallDto.setSystemname( entity.getSystemname() );

        return apiUsercallDto;
    }

    @Override
    public List<ApiUsercall> toEntity(List<ApiUsercallDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiUsercall> list = new ArrayList<ApiUsercall>( dtoList.size() );
        for ( ApiUsercallDto apiUsercallDto : dtoList ) {
            list.add( toEntity( apiUsercallDto ) );
        }

        return list;
    }

    @Override
    public List<ApiUsercallDto> toDto(List<ApiUsercall> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiUsercallDto> list = new ArrayList<ApiUsercallDto>( entityList.size() );
        for ( ApiUsercall apiUsercall : entityList ) {
            list.add( toDto( apiUsercall ) );
        }

        return list;
    }
}
