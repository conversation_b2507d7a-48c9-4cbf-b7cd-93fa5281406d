package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.ApiUnexpectedcontentresult;
import com.wzsec.modules.ic.service.dto.ApiUnexpectedcontentresultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:27:07+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ApiUnexpectedcontentresultMapperImpl implements ApiUnexpectedcontentresultMapper {

    @Override
    public ApiUnexpectedcontentresultDto toDto(ApiUnexpectedcontentresult entity) {
        if ( entity == null ) {
            return null;
        }

        ApiUnexpectedcontentresultDto apiUnexpectedcontentresultDto = new ApiUnexpectedcontentresultDto();

        apiUnexpectedcontentresultDto.setApicode( entity.getApicode() );
        apiUnexpectedcontentresultDto.setCalltime( entity.getCalltime() );
        apiUnexpectedcontentresultDto.setCheckobject( entity.getCheckobject() );
        apiUnexpectedcontentresultDto.setChecktime( entity.getChecktime() );
        apiUnexpectedcontentresultDto.setId( entity.getId() );
        apiUnexpectedcontentresultDto.setRisk( entity.getRisk() );
        apiUnexpectedcontentresultDto.setSparefield1( entity.getSparefield1() );
        apiUnexpectedcontentresultDto.setSparefield2( entity.getSparefield2() );
        apiUnexpectedcontentresultDto.setSparefield3( entity.getSparefield3() );
        apiUnexpectedcontentresultDto.setSparefield4( entity.getSparefield4() );
        apiUnexpectedcontentresultDto.setUnexpectedcontent( entity.getUnexpectedcontent() );

        return apiUnexpectedcontentresultDto;
    }

    @Override
    public List<ApiUnexpectedcontentresultDto> toDto(List<ApiUnexpectedcontentresult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiUnexpectedcontentresultDto> list = new ArrayList<ApiUnexpectedcontentresultDto>( entityList.size() );
        for ( ApiUnexpectedcontentresult apiUnexpectedcontentresult : entityList ) {
            list.add( toDto( apiUnexpectedcontentresult ) );
        }

        return list;
    }

    @Override
    public ApiUnexpectedcontentresult toEntity(ApiUnexpectedcontentresultDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiUnexpectedcontentresult apiUnexpectedcontentresult = new ApiUnexpectedcontentresult();

        apiUnexpectedcontentresult.setApicode( dto.getApicode() );
        apiUnexpectedcontentresult.setCalltime( dto.getCalltime() );
        apiUnexpectedcontentresult.setCheckobject( dto.getCheckobject() );
        apiUnexpectedcontentresult.setChecktime( dto.getChecktime() );
        apiUnexpectedcontentresult.setId( dto.getId() );
        apiUnexpectedcontentresult.setRisk( dto.getRisk() );
        apiUnexpectedcontentresult.setSparefield1( dto.getSparefield1() );
        apiUnexpectedcontentresult.setSparefield2( dto.getSparefield2() );
        apiUnexpectedcontentresult.setSparefield3( dto.getSparefield3() );
        apiUnexpectedcontentresult.setSparefield4( dto.getSparefield4() );
        apiUnexpectedcontentresult.setUnexpectedcontent( dto.getUnexpectedcontent() );

        return apiUnexpectedcontentresult;
    }

    @Override
    public List<ApiUnexpectedcontentresult> toEntity(List<ApiUnexpectedcontentresultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiUnexpectedcontentresult> list = new ArrayList<ApiUnexpectedcontentresult>( dtoList.size() );
        for ( ApiUnexpectedcontentresultDto apiUnexpectedcontentresultDto : dtoList ) {
            list.add( toEntity( apiUnexpectedcontentresultDto ) );
        }

        return list;
    }
}
