package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcCallerinfo;
import com.wzsec.modules.ic.service.dto.IcCallerinfoDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:27:08+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class IcCallerinfoMapperImpl implements IcCallerinfoMapper {

    @Override
    public IcCallerinfoDto toDto(IcCallerinfo entity) {
        if ( entity == null ) {
            return null;
        }

        IcCallerinfoDto icCallerinfoDto = new IcCallerinfoDto();

        icCallerinfoDto.setAk( entity.getAk() );
        icCallerinfoDto.setCallerdepartment( entity.getCallerdepartment() );
        icCallerinfoDto.setCallerip( entity.getCallerip() );
        icCallerinfoDto.setCallerlinkman( entity.getCallerlinkman() );
        icCallerinfoDto.setCallerlinktel( entity.getCallerlinktel() );
        icCallerinfoDto.setCallername( entity.getCallername() );
        icCallerinfoDto.setCallersysname( entity.getCallersysname() );
        icCallerinfoDto.setCreatetime( entity.getCreatetime() );
        icCallerinfoDto.setCreateuser( entity.getCreateuser() );
        icCallerinfoDto.setId( entity.getId() );
        icCallerinfoDto.setSparefield1( entity.getSparefield1() );
        icCallerinfoDto.setSparefield2( entity.getSparefield2() );
        icCallerinfoDto.setSparefield3( entity.getSparefield3() );
        icCallerinfoDto.setSparefield4( entity.getSparefield4() );
        icCallerinfoDto.setSparefield5( entity.getSparefield5() );
        icCallerinfoDto.setUpdatetime( entity.getUpdatetime() );
        icCallerinfoDto.setUpdateuser( entity.getUpdateuser() );

        return icCallerinfoDto;
    }

    @Override
    public List<IcCallerinfoDto> toDto(List<IcCallerinfo> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcCallerinfoDto> list = new ArrayList<IcCallerinfoDto>( entityList.size() );
        for ( IcCallerinfo icCallerinfo : entityList ) {
            list.add( toDto( icCallerinfo ) );
        }

        return list;
    }

    @Override
    public IcCallerinfo toEntity(IcCallerinfoDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcCallerinfo icCallerinfo = new IcCallerinfo();

        icCallerinfo.setAk( dto.getAk() );
        icCallerinfo.setCallerdepartment( dto.getCallerdepartment() );
        icCallerinfo.setCallerip( dto.getCallerip() );
        icCallerinfo.setCallerlinkman( dto.getCallerlinkman() );
        icCallerinfo.setCallerlinktel( dto.getCallerlinktel() );
        icCallerinfo.setCallername( dto.getCallername() );
        icCallerinfo.setCallersysname( dto.getCallersysname() );
        icCallerinfo.setCreatetime( dto.getCreatetime() );
        icCallerinfo.setCreateuser( dto.getCreateuser() );
        icCallerinfo.setId( dto.getId() );
        icCallerinfo.setSparefield1( dto.getSparefield1() );
        icCallerinfo.setSparefield2( dto.getSparefield2() );
        icCallerinfo.setSparefield3( dto.getSparefield3() );
        icCallerinfo.setSparefield4( dto.getSparefield4() );
        icCallerinfo.setSparefield5( dto.getSparefield5() );
        icCallerinfo.setUpdatetime( dto.getUpdatetime() );
        icCallerinfo.setUpdateuser( dto.getUpdateuser() );

        return icCallerinfo;
    }

    @Override
    public List<IcCallerinfo> toEntity(List<IcCallerinfoDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcCallerinfo> list = new ArrayList<IcCallerinfo>( dtoList.size() );
        for ( IcCallerinfoDto icCallerinfoDto : dtoList ) {
            list.add( toEntity( icCallerinfoDto ) );
        }

        return list;
    }
}
