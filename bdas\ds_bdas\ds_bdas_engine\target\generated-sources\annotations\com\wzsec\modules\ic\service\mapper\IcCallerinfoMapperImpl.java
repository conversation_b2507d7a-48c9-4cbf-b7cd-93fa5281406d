package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcCallerinfo;
import com.wzsec.modules.ic.service.dto.IcCallerinfoDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:01+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class IcCallerinfoMapperImpl implements IcCallerinfoMapper {

    @Override
    public IcCallerinfo toEntity(IcCallerinfoDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcCallerinfo icCallerinfo = new IcCallerinfo();

        icCallerinfo.setId( dto.getId() );
        icCallerinfo.setAk( dto.getAk() );
        icCallerinfo.setCallerip( dto.getCallerip() );
        icCallerinfo.setCallername( dto.getCallername() );
        icCallerinfo.setCallersysname( dto.getCallersysname() );
        icCallerinfo.setCallerdepartment( dto.getCallerdepartment() );
        icCallerinfo.setCallerlinkman( dto.getCallerlinkman() );
        icCallerinfo.setCallerlinktel( dto.getCallerlinktel() );
        icCallerinfo.setCreateuser( dto.getCreateuser() );
        icCallerinfo.setCreatetime( dto.getCreatetime() );
        icCallerinfo.setUpdateuser( dto.getUpdateuser() );
        icCallerinfo.setUpdatetime( dto.getUpdatetime() );
        icCallerinfo.setSparefield1( dto.getSparefield1() );
        icCallerinfo.setSparefield2( dto.getSparefield2() );
        icCallerinfo.setSparefield3( dto.getSparefield3() );
        icCallerinfo.setSparefield4( dto.getSparefield4() );
        icCallerinfo.setSparefield5( dto.getSparefield5() );

        return icCallerinfo;
    }

    @Override
    public IcCallerinfoDto toDto(IcCallerinfo entity) {
        if ( entity == null ) {
            return null;
        }

        IcCallerinfoDto icCallerinfoDto = new IcCallerinfoDto();

        icCallerinfoDto.setId( entity.getId() );
        icCallerinfoDto.setAk( entity.getAk() );
        icCallerinfoDto.setCallerip( entity.getCallerip() );
        icCallerinfoDto.setCallername( entity.getCallername() );
        icCallerinfoDto.setCallersysname( entity.getCallersysname() );
        icCallerinfoDto.setCallerdepartment( entity.getCallerdepartment() );
        icCallerinfoDto.setCallerlinkman( entity.getCallerlinkman() );
        icCallerinfoDto.setCallerlinktel( entity.getCallerlinktel() );
        icCallerinfoDto.setCreateuser( entity.getCreateuser() );
        icCallerinfoDto.setCreatetime( entity.getCreatetime() );
        icCallerinfoDto.setUpdateuser( entity.getUpdateuser() );
        icCallerinfoDto.setUpdatetime( entity.getUpdatetime() );
        icCallerinfoDto.setSparefield1( entity.getSparefield1() );
        icCallerinfoDto.setSparefield2( entity.getSparefield2() );
        icCallerinfoDto.setSparefield3( entity.getSparefield3() );
        icCallerinfoDto.setSparefield4( entity.getSparefield4() );
        icCallerinfoDto.setSparefield5( entity.getSparefield5() );

        return icCallerinfoDto;
    }

    @Override
    public List<IcCallerinfo> toEntity(List<IcCallerinfoDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcCallerinfo> list = new ArrayList<IcCallerinfo>( dtoList.size() );
        for ( IcCallerinfoDto icCallerinfoDto : dtoList ) {
            list.add( toEntity( icCallerinfoDto ) );
        }

        return list;
    }

    @Override
    public List<IcCallerinfoDto> toDto(List<IcCallerinfo> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcCallerinfoDto> list = new ArrayList<IcCallerinfoDto>( entityList.size() );
        for ( IcCallerinfo icCallerinfo : entityList ) {
            list.add( toDto( icCallerinfo ) );
        }

        return list;
    }
}
