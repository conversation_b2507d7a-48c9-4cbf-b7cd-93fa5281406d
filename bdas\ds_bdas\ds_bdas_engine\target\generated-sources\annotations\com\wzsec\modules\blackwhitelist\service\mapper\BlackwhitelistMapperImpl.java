package com.wzsec.modules.blackwhitelist.service.mapper;

import com.wzsec.modules.blackwhitelist.domain.Blackwhitelist;
import com.wzsec.modules.blackwhitelist.service.dto.BlackwhitelistDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:27:06+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class BlackwhitelistMapperImpl implements BlackwhitelistMapper {

    @Override
    public BlackwhitelistDto toDto(Blackwhitelist entity) {
        if ( entity == null ) {
            return null;
        }

        BlackwhitelistDto blackwhitelistDto = new BlackwhitelistDto();

        blackwhitelistDto.setApply( entity.getApply() );
        blackwhitelistDto.setContent( entity.getContent() );
        blackwhitelistDto.setCreateTime( entity.getCreateTime() );
        blackwhitelistDto.setCreateuser( entity.getCreateuser() );
        blackwhitelistDto.setId( entity.getId() );
        blackwhitelistDto.setNote( entity.getNote() );
        blackwhitelistDto.setPurpose( entity.getPurpose() );
        blackwhitelistDto.setSparefield1( entity.getSparefield1() );
        blackwhitelistDto.setSparefield2( entity.getSparefield2() );
        blackwhitelistDto.setSparefield3( entity.getSparefield3() );
        blackwhitelistDto.setSparefield4( entity.getSparefield4() );
        blackwhitelistDto.setSparefield5( entity.getSparefield5() );
        blackwhitelistDto.setState( entity.getState() );
        blackwhitelistDto.setType( entity.getType() );
        blackwhitelistDto.setUpdateTime( entity.getUpdateTime() );
        blackwhitelistDto.setUpdateuser( entity.getUpdateuser() );

        return blackwhitelistDto;
    }

    @Override
    public List<BlackwhitelistDto> toDto(List<Blackwhitelist> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<BlackwhitelistDto> list = new ArrayList<BlackwhitelistDto>( entityList.size() );
        for ( Blackwhitelist blackwhitelist : entityList ) {
            list.add( toDto( blackwhitelist ) );
        }

        return list;
    }

    @Override
    public Blackwhitelist toEntity(BlackwhitelistDto dto) {
        if ( dto == null ) {
            return null;
        }

        Blackwhitelist blackwhitelist = new Blackwhitelist();

        blackwhitelist.setApply( dto.getApply() );
        blackwhitelist.setContent( dto.getContent() );
        blackwhitelist.setCreateTime( dto.getCreateTime() );
        blackwhitelist.setCreateuser( dto.getCreateuser() );
        blackwhitelist.setId( dto.getId() );
        blackwhitelist.setNote( dto.getNote() );
        blackwhitelist.setPurpose( dto.getPurpose() );
        blackwhitelist.setSparefield1( dto.getSparefield1() );
        blackwhitelist.setSparefield2( dto.getSparefield2() );
        blackwhitelist.setSparefield3( dto.getSparefield3() );
        blackwhitelist.setSparefield4( dto.getSparefield4() );
        blackwhitelist.setSparefield5( dto.getSparefield5() );
        blackwhitelist.setState( dto.getState() );
        blackwhitelist.setType( dto.getType() );
        blackwhitelist.setUpdateTime( dto.getUpdateTime() );
        blackwhitelist.setUpdateuser( dto.getUpdateuser() );

        return blackwhitelist;
    }

    @Override
    public List<Blackwhitelist> toEntity(List<BlackwhitelistDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Blackwhitelist> list = new ArrayList<Blackwhitelist>( dtoList.size() );
        for ( BlackwhitelistDto blackwhitelistDto : dtoList ) {
            list.add( toEntity( blackwhitelistDto ) );
        }

        return list;
    }
}
