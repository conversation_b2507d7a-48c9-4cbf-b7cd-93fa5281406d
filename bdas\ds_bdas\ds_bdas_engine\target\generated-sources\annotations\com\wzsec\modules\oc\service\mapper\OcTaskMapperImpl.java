package com.wzsec.modules.oc.service.mapper;

import com.wzsec.modules.oc.domain.OcTask;
import com.wzsec.modules.oc.service.dto.OcTaskDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:53+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class OcTaskMapperImpl implements OcTaskMapper {

    @Override
    public OcTaskDto toDto(OcTask entity) {
        if ( entity == null ) {
            return null;
        }

        OcTaskDto ocTaskDto = new OcTaskDto();

        ocTaskDto.setCreatetime( entity.getCreatetime() );
        ocTaskDto.setCreateuser( entity.getCreateuser() );
        ocTaskDto.setCron( entity.getCron() );
        ocTaskDto.setExecutionstate( entity.getExecutionstate() );
        ocTaskDto.setId( entity.getId() );
        ocTaskDto.setOperationrule( entity.getOperationrule() );
        ocTaskDto.setSourceid( entity.getSourceid() );
        ocTaskDto.setSparefield1( entity.getSparefield1() );
        ocTaskDto.setSparefield2( entity.getSparefield2() );
        ocTaskDto.setSparefield3( entity.getSparefield3() );
        ocTaskDto.setSparefield4( entity.getSparefield4() );
        ocTaskDto.setStatus( entity.getStatus() );
        ocTaskDto.setSubmitmethod( entity.getSubmitmethod() );
        ocTaskDto.setTaskname( entity.getTaskname() );
        ocTaskDto.setTasktype( entity.getTasktype() );
        ocTaskDto.setUpdatetime( entity.getUpdatetime() );
        ocTaskDto.setUpdateuser( entity.getUpdateuser() );

        return ocTaskDto;
    }

    @Override
    public List<OcTaskDto> toDto(List<OcTask> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<OcTaskDto> list = new ArrayList<OcTaskDto>( entityList.size() );
        for ( OcTask ocTask : entityList ) {
            list.add( toDto( ocTask ) );
        }

        return list;
    }

    @Override
    public OcTask toEntity(OcTaskDto dto) {
        if ( dto == null ) {
            return null;
        }

        OcTask ocTask = new OcTask();

        ocTask.setCreatetime( dto.getCreatetime() );
        ocTask.setCreateuser( dto.getCreateuser() );
        ocTask.setCron( dto.getCron() );
        ocTask.setExecutionstate( dto.getExecutionstate() );
        ocTask.setId( dto.getId() );
        ocTask.setOperationrule( dto.getOperationrule() );
        ocTask.setSourceid( dto.getSourceid() );
        ocTask.setSparefield1( dto.getSparefield1() );
        ocTask.setSparefield2( dto.getSparefield2() );
        ocTask.setSparefield3( dto.getSparefield3() );
        ocTask.setSparefield4( dto.getSparefield4() );
        ocTask.setStatus( dto.getStatus() );
        ocTask.setSubmitmethod( dto.getSubmitmethod() );
        ocTask.setTaskname( dto.getTaskname() );
        ocTask.setTasktype( dto.getTasktype() );
        ocTask.setUpdatetime( dto.getUpdatetime() );
        ocTask.setUpdateuser( dto.getUpdateuser() );

        return ocTask;
    }

    @Override
    public List<OcTask> toEntity(List<OcTaskDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<OcTask> list = new ArrayList<OcTask>( dtoList.size() );
        for ( OcTaskDto ocTaskDto : dtoList ) {
            list.add( toEntity( ocTaskDto ) );
        }

        return list;
    }
}
