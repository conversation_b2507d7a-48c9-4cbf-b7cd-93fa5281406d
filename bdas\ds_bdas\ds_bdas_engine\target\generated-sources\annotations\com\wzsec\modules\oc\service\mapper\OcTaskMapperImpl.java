package com.wzsec.modules.oc.service.mapper;

import com.wzsec.modules.oc.domain.OcTask;
import com.wzsec.modules.oc.service.dto.OcTaskDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:02+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class OcTaskMapperImpl implements OcTaskMapper {

    @Override
    public OcTask toEntity(OcTaskDto dto) {
        if ( dto == null ) {
            return null;
        }

        OcTask ocTask = new OcTask();

        ocTask.setId( dto.getId() );
        ocTask.setTaskname( dto.getTaskname() );
        ocTask.setTasktype( dto.getTasktype() );
        ocTask.setSourceid( dto.getSourceid() );
        ocTask.setOperationrule( dto.getOperationrule() );
        ocTask.setSubmitmethod( dto.getSubmitmethod() );
        ocTask.setCron( dto.getCron() );
        ocTask.setExecutionstate( dto.getExecutionstate() );
        ocTask.setStatus( dto.getStatus() );
        ocTask.setCreateuser( dto.getCreateuser() );
        ocTask.setCreatetime( dto.getCreatetime() );
        ocTask.setUpdateuser( dto.getUpdateuser() );
        ocTask.setUpdatetime( dto.getUpdatetime() );
        ocTask.setSparefield1( dto.getSparefield1() );
        ocTask.setSparefield2( dto.getSparefield2() );
        ocTask.setSparefield3( dto.getSparefield3() );
        ocTask.setSparefield4( dto.getSparefield4() );

        return ocTask;
    }

    @Override
    public OcTaskDto toDto(OcTask entity) {
        if ( entity == null ) {
            return null;
        }

        OcTaskDto ocTaskDto = new OcTaskDto();

        ocTaskDto.setId( entity.getId() );
        ocTaskDto.setTaskname( entity.getTaskname() );
        ocTaskDto.setTasktype( entity.getTasktype() );
        ocTaskDto.setSourceid( entity.getSourceid() );
        ocTaskDto.setOperationrule( entity.getOperationrule() );
        ocTaskDto.setSubmitmethod( entity.getSubmitmethod() );
        ocTaskDto.setCron( entity.getCron() );
        ocTaskDto.setExecutionstate( entity.getExecutionstate() );
        ocTaskDto.setStatus( entity.getStatus() );
        ocTaskDto.setCreateuser( entity.getCreateuser() );
        ocTaskDto.setCreatetime( entity.getCreatetime() );
        ocTaskDto.setUpdateuser( entity.getUpdateuser() );
        ocTaskDto.setUpdatetime( entity.getUpdatetime() );
        ocTaskDto.setSparefield1( entity.getSparefield1() );
        ocTaskDto.setSparefield2( entity.getSparefield2() );
        ocTaskDto.setSparefield3( entity.getSparefield3() );
        ocTaskDto.setSparefield4( entity.getSparefield4() );

        return ocTaskDto;
    }

    @Override
    public List<OcTask> toEntity(List<OcTaskDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<OcTask> list = new ArrayList<OcTask>( dtoList.size() );
        for ( OcTaskDto ocTaskDto : dtoList ) {
            list.add( toEntity( ocTaskDto ) );
        }

        return list;
    }

    @Override
    public List<OcTaskDto> toDto(List<OcTask> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<OcTaskDto> list = new ArrayList<OcTaskDto>( entityList.size() );
        for ( OcTask ocTask : entityList ) {
            list.add( toDto( ocTask ) );
        }

        return list;
    }
}
