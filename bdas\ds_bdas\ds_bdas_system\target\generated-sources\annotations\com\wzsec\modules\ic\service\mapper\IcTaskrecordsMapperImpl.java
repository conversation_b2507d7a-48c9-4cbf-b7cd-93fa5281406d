package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcTaskrecords;
import com.wzsec.modules.ic.service.dto.IcTaskrecordsDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:31+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class IcTaskrecordsMapperImpl implements IcTaskrecordsMapper {

    @Override
    public IcTaskrecords toEntity(IcTaskrecordsDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcTaskrecords icTaskrecords = new IcTaskrecords();

        icTaskrecords.setId( dto.getId() );
        icTaskrecords.setTaskname( dto.getTaskname() );
        icTaskrecords.setTasktype( dto.getTasktype() );
        icTaskrecords.setCreateuser( dto.getCreateuser() );
        icTaskrecords.setCreatetime( dto.getCreatetime() );
        icTaskrecords.setStarttime( dto.getStarttime() );
        icTaskrecords.setEndtime( dto.getEndtime() );
        icTaskrecords.setSubmitmethod( dto.getSubmitmethod() );
        icTaskrecords.setExecutionstate( dto.getExecutionstate() );
        icTaskrecords.setSparefield1( dto.getSparefield1() );
        icTaskrecords.setSparefield2( dto.getSparefield2() );
        icTaskrecords.setSparefield3( dto.getSparefield3() );
        icTaskrecords.setSparefield4( dto.getSparefield4() );

        return icTaskrecords;
    }

    @Override
    public IcTaskrecordsDto toDto(IcTaskrecords entity) {
        if ( entity == null ) {
            return null;
        }

        IcTaskrecordsDto icTaskrecordsDto = new IcTaskrecordsDto();

        icTaskrecordsDto.setId( entity.getId() );
        icTaskrecordsDto.setTaskname( entity.getTaskname() );
        icTaskrecordsDto.setTasktype( entity.getTasktype() );
        icTaskrecordsDto.setCreateuser( entity.getCreateuser() );
        icTaskrecordsDto.setCreatetime( entity.getCreatetime() );
        icTaskrecordsDto.setStarttime( entity.getStarttime() );
        icTaskrecordsDto.setEndtime( entity.getEndtime() );
        icTaskrecordsDto.setSubmitmethod( entity.getSubmitmethod() );
        icTaskrecordsDto.setExecutionstate( entity.getExecutionstate() );
        icTaskrecordsDto.setSparefield1( entity.getSparefield1() );
        icTaskrecordsDto.setSparefield2( entity.getSparefield2() );
        icTaskrecordsDto.setSparefield3( entity.getSparefield3() );
        icTaskrecordsDto.setSparefield4( entity.getSparefield4() );

        return icTaskrecordsDto;
    }

    @Override
    public List<IcTaskrecords> toEntity(List<IcTaskrecordsDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcTaskrecords> list = new ArrayList<IcTaskrecords>( dtoList.size() );
        for ( IcTaskrecordsDto icTaskrecordsDto : dtoList ) {
            list.add( toEntity( icTaskrecordsDto ) );
        }

        return list;
    }

    @Override
    public List<IcTaskrecordsDto> toDto(List<IcTaskrecords> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcTaskrecordsDto> list = new ArrayList<IcTaskrecordsDto>( entityList.size() );
        for ( IcTaskrecords icTaskrecords : entityList ) {
            list.add( toDto( icTaskrecords ) );
        }

        return list;
    }
}
