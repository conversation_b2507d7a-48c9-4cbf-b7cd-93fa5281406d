package com.wzsec.utils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class TWOSHA1 {
	
    private static final char[] HEX_DIGITS = {'0', '1', '2', '3', '4', '5',
	            '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};

    private static String getFormattedText(byte[] bytes) {
        int len = bytes.length;
        StringBuilder buf = new StringBuilder(len * 2);
        //把密文转换成十六进制的字符串形式
        for (int j = 0; j < len; j++) {
            buf.append(HEX_DIGITS[(bytes[j] >> 4) & 0x0f]);
            buf.append(HEX_DIGITS[bytes[j] & 0x0f]);
        }
        return buf.toString();
    }
    
    /**
     * mysql中密码两次hash加密
     * @param paramString
     * @return
     */
    public static String encrypt(String paramString) {
    	 String encStr = paramString;
	     MessageDigest messageDigest;
	        try {
	            messageDigest = MessageDigest.getInstance("SHA1");
	            messageDigest.update(paramString.getBytes());
	            byte[] b_itr1 = messageDigest.digest();
	            messageDigest.update(b_itr1);
	            encStr = getFormattedText(messageDigest.digest());
	        } catch (NoSuchAlgorithmException e) {
	            System.out.println(e.getMessage());
	        }
			return encStr; 
    }

	public static void main(String[] args) {
		TWOSHA1 twoSha1 = new TWOSHA1();
        System.out.println(twoSha1.encrypt("root"));
	}

}
