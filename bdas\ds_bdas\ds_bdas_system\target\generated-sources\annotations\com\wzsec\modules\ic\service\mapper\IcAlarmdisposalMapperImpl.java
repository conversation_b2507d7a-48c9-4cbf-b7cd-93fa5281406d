package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcAlarmdisposal;
import com.wzsec.modules.ic.service.dto.IcAlarmdisposalDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:31+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class IcAlarmdisposalMapperImpl implements IcAlarmdisposalMapper {

    @Override
    public IcAlarmdisposalDto toDto(IcAlarmdisposal entity) {
        if ( entity == null ) {
            return null;
        }

        IcAlarmdisposalDto icAlarmdisposalDto = new IcAlarmdisposalDto();

        icAlarmdisposalDto.setAccount( entity.getAccount() );
        icAlarmdisposalDto.setAk( entity.getAk() );
        icAlarmdisposalDto.setApicode( entity.getApicode() );
        icAlarmdisposalDto.setApiname( entity.getApiname() );
        icAlarmdisposalDto.setApiurl( entity.getApiurl() );
        icAlarmdisposalDto.setArea( entity.getArea() );
        icAlarmdisposalDto.setChecktime( entity.getChecktime() );
        icAlarmdisposalDto.setCircumstantiality( entity.getCircumstantiality() );
        icAlarmdisposalDto.setDepartment( entity.getDepartment() );
        icAlarmdisposalDto.setDestinationip( entity.getDestinationip() );
        icAlarmdisposalDto.setDestinationport( entity.getDestinationport() );
        icAlarmdisposalDto.setDetectionmodel( entity.getDetectionmodel() );
        icAlarmdisposalDto.setEventhandlingtime( entity.getEventhandlingtime() );
        icAlarmdisposalDto.setEventrule( entity.getEventrule() );
        icAlarmdisposalDto.setIcresdepartment( entity.getIcresdepartment() );
        icAlarmdisposalDto.setIcressystem( entity.getIcressystem() );
        icAlarmdisposalDto.setId( entity.getId() );
        icAlarmdisposalDto.setIncidenthandler( entity.getIncidenthandler() );
        icAlarmdisposalDto.setNote( entity.getNote() );
        icAlarmdisposalDto.setPushnumber( entity.getPushnumber() );
        icAlarmdisposalDto.setReqapp( entity.getReqapp() );
        icAlarmdisposalDto.setReqdepartment( entity.getReqdepartment() );
        icAlarmdisposalDto.setReservefield1( entity.getReservefield1() );
        icAlarmdisposalDto.setReservefield2( entity.getReservefield2() );
        icAlarmdisposalDto.setReservefield3( entity.getReservefield3() );
        icAlarmdisposalDto.setReservefield4( entity.getReservefield4() );
        icAlarmdisposalDto.setReservefield5( entity.getReservefield5() );
        icAlarmdisposalDto.setReservefield6( entity.getReservefield6() );
        icAlarmdisposalDto.setRisk( entity.getRisk() );
        icAlarmdisposalDto.setSourceip( entity.getSourceip() );
        icAlarmdisposalDto.setSourceport( entity.getSourceport() );
        icAlarmdisposalDto.setSystemname( entity.getSystemname() );
        icAlarmdisposalDto.setTreatmentstate( entity.getTreatmentstate() );
        icAlarmdisposalDto.setUuid( entity.getUuid() );

        return icAlarmdisposalDto;
    }

    @Override
    public List<IcAlarmdisposalDto> toDto(List<IcAlarmdisposal> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcAlarmdisposalDto> list = new ArrayList<IcAlarmdisposalDto>( entityList.size() );
        for ( IcAlarmdisposal icAlarmdisposal : entityList ) {
            list.add( toDto( icAlarmdisposal ) );
        }

        return list;
    }

    @Override
    public IcAlarmdisposal toEntity(IcAlarmdisposalDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcAlarmdisposal icAlarmdisposal = new IcAlarmdisposal();

        icAlarmdisposal.setAccount( dto.getAccount() );
        icAlarmdisposal.setAk( dto.getAk() );
        icAlarmdisposal.setApicode( dto.getApicode() );
        icAlarmdisposal.setApiname( dto.getApiname() );
        icAlarmdisposal.setApiurl( dto.getApiurl() );
        icAlarmdisposal.setArea( dto.getArea() );
        icAlarmdisposal.setChecktime( dto.getChecktime() );
        icAlarmdisposal.setCircumstantiality( dto.getCircumstantiality() );
        icAlarmdisposal.setDepartment( dto.getDepartment() );
        icAlarmdisposal.setDestinationip( dto.getDestinationip() );
        icAlarmdisposal.setDestinationport( dto.getDestinationport() );
        icAlarmdisposal.setDetectionmodel( dto.getDetectionmodel() );
        icAlarmdisposal.setEventhandlingtime( dto.getEventhandlingtime() );
        icAlarmdisposal.setEventrule( dto.getEventrule() );
        icAlarmdisposal.setIcresdepartment( dto.getIcresdepartment() );
        icAlarmdisposal.setIcressystem( dto.getIcressystem() );
        icAlarmdisposal.setId( dto.getId() );
        icAlarmdisposal.setIncidenthandler( dto.getIncidenthandler() );
        icAlarmdisposal.setNote( dto.getNote() );
        icAlarmdisposal.setPushnumber( dto.getPushnumber() );
        icAlarmdisposal.setReqapp( dto.getReqapp() );
        icAlarmdisposal.setReqdepartment( dto.getReqdepartment() );
        icAlarmdisposal.setReservefield1( dto.getReservefield1() );
        icAlarmdisposal.setReservefield2( dto.getReservefield2() );
        icAlarmdisposal.setReservefield3( dto.getReservefield3() );
        icAlarmdisposal.setReservefield4( dto.getReservefield4() );
        icAlarmdisposal.setReservefield5( dto.getReservefield5() );
        icAlarmdisposal.setReservefield6( dto.getReservefield6() );
        icAlarmdisposal.setRisk( dto.getRisk() );
        icAlarmdisposal.setSourceip( dto.getSourceip() );
        icAlarmdisposal.setSourceport( dto.getSourceport() );
        icAlarmdisposal.setSystemname( dto.getSystemname() );
        icAlarmdisposal.setTreatmentstate( dto.getTreatmentstate() );
        icAlarmdisposal.setUuid( dto.getUuid() );

        return icAlarmdisposal;
    }

    @Override
    public List<IcAlarmdisposal> toEntity(List<IcAlarmdisposalDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcAlarmdisposal> list = new ArrayList<IcAlarmdisposal>( dtoList.size() );
        for ( IcAlarmdisposalDto icAlarmdisposalDto : dtoList ) {
            list.add( toEntity( icAlarmdisposalDto ) );
        }

        return list;
    }
}
