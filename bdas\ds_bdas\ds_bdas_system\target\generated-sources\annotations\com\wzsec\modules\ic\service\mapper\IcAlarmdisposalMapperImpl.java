package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcAlarmdisposal;
import com.wzsec.modules.ic.service.dto.IcAlarmdisposalDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:31+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class IcAlarmdisposalMapperImpl implements IcAlarmdisposalMapper {

    @Override
    public IcAlarmdisposal toEntity(IcAlarmdisposalDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcAlarmdisposal icAlarmdisposal = new IcAlarmdisposal();

        icAlarmdisposal.setId( dto.getId() );
        icAlarmdisposal.setApicode( dto.getApicode() );
        icAlarmdisposal.setApiname( dto.getApiname() );
        icAlarmdisposal.setDetectionmodel( dto.getDetectionmodel() );
        icAlarmdisposal.setCircumstantiality( dto.getCircumstantiality() );
        icAlarmdisposal.setRisk( dto.getRisk() );
        icAlarmdisposal.setChecktime( dto.getChecktime() );
        icAlarmdisposal.setTreatmentstate( dto.getTreatmentstate() );
        icAlarmdisposal.setNote( dto.getNote() );
        icAlarmdisposal.setReservefield1( dto.getReservefield1() );
        icAlarmdisposal.setReservefield2( dto.getReservefield2() );
        icAlarmdisposal.setReservefield3( dto.getReservefield3() );
        icAlarmdisposal.setReservefield4( dto.getReservefield4() );
        icAlarmdisposal.setReservefield5( dto.getReservefield5() );
        icAlarmdisposal.setReservefield6( dto.getReservefield6() );
        icAlarmdisposal.setArea( dto.getArea() );
        icAlarmdisposal.setDepartment( dto.getDepartment() );
        icAlarmdisposal.setIncidenthandler( dto.getIncidenthandler() );
        icAlarmdisposal.setEventhandlingtime( dto.getEventhandlingtime() );
        icAlarmdisposal.setSourceip( dto.getSourceip() );
        icAlarmdisposal.setSourceport( dto.getSourceport() );
        icAlarmdisposal.setDestinationip( dto.getDestinationip() );
        icAlarmdisposal.setDestinationport( dto.getDestinationport() );
        icAlarmdisposal.setAccount( dto.getAccount() );
        icAlarmdisposal.setEventrule( dto.getEventrule() );
        icAlarmdisposal.setPushnumber( dto.getPushnumber() );
        icAlarmdisposal.setUuid( dto.getUuid() );
        icAlarmdisposal.setAk( dto.getAk() );
        icAlarmdisposal.setApiurl( dto.getApiurl() );
        icAlarmdisposal.setReqapp( dto.getReqapp() );
        icAlarmdisposal.setReqdepartment( dto.getReqdepartment() );
        icAlarmdisposal.setIcressystem( dto.getIcressystem() );
        icAlarmdisposal.setIcresdepartment( dto.getIcresdepartment() );
        icAlarmdisposal.setSystemname( dto.getSystemname() );

        return icAlarmdisposal;
    }

    @Override
    public IcAlarmdisposalDto toDto(IcAlarmdisposal entity) {
        if ( entity == null ) {
            return null;
        }

        IcAlarmdisposalDto icAlarmdisposalDto = new IcAlarmdisposalDto();

        icAlarmdisposalDto.setId( entity.getId() );
        icAlarmdisposalDto.setApicode( entity.getApicode() );
        icAlarmdisposalDto.setApiname( entity.getApiname() );
        icAlarmdisposalDto.setDetectionmodel( entity.getDetectionmodel() );
        icAlarmdisposalDto.setCircumstantiality( entity.getCircumstantiality() );
        icAlarmdisposalDto.setRisk( entity.getRisk() );
        icAlarmdisposalDto.setChecktime( entity.getChecktime() );
        icAlarmdisposalDto.setTreatmentstate( entity.getTreatmentstate() );
        icAlarmdisposalDto.setNote( entity.getNote() );
        icAlarmdisposalDto.setReservefield1( entity.getReservefield1() );
        icAlarmdisposalDto.setReservefield2( entity.getReservefield2() );
        icAlarmdisposalDto.setReservefield3( entity.getReservefield3() );
        icAlarmdisposalDto.setReservefield4( entity.getReservefield4() );
        icAlarmdisposalDto.setReservefield5( entity.getReservefield5() );
        icAlarmdisposalDto.setReservefield6( entity.getReservefield6() );
        icAlarmdisposalDto.setArea( entity.getArea() );
        icAlarmdisposalDto.setDepartment( entity.getDepartment() );
        icAlarmdisposalDto.setIncidenthandler( entity.getIncidenthandler() );
        icAlarmdisposalDto.setEventhandlingtime( entity.getEventhandlingtime() );
        icAlarmdisposalDto.setSourceip( entity.getSourceip() );
        icAlarmdisposalDto.setSourceport( entity.getSourceport() );
        icAlarmdisposalDto.setDestinationip( entity.getDestinationip() );
        icAlarmdisposalDto.setDestinationport( entity.getDestinationport() );
        icAlarmdisposalDto.setAccount( entity.getAccount() );
        icAlarmdisposalDto.setEventrule( entity.getEventrule() );
        icAlarmdisposalDto.setPushnumber( entity.getPushnumber() );
        icAlarmdisposalDto.setUuid( entity.getUuid() );
        icAlarmdisposalDto.setAk( entity.getAk() );
        icAlarmdisposalDto.setApiurl( entity.getApiurl() );
        icAlarmdisposalDto.setReqapp( entity.getReqapp() );
        icAlarmdisposalDto.setReqdepartment( entity.getReqdepartment() );
        icAlarmdisposalDto.setIcressystem( entity.getIcressystem() );
        icAlarmdisposalDto.setIcresdepartment( entity.getIcresdepartment() );
        icAlarmdisposalDto.setSystemname( entity.getSystemname() );

        return icAlarmdisposalDto;
    }

    @Override
    public List<IcAlarmdisposal> toEntity(List<IcAlarmdisposalDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcAlarmdisposal> list = new ArrayList<IcAlarmdisposal>( dtoList.size() );
        for ( IcAlarmdisposalDto icAlarmdisposalDto : dtoList ) {
            list.add( toEntity( icAlarmdisposalDto ) );
        }

        return list;
    }

    @Override
    public List<IcAlarmdisposalDto> toDto(List<IcAlarmdisposal> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcAlarmdisposalDto> list = new ArrayList<IcAlarmdisposalDto>( entityList.size() );
        for ( IcAlarmdisposal icAlarmdisposal : entityList ) {
            list.add( toDto( icAlarmdisposal ) );
        }

        return list;
    }
}
