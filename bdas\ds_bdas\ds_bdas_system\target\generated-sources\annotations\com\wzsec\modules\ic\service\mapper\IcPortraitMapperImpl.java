package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcPortrait;
import com.wzsec.modules.ic.service.dto.IcPortraitDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:30+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class IcPortraitMapperImpl implements IcPortraitMapper {

    @Override
    public IcPortrait toEntity(IcPortraitDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcPortrait icPortrait = new IcPortrait();

        icPortrait.setId( dto.getId() );
        icPortrait.setApicode( dto.getApicode() );
        icPortrait.setApiname( dto.getApiname() );
        icPortrait.setUrl( dto.getUrl() );
        icPortrait.setInvokeType( dto.getInvokeType() );
        icPortrait.setDataformat( dto.getDataformat() );
        icPortrait.setInputparams( dto.getInputparams() );
        icPortrait.setOutputparams( dto.getOutputparams() );
        icPortrait.setOrganization( dto.getOrganization() );
        icPortrait.setRegisterUnit( dto.getRegisterUnit() );
        icPortrait.setBusinessname( dto.getBusinessname() );
        icPortrait.setBusinessid( dto.getBusinessid() );
        icPortrait.setSafetyindex( dto.getSafetyindex() );
        icPortrait.setSensitivetype( dto.getSensitivetype() );
        icPortrait.setSensitiverisk( dto.getSensitiverisk() );
        icPortrait.setUnexpectedmethod( dto.getUnexpectedmethod() );
        icPortrait.setUnexpectedfieldname( dto.getUnexpectedfieldname() );
        icPortrait.setUnexpectedfieldvalue( dto.getUnexpectedfieldvalue() );
        icPortrait.setUnexpectedrisk( dto.getUnexpectedrisk() );
        icPortrait.setOutdatacallcount( dto.getOutdatacallcount() );
        icPortrait.setOutdatacallrisk( dto.getOutdatacallrisk() );
        icPortrait.setAuthreqmethod( dto.getAuthreqmethod() );
        icPortrait.setAuthmethod( dto.getAuthmethod() );
        icPortrait.setAuthserviceip( dto.getAuthserviceip() );
        icPortrait.setAuthserviceport( dto.getAuthserviceport() );
        icPortrait.setAuthrisk( dto.getAuthrisk() );
        icPortrait.setActualinput( dto.getActualinput() );
        icPortrait.setActualoutput( dto.getActualoutput() );
        icPortrait.setComplianceauditrisk( dto.getComplianceauditrisk() );
        icPortrait.setSilentaccount( dto.getSilentaccount() );
        icPortrait.setSilentaccountrisk( dto.getSilentaccountrisk() );
        icPortrait.setUsercallclientip( dto.getUsercallclientip() );
        icPortrait.setUsercallcount( dto.getUsercallcount() );
        icPortrait.setUsercallrisk( dto.getUsercallrisk() );
        icPortrait.setInvokingrecordsum( dto.getInvokingrecordsum() );
        icPortrait.setInvokingrecordweekavg( dto.getInvokingrecordweekavg() );
        icPortrait.setInvokingrecordmax( dto.getInvokingrecordmax() );
        icPortrait.setInvokingrecordmin( dto.getInvokingrecordmin() );
        icPortrait.setInfrequentlycalldecide( dto.getInfrequentlycalldecide() );
        icPortrait.setCallbegintime( dto.getCallbegintime() );
        icPortrait.setCallendtime( dto.getCallendtime() );
        icPortrait.setUnusedcheckdecide( dto.getUnusedcheckdecide() );
        icPortrait.setErrorchecktype( dto.getErrorchecktype() );
        icPortrait.setErrorchecksum( dto.getErrorchecksum() );
        icPortrait.setAttackdetection( dto.getAttackdetection() );
        icPortrait.setCreatetime( dto.getCreatetime() );
        icPortrait.setUpdatetime( dto.getUpdatetime() );
        icPortrait.setNote( dto.getNote() );
        icPortrait.setSparefield1( dto.getSparefield1() );
        icPortrait.setSparefield2( dto.getSparefield2() );
        icPortrait.setSparefield3( dto.getSparefield3() );
        icPortrait.setSparefield4( dto.getSparefield4() );
        icPortrait.setSparefield5( dto.getSparefield5() );
        icPortrait.setSparefield6( dto.getSparefield6() );
        icPortrait.setSparefield7( dto.getSparefield7() );
        icPortrait.setSparefield8( dto.getSparefield8() );
        icPortrait.setSparefield9( dto.getSparefield9() );
        icPortrait.setSparefield10( dto.getSparefield10() );

        return icPortrait;
    }

    @Override
    public IcPortraitDto toDto(IcPortrait entity) {
        if ( entity == null ) {
            return null;
        }

        IcPortraitDto icPortraitDto = new IcPortraitDto();

        icPortraitDto.setId( entity.getId() );
        icPortraitDto.setApicode( entity.getApicode() );
        icPortraitDto.setApiname( entity.getApiname() );
        icPortraitDto.setUrl( entity.getUrl() );
        icPortraitDto.setInvokeType( entity.getInvokeType() );
        icPortraitDto.setDataformat( entity.getDataformat() );
        icPortraitDto.setInputparams( entity.getInputparams() );
        icPortraitDto.setOutputparams( entity.getOutputparams() );
        icPortraitDto.setOrganization( entity.getOrganization() );
        icPortraitDto.setRegisterUnit( entity.getRegisterUnit() );
        icPortraitDto.setBusinessname( entity.getBusinessname() );
        icPortraitDto.setBusinessid( entity.getBusinessid() );
        icPortraitDto.setSafetyindex( entity.getSafetyindex() );
        icPortraitDto.setSensitivetype( entity.getSensitivetype() );
        icPortraitDto.setSensitiverisk( entity.getSensitiverisk() );
        icPortraitDto.setUnexpectedmethod( entity.getUnexpectedmethod() );
        icPortraitDto.setUnexpectedfieldname( entity.getUnexpectedfieldname() );
        icPortraitDto.setUnexpectedfieldvalue( entity.getUnexpectedfieldvalue() );
        icPortraitDto.setUnexpectedrisk( entity.getUnexpectedrisk() );
        icPortraitDto.setOutdatacallcount( entity.getOutdatacallcount() );
        icPortraitDto.setOutdatacallrisk( entity.getOutdatacallrisk() );
        icPortraitDto.setAuthreqmethod( entity.getAuthreqmethod() );
        icPortraitDto.setAuthmethod( entity.getAuthmethod() );
        icPortraitDto.setAuthserviceip( entity.getAuthserviceip() );
        icPortraitDto.setAuthserviceport( entity.getAuthserviceport() );
        icPortraitDto.setAuthrisk( entity.getAuthrisk() );
        icPortraitDto.setActualinput( entity.getActualinput() );
        icPortraitDto.setActualoutput( entity.getActualoutput() );
        icPortraitDto.setComplianceauditrisk( entity.getComplianceauditrisk() );
        icPortraitDto.setSilentaccount( entity.getSilentaccount() );
        icPortraitDto.setSilentaccountrisk( entity.getSilentaccountrisk() );
        icPortraitDto.setUsercallclientip( entity.getUsercallclientip() );
        icPortraitDto.setUsercallcount( entity.getUsercallcount() );
        icPortraitDto.setUsercallrisk( entity.getUsercallrisk() );
        icPortraitDto.setInvokingrecordsum( entity.getInvokingrecordsum() );
        icPortraitDto.setInvokingrecordweekavg( entity.getInvokingrecordweekavg() );
        icPortraitDto.setInvokingrecordmax( entity.getInvokingrecordmax() );
        icPortraitDto.setInvokingrecordmin( entity.getInvokingrecordmin() );
        icPortraitDto.setInfrequentlycalldecide( entity.getInfrequentlycalldecide() );
        icPortraitDto.setCallbegintime( entity.getCallbegintime() );
        icPortraitDto.setCallendtime( entity.getCallendtime() );
        icPortraitDto.setUnusedcheckdecide( entity.getUnusedcheckdecide() );
        icPortraitDto.setErrorchecktype( entity.getErrorchecktype() );
        icPortraitDto.setErrorchecksum( entity.getErrorchecksum() );
        icPortraitDto.setAttackdetection( entity.getAttackdetection() );
        icPortraitDto.setCreatetime( entity.getCreatetime() );
        icPortraitDto.setUpdatetime( entity.getUpdatetime() );
        icPortraitDto.setNote( entity.getNote() );
        icPortraitDto.setSparefield1( entity.getSparefield1() );
        icPortraitDto.setSparefield2( entity.getSparefield2() );
        icPortraitDto.setSparefield3( entity.getSparefield3() );
        icPortraitDto.setSparefield4( entity.getSparefield4() );
        icPortraitDto.setSparefield5( entity.getSparefield5() );
        icPortraitDto.setSparefield6( entity.getSparefield6() );
        icPortraitDto.setSparefield7( entity.getSparefield7() );
        icPortraitDto.setSparefield8( entity.getSparefield8() );
        icPortraitDto.setSparefield9( entity.getSparefield9() );
        icPortraitDto.setSparefield10( entity.getSparefield10() );

        return icPortraitDto;
    }

    @Override
    public List<IcPortrait> toEntity(List<IcPortraitDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcPortrait> list = new ArrayList<IcPortrait>( dtoList.size() );
        for ( IcPortraitDto icPortraitDto : dtoList ) {
            list.add( toEntity( icPortraitDto ) );
        }

        return list;
    }

    @Override
    public List<IcPortraitDto> toDto(List<IcPortrait> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcPortraitDto> list = new ArrayList<IcPortraitDto>( entityList.size() );
        for ( IcPortrait icPortrait : entityList ) {
            list.add( toDto( icPortrait ) );
        }

        return list;
    }
}
