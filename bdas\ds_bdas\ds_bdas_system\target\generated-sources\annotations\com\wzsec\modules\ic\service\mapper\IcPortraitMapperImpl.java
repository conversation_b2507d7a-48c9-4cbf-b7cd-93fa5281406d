package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcPortrait;
import com.wzsec.modules.ic.service.dto.IcPortraitDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:32+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class IcPortraitMapperImpl implements IcPortraitMapper {

    @Override
    public IcPortraitDto toDto(IcPortrait entity) {
        if ( entity == null ) {
            return null;
        }

        IcPortraitDto icPortraitDto = new IcPortraitDto();

        icPortraitDto.setActualinput( entity.getActualinput() );
        icPortraitDto.setActualoutput( entity.getActualoutput() );
        icPortraitDto.setApicode( entity.getApicode() );
        icPortraitDto.setApiname( entity.getApiname() );
        icPortraitDto.setAttackdetection( entity.getAttackdetection() );
        icPortraitDto.setAuthmethod( entity.getAuthmethod() );
        icPortraitDto.setAuthreqmethod( entity.getAuthreqmethod() );
        icPortraitDto.setAuthrisk( entity.getAuthrisk() );
        icPortraitDto.setAuthserviceip( entity.getAuthserviceip() );
        icPortraitDto.setAuthserviceport( entity.getAuthserviceport() );
        icPortraitDto.setBusinessid( entity.getBusinessid() );
        icPortraitDto.setBusinessname( entity.getBusinessname() );
        icPortraitDto.setCallbegintime( entity.getCallbegintime() );
        icPortraitDto.setCallendtime( entity.getCallendtime() );
        icPortraitDto.setComplianceauditrisk( entity.getComplianceauditrisk() );
        icPortraitDto.setCreatetime( entity.getCreatetime() );
        icPortraitDto.setDataformat( entity.getDataformat() );
        icPortraitDto.setErrorchecksum( entity.getErrorchecksum() );
        icPortraitDto.setErrorchecktype( entity.getErrorchecktype() );
        icPortraitDto.setId( entity.getId() );
        icPortraitDto.setInfrequentlycalldecide( entity.getInfrequentlycalldecide() );
        icPortraitDto.setInputparams( entity.getInputparams() );
        icPortraitDto.setInvokeType( entity.getInvokeType() );
        icPortraitDto.setInvokingrecordmax( entity.getInvokingrecordmax() );
        icPortraitDto.setInvokingrecordmin( entity.getInvokingrecordmin() );
        icPortraitDto.setInvokingrecordsum( entity.getInvokingrecordsum() );
        icPortraitDto.setInvokingrecordweekavg( entity.getInvokingrecordweekavg() );
        icPortraitDto.setNote( entity.getNote() );
        icPortraitDto.setOrganization( entity.getOrganization() );
        icPortraitDto.setOutdatacallcount( entity.getOutdatacallcount() );
        icPortraitDto.setOutdatacallrisk( entity.getOutdatacallrisk() );
        icPortraitDto.setOutputparams( entity.getOutputparams() );
        icPortraitDto.setRegisterUnit( entity.getRegisterUnit() );
        icPortraitDto.setSafetyindex( entity.getSafetyindex() );
        icPortraitDto.setSensitiverisk( entity.getSensitiverisk() );
        icPortraitDto.setSensitivetype( entity.getSensitivetype() );
        icPortraitDto.setSilentaccount( entity.getSilentaccount() );
        icPortraitDto.setSilentaccountrisk( entity.getSilentaccountrisk() );
        icPortraitDto.setSparefield1( entity.getSparefield1() );
        icPortraitDto.setSparefield10( entity.getSparefield10() );
        icPortraitDto.setSparefield2( entity.getSparefield2() );
        icPortraitDto.setSparefield3( entity.getSparefield3() );
        icPortraitDto.setSparefield4( entity.getSparefield4() );
        icPortraitDto.setSparefield5( entity.getSparefield5() );
        icPortraitDto.setSparefield6( entity.getSparefield6() );
        icPortraitDto.setSparefield7( entity.getSparefield7() );
        icPortraitDto.setSparefield8( entity.getSparefield8() );
        icPortraitDto.setSparefield9( entity.getSparefield9() );
        icPortraitDto.setUnexpectedfieldname( entity.getUnexpectedfieldname() );
        icPortraitDto.setUnexpectedfieldvalue( entity.getUnexpectedfieldvalue() );
        icPortraitDto.setUnexpectedmethod( entity.getUnexpectedmethod() );
        icPortraitDto.setUnexpectedrisk( entity.getUnexpectedrisk() );
        icPortraitDto.setUnusedcheckdecide( entity.getUnusedcheckdecide() );
        icPortraitDto.setUpdatetime( entity.getUpdatetime() );
        icPortraitDto.setUrl( entity.getUrl() );
        icPortraitDto.setUsercallclientip( entity.getUsercallclientip() );
        icPortraitDto.setUsercallcount( entity.getUsercallcount() );
        icPortraitDto.setUsercallrisk( entity.getUsercallrisk() );

        return icPortraitDto;
    }

    @Override
    public List<IcPortraitDto> toDto(List<IcPortrait> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcPortraitDto> list = new ArrayList<IcPortraitDto>( entityList.size() );
        for ( IcPortrait icPortrait : entityList ) {
            list.add( toDto( icPortrait ) );
        }

        return list;
    }

    @Override
    public IcPortrait toEntity(IcPortraitDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcPortrait icPortrait = new IcPortrait();

        icPortrait.setActualinput( dto.getActualinput() );
        icPortrait.setActualoutput( dto.getActualoutput() );
        icPortrait.setApicode( dto.getApicode() );
        icPortrait.setApiname( dto.getApiname() );
        icPortrait.setAttackdetection( dto.getAttackdetection() );
        icPortrait.setAuthmethod( dto.getAuthmethod() );
        icPortrait.setAuthreqmethod( dto.getAuthreqmethod() );
        icPortrait.setAuthrisk( dto.getAuthrisk() );
        icPortrait.setAuthserviceip( dto.getAuthserviceip() );
        icPortrait.setAuthserviceport( dto.getAuthserviceport() );
        icPortrait.setBusinessid( dto.getBusinessid() );
        icPortrait.setBusinessname( dto.getBusinessname() );
        icPortrait.setCallbegintime( dto.getCallbegintime() );
        icPortrait.setCallendtime( dto.getCallendtime() );
        icPortrait.setComplianceauditrisk( dto.getComplianceauditrisk() );
        icPortrait.setCreatetime( dto.getCreatetime() );
        icPortrait.setDataformat( dto.getDataformat() );
        icPortrait.setErrorchecksum( dto.getErrorchecksum() );
        icPortrait.setErrorchecktype( dto.getErrorchecktype() );
        icPortrait.setId( dto.getId() );
        icPortrait.setInfrequentlycalldecide( dto.getInfrequentlycalldecide() );
        icPortrait.setInputparams( dto.getInputparams() );
        icPortrait.setInvokeType( dto.getInvokeType() );
        icPortrait.setInvokingrecordmax( dto.getInvokingrecordmax() );
        icPortrait.setInvokingrecordmin( dto.getInvokingrecordmin() );
        icPortrait.setInvokingrecordsum( dto.getInvokingrecordsum() );
        icPortrait.setInvokingrecordweekavg( dto.getInvokingrecordweekavg() );
        icPortrait.setNote( dto.getNote() );
        icPortrait.setOrganization( dto.getOrganization() );
        icPortrait.setOutdatacallcount( dto.getOutdatacallcount() );
        icPortrait.setOutdatacallrisk( dto.getOutdatacallrisk() );
        icPortrait.setOutputparams( dto.getOutputparams() );
        icPortrait.setRegisterUnit( dto.getRegisterUnit() );
        icPortrait.setSafetyindex( dto.getSafetyindex() );
        icPortrait.setSensitiverisk( dto.getSensitiverisk() );
        icPortrait.setSensitivetype( dto.getSensitivetype() );
        icPortrait.setSilentaccount( dto.getSilentaccount() );
        icPortrait.setSilentaccountrisk( dto.getSilentaccountrisk() );
        icPortrait.setSparefield1( dto.getSparefield1() );
        icPortrait.setSparefield10( dto.getSparefield10() );
        icPortrait.setSparefield2( dto.getSparefield2() );
        icPortrait.setSparefield3( dto.getSparefield3() );
        icPortrait.setSparefield4( dto.getSparefield4() );
        icPortrait.setSparefield5( dto.getSparefield5() );
        icPortrait.setSparefield6( dto.getSparefield6() );
        icPortrait.setSparefield7( dto.getSparefield7() );
        icPortrait.setSparefield8( dto.getSparefield8() );
        icPortrait.setSparefield9( dto.getSparefield9() );
        icPortrait.setUnexpectedfieldname( dto.getUnexpectedfieldname() );
        icPortrait.setUnexpectedfieldvalue( dto.getUnexpectedfieldvalue() );
        icPortrait.setUnexpectedmethod( dto.getUnexpectedmethod() );
        icPortrait.setUnexpectedrisk( dto.getUnexpectedrisk() );
        icPortrait.setUnusedcheckdecide( dto.getUnusedcheckdecide() );
        icPortrait.setUpdatetime( dto.getUpdatetime() );
        icPortrait.setUrl( dto.getUrl() );
        icPortrait.setUsercallclientip( dto.getUsercallclientip() );
        icPortrait.setUsercallcount( dto.getUsercallcount() );
        icPortrait.setUsercallrisk( dto.getUsercallrisk() );

        return icPortrait;
    }

    @Override
    public List<IcPortrait> toEntity(List<IcPortraitDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcPortrait> list = new ArrayList<IcPortrait>( dtoList.size() );
        for ( IcPortraitDto icPortraitDto : dtoList ) {
            list.add( toEntity( icPortraitDto ) );
        }

        return list;
    }
}
