package com.wzsec.modules.sdd.weakpwd.service.mapper;

import com.wzsec.modules.sdd.weakpwd.domain.ResultTable;
import com.wzsec.modules.sdd.weakpwd.service.dto.ResultTableDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:54+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ResultTableMapperImpl implements ResultTableMapper {

    @Override
    public ResultTableDto toDto(ResultTable entity) {
        if ( entity == null ) {
            return null;
        }

        ResultTableDto resultTableDto = new ResultTableDto();

        resultTableDto.setCreatetime( entity.getCreatetime() );
        resultTableDto.setDbname( entity.getDbname() );
        resultTableDto.setId( entity.getId() );
        resultTableDto.setLocation( entity.getLocation() );
        resultTableDto.setLoginname( entity.getLoginname() );
        resultTableDto.setLoginnamefield( entity.getLoginnamefield() );
        resultTableDto.setPwd( entity.getPwd() );
        resultTableDto.setPwdfield( entity.getPwdfield() );
        resultTableDto.setResulttype( entity.getResulttype() );
        resultTableDto.setSparefield1( entity.getSparefield1() );
        resultTableDto.setSparefield2( entity.getSparefield2() );
        resultTableDto.setSparefield3( entity.getSparefield3() );
        resultTableDto.setSparefield4( entity.getSparefield4() );
        resultTableDto.setSparefield5( entity.getSparefield5() );
        resultTableDto.setSubtaskno( entity.getSubtaskno() );
        resultTableDto.setTablename( entity.getTablename() );
        resultTableDto.setTaskno( entity.getTaskno() );
        resultTableDto.setType( entity.getType() );
        resultTableDto.setUsername( entity.getUsername() );
        resultTableDto.setUsernamefield( entity.getUsernamefield() );
        resultTableDto.setWpcount( entity.getWpcount() );

        return resultTableDto;
    }

    @Override
    public List<ResultTableDto> toDto(List<ResultTable> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ResultTableDto> list = new ArrayList<ResultTableDto>( entityList.size() );
        for ( ResultTable resultTable : entityList ) {
            list.add( toDto( resultTable ) );
        }

        return list;
    }

    @Override
    public ResultTable toEntity(ResultTableDto dto) {
        if ( dto == null ) {
            return null;
        }

        ResultTable resultTable = new ResultTable();

        resultTable.setCreatetime( dto.getCreatetime() );
        resultTable.setDbname( dto.getDbname() );
        resultTable.setId( dto.getId() );
        resultTable.setLocation( dto.getLocation() );
        resultTable.setLoginname( dto.getLoginname() );
        resultTable.setLoginnamefield( dto.getLoginnamefield() );
        resultTable.setPwd( dto.getPwd() );
        resultTable.setPwdfield( dto.getPwdfield() );
        resultTable.setResulttype( dto.getResulttype() );
        resultTable.setSparefield1( dto.getSparefield1() );
        resultTable.setSparefield2( dto.getSparefield2() );
        resultTable.setSparefield3( dto.getSparefield3() );
        resultTable.setSparefield4( dto.getSparefield4() );
        resultTable.setSparefield5( dto.getSparefield5() );
        resultTable.setSubtaskno( dto.getSubtaskno() );
        resultTable.setTablename( dto.getTablename() );
        resultTable.setTaskno( dto.getTaskno() );
        resultTable.setType( dto.getType() );
        resultTable.setUsername( dto.getUsername() );
        resultTable.setUsernamefield( dto.getUsernamefield() );
        resultTable.setWpcount( dto.getWpcount() );

        return resultTable;
    }

    @Override
    public List<ResultTable> toEntity(List<ResultTableDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ResultTable> list = new ArrayList<ResultTable>( dtoList.size() );
        for ( ResultTableDto resultTableDto : dtoList ) {
            list.add( toEntity( resultTableDto ) );
        }

        return list;
    }
}
