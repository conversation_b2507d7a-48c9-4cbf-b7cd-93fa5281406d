package com.wzsec.modules.sdd.weakpwd.service.mapper;

import com.wzsec.modules.sdd.weakpwd.domain.ResultTable;
import com.wzsec.modules.sdd.weakpwd.service.dto.ResultTableDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:02+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ResultTableMapperImpl implements ResultTableMapper {

    @Override
    public ResultTable toEntity(ResultTableDto dto) {
        if ( dto == null ) {
            return null;
        }

        ResultTable resultTable = new ResultTable();

        resultTable.setId( dto.getId() );
        resultTable.setTaskno( dto.getTaskno() );
        resultTable.setSubtaskno( dto.getSubtaskno() );
        resultTable.setLocation( dto.getLocation() );
        resultTable.setType( dto.getType() );
        resultTable.setDbname( dto.getDbname() );
        resultTable.setTablename( dto.getTablename() );
        resultTable.setLoginnamefield( dto.getLoginnamefield() );
        resultTable.setUsernamefield( dto.getUsernamefield() );
        resultTable.setPwdfield( dto.getPwdfield() );
        resultTable.setWpcount( dto.getWpcount() );
        resultTable.setResulttype( dto.getResulttype() );
        resultTable.setLoginname( dto.getLoginname() );
        resultTable.setUsername( dto.getUsername() );
        resultTable.setPwd( dto.getPwd() );
        resultTable.setCreatetime( dto.getCreatetime() );
        resultTable.setSparefield1( dto.getSparefield1() );
        resultTable.setSparefield2( dto.getSparefield2() );
        resultTable.setSparefield3( dto.getSparefield3() );
        resultTable.setSparefield4( dto.getSparefield4() );
        resultTable.setSparefield5( dto.getSparefield5() );

        return resultTable;
    }

    @Override
    public ResultTableDto toDto(ResultTable entity) {
        if ( entity == null ) {
            return null;
        }

        ResultTableDto resultTableDto = new ResultTableDto();

        resultTableDto.setId( entity.getId() );
        resultTableDto.setTaskno( entity.getTaskno() );
        resultTableDto.setSubtaskno( entity.getSubtaskno() );
        resultTableDto.setLocation( entity.getLocation() );
        resultTableDto.setType( entity.getType() );
        resultTableDto.setDbname( entity.getDbname() );
        resultTableDto.setTablename( entity.getTablename() );
        resultTableDto.setLoginnamefield( entity.getLoginnamefield() );
        resultTableDto.setUsernamefield( entity.getUsernamefield() );
        resultTableDto.setPwdfield( entity.getPwdfield() );
        resultTableDto.setWpcount( entity.getWpcount() );
        resultTableDto.setResulttype( entity.getResulttype() );
        resultTableDto.setLoginname( entity.getLoginname() );
        resultTableDto.setUsername( entity.getUsername() );
        resultTableDto.setPwd( entity.getPwd() );
        resultTableDto.setCreatetime( entity.getCreatetime() );
        resultTableDto.setSparefield1( entity.getSparefield1() );
        resultTableDto.setSparefield2( entity.getSparefield2() );
        resultTableDto.setSparefield3( entity.getSparefield3() );
        resultTableDto.setSparefield4( entity.getSparefield4() );
        resultTableDto.setSparefield5( entity.getSparefield5() );

        return resultTableDto;
    }

    @Override
    public List<ResultTable> toEntity(List<ResultTableDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ResultTable> list = new ArrayList<ResultTable>( dtoList.size() );
        for ( ResultTableDto resultTableDto : dtoList ) {
            list.add( toEntity( resultTableDto ) );
        }

        return list;
    }

    @Override
    public List<ResultTableDto> toDto(List<ResultTable> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ResultTableDto> list = new ArrayList<ResultTableDto>( entityList.size() );
        for ( ResultTable resultTable : entityList ) {
            list.add( toDto( resultTable ) );
        }

        return list;
    }
}
