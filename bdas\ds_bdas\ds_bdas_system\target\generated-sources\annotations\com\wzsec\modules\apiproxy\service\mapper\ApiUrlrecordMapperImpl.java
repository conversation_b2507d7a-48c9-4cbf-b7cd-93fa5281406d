package com.wzsec.modules.apiproxy.service.mapper;

import com.wzsec.modules.apiproxy.domain.ApiUrlrecord;
import com.wzsec.modules.apiproxy.service.dto.ApiUrlrecordDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:26:45+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ApiUrlrecordMapperImpl implements ApiUrlrecordMapper {

    @Override
    public ApiUrlrecordDto toDto(ApiUrlrecord entity) {
        if ( entity == null ) {
            return null;
        }

        ApiUrlrecordDto apiUrlrecordDto = new ApiUrlrecordDto();

        apiUrlrecordDto.setHandletime( entity.getHandletime() );
        apiUrlrecordDto.setId( entity.getId() );
        apiUrlrecordDto.setInserttime( entity.getInserttime() );
        apiUrlrecordDto.setIsmask( entity.getIsmask() );
        apiUrlrecordDto.setMaskdatalength( entity.getMaskdatalength() );
        apiUrlrecordDto.setMaskparam( entity.getMaskparam() );
        apiUrlrecordDto.setMaskstatus( entity.getMaskstatus() );
        apiUrlrecordDto.setRequesprotocol( entity.getRequesprotocol() );
        apiUrlrecordDto.setRequestip( entity.getRequestip() );
        apiUrlrecordDto.setRequestmethond( entity.getRequestmethond() );
        apiUrlrecordDto.setRequestparam( entity.getRequestparam() );
        apiUrlrecordDto.setRequesttime( entity.getRequesttime() );
        apiUrlrecordDto.setRequesturl( entity.getRequesturl() );
        apiUrlrecordDto.setResponsedatalength( entity.getResponsedatalength() );
        apiUrlrecordDto.setResponseparam( entity.getResponseparam() );
        apiUrlrecordDto.setServerapi( entity.getServerapi() );
        apiUrlrecordDto.setServerhost( entity.getServerhost() );
        apiUrlrecordDto.setServerport( entity.getServerport() );
        apiUrlrecordDto.setServerurl( entity.getServerurl() );
        apiUrlrecordDto.setSparefield1( entity.getSparefield1() );
        apiUrlrecordDto.setSparefield2( entity.getSparefield2() );
        apiUrlrecordDto.setSparefield3( entity.getSparefield3() );
        apiUrlrecordDto.setSparefield4( entity.getSparefield4() );
        apiUrlrecordDto.setSparefield5( entity.getSparefield5() );

        return apiUrlrecordDto;
    }

    @Override
    public List<ApiUrlrecordDto> toDto(List<ApiUrlrecord> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiUrlrecordDto> list = new ArrayList<ApiUrlrecordDto>( entityList.size() );
        for ( ApiUrlrecord apiUrlrecord : entityList ) {
            list.add( toDto( apiUrlrecord ) );
        }

        return list;
    }

    @Override
    public ApiUrlrecord toEntity(ApiUrlrecordDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiUrlrecord apiUrlrecord = new ApiUrlrecord();

        apiUrlrecord.setHandletime( dto.getHandletime() );
        apiUrlrecord.setId( dto.getId() );
        apiUrlrecord.setInserttime( dto.getInserttime() );
        apiUrlrecord.setIsmask( dto.getIsmask() );
        apiUrlrecord.setMaskdatalength( dto.getMaskdatalength() );
        apiUrlrecord.setMaskparam( dto.getMaskparam() );
        apiUrlrecord.setMaskstatus( dto.getMaskstatus() );
        apiUrlrecord.setRequesprotocol( dto.getRequesprotocol() );
        apiUrlrecord.setRequestip( dto.getRequestip() );
        apiUrlrecord.setRequestmethond( dto.getRequestmethond() );
        apiUrlrecord.setRequestparam( dto.getRequestparam() );
        apiUrlrecord.setRequesttime( dto.getRequesttime() );
        apiUrlrecord.setRequesturl( dto.getRequesturl() );
        apiUrlrecord.setResponsedatalength( dto.getResponsedatalength() );
        apiUrlrecord.setResponseparam( dto.getResponseparam() );
        apiUrlrecord.setServerapi( dto.getServerapi() );
        apiUrlrecord.setServerhost( dto.getServerhost() );
        apiUrlrecord.setServerport( dto.getServerport() );
        apiUrlrecord.setServerurl( dto.getServerurl() );
        apiUrlrecord.setSparefield1( dto.getSparefield1() );
        apiUrlrecord.setSparefield2( dto.getSparefield2() );
        apiUrlrecord.setSparefield3( dto.getSparefield3() );
        apiUrlrecord.setSparefield4( dto.getSparefield4() );
        apiUrlrecord.setSparefield5( dto.getSparefield5() );

        return apiUrlrecord;
    }

    @Override
    public List<ApiUrlrecord> toEntity(List<ApiUrlrecordDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiUrlrecord> list = new ArrayList<ApiUrlrecord>( dtoList.size() );
        for ( ApiUrlrecordDto apiUrlrecordDto : dtoList ) {
            list.add( toEntity( apiUrlrecordDto ) );
        }

        return list;
    }
}
