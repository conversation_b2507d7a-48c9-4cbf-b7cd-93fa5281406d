package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.ApiFlowcollectionsizeresult;
import com.wzsec.modules.ic.service.dto.ApiFlowcollectionsizeresultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:27:07+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ApiFlowcollectionsizeresultMapperImpl implements ApiFlowcollectionsizeresultMapper {

    @Override
    public ApiFlowcollectionsizeresultDto toDto(ApiFlowcollectionsizeresult entity) {
        if ( entity == null ) {
            return null;
        }

        ApiFlowcollectionsizeresultDto apiFlowcollectionsizeresultDto = new ApiFlowcollectionsizeresultDto();

        apiFlowcollectionsizeresultDto.setAcquisitiontime( entity.getAcquisitiontime() );
        apiFlowcollectionsizeresultDto.setAvgsize( entity.getAvgsize() );
        apiFlowcollectionsizeresultDto.setCreatetime( entity.getCreatetime() );
        apiFlowcollectionsizeresultDto.setFilesize( entity.getFilesize() );
        apiFlowcollectionsizeresultDto.setId( entity.getId() );
        apiFlowcollectionsizeresultDto.setRisk( entity.getRisk() );
        apiFlowcollectionsizeresultDto.setServerip( entity.getServerip() );
        apiFlowcollectionsizeresultDto.setServerport( entity.getServerport() );
        apiFlowcollectionsizeresultDto.setSparefield1( entity.getSparefield1() );
        apiFlowcollectionsizeresultDto.setSparefield2( entity.getSparefield2() );
        apiFlowcollectionsizeresultDto.setSparefield3( entity.getSparefield3() );
        apiFlowcollectionsizeresultDto.setSparefield4( entity.getSparefield4() );
        apiFlowcollectionsizeresultDto.setSparefield5( entity.getSparefield5() );

        return apiFlowcollectionsizeresultDto;
    }

    @Override
    public List<ApiFlowcollectionsizeresultDto> toDto(List<ApiFlowcollectionsizeresult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiFlowcollectionsizeresultDto> list = new ArrayList<ApiFlowcollectionsizeresultDto>( entityList.size() );
        for ( ApiFlowcollectionsizeresult apiFlowcollectionsizeresult : entityList ) {
            list.add( toDto( apiFlowcollectionsizeresult ) );
        }

        return list;
    }

    @Override
    public ApiFlowcollectionsizeresult toEntity(ApiFlowcollectionsizeresultDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiFlowcollectionsizeresult apiFlowcollectionsizeresult = new ApiFlowcollectionsizeresult();

        apiFlowcollectionsizeresult.setAcquisitiontime( dto.getAcquisitiontime() );
        apiFlowcollectionsizeresult.setAvgsize( dto.getAvgsize() );
        apiFlowcollectionsizeresult.setCreatetime( dto.getCreatetime() );
        apiFlowcollectionsizeresult.setFilesize( dto.getFilesize() );
        apiFlowcollectionsizeresult.setId( dto.getId() );
        apiFlowcollectionsizeresult.setRisk( dto.getRisk() );
        apiFlowcollectionsizeresult.setServerip( dto.getServerip() );
        apiFlowcollectionsizeresult.setServerport( dto.getServerport() );
        apiFlowcollectionsizeresult.setSparefield1( dto.getSparefield1() );
        apiFlowcollectionsizeresult.setSparefield2( dto.getSparefield2() );
        apiFlowcollectionsizeresult.setSparefield3( dto.getSparefield3() );
        apiFlowcollectionsizeresult.setSparefield4( dto.getSparefield4() );
        apiFlowcollectionsizeresult.setSparefield5( dto.getSparefield5() );

        return apiFlowcollectionsizeresult;
    }

    @Override
    public List<ApiFlowcollectionsizeresult> toEntity(List<ApiFlowcollectionsizeresultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiFlowcollectionsizeresult> list = new ArrayList<ApiFlowcollectionsizeresult>( dtoList.size() );
        for ( ApiFlowcollectionsizeresultDto apiFlowcollectionsizeresultDto : dtoList ) {
            list.add( toEntity( apiFlowcollectionsizeresultDto ) );
        }

        return list;
    }
}
