// package com.wzsec.utils;
//
// import java.io.IOException;
// import java.security.NoSuchAlgorithmException;
// import java.util.ArrayList;
// import java.util.HashMap;
// import java.util.List;
// import java.util.Map;
//
// import org.apache.hadoop.conf.Configuration;
// import org.apache.hadoop.hbase.Cell;
// import org.apache.hadoop.hbase.CellUtil;
// import org.apache.hadoop.hbase.HBaseConfiguration;
// import org.apache.hadoop.hbase.TableName;
// import org.apache.hadoop.hbase.client.Admin;
// import org.apache.hadoop.hbase.client.Connection;
// import org.apache.hadoop.hbase.client.ConnectionFactory;
// import org.apache.hadoop.hbase.client.Get;
// import org.apache.hadoop.hbase.client.Result;
// import org.apache.hadoop.hbase.client.ResultScanner;
// import org.apache.hadoop.hbase.client.Scan;
// import org.apache.hadoop.hbase.client.Table;
// import org.apache.hadoop.hbase.filter.CompareFilter.CompareOp;
// import org.apache.hadoop.hbase.filter.SubstringComparator;
// import org.apache.hadoop.hbase.filter.ValueFilter;
// import org.apache.hadoop.hbase.util.Bytes;
// import org.apache.log4j.Logger;
// import org.springframework.stereotype.Controller;
//
// @Controller
// public class HBaseUtils {
//
// 	public static Logger log = Logger.getLogger(HBaseUtils.class);
//
// 	/**
// 	 * @Description:连接hbase
// 	 * <AUTHOR>
// 	 * @date 2020年2月26日
// 	 */
// 	public static Connection getConn(String quorum) {
// 		Configuration configuration = HBaseConfiguration.create();
// 		Connection createConnection =null;
// 		// String quorum =
// 		// ConfigurationManager.getProperty("hbase.zookeeper.quorum");
// 		configuration.set("hbase.zookeeper.quorum", quorum);
// 		try {
// 			 createConnection = ConnectionFactory.createConnection(configuration);
// 		} catch (Exception e) {
// 			e.printStackTrace();
// 		}
// 		return createConnection;
// 	}
//
// 	/**
// 	 *@Description: 明文弱密码检测
// 	 *<AUTHOR>
// 	 *@date 2020年2月26日
// 	 */
// 	public static List<String> ClearPWIsExist (Table table,List<String> pwdList) {
// 		List<String> weakpwdList = new ArrayList<String>();
// 		try{
// 			for (String pwd:pwdList) {
// 				Get p = new Get(Bytes.toBytes(pwd));
// 				Result result = table.get(p);
// 				int size = result.size();
// 				if(size>0){
// 					weakpwdList.add(pwd);
// 				}
// 			}
// 		}catch (Exception e) {
// 			e.printStackTrace();
// 		}
// 		return weakpwdList;
// 	}
//
//
//
// 	/**
// 	 *@Description: 加密弱密码检测
// 	 *<AUTHOR>
// 	 *@date 2020年2月26日
// 	 */
// 	public static List<String> MD5PWIsExist (Table table, List<String> pwdList) {
// 		List<String> weakpwdList = new ArrayList<String>();
// 		try{
// 			for (String pwd:pwdList) {
// 				Scan scan = new Scan();
// 				ValueFilter valueFilter = new ValueFilter(CompareOp.EQUAL, new SubstringComparator(pwd));
// 				scan.setFilter(valueFilter);
// 				ResultScanner rs = table.getScanner(scan);
// 				Result next = rs.next();
// 				if(next!=null && next.size()>0){
// 					weakpwdList.add(pwd);
// 				}
// 			}
// 		}catch (Exception e) {
// 			e.printStackTrace();
// 		}
// 		return weakpwdList;
// 	}
//
// 	/**
// 	 *@Description: 明文hbase库弱密码检测
// 	 *<AUTHOR>
// 	 *@date 2020年2月26日
// 	 */
// 	public static List<String> clearAlgMatching (Table table,  List<String> pwdList) {
// 		List<String> weakpwdList = new ArrayList<String>();
// 		try{
// 			Scan scan = new Scan();
// 			ResultScanner rs = table.getScanner(scan);
// 			for(Result r:rs){
// 				for (Cell kv : r.rawCells()) {
// 					String rowkey = Bytes.toString(CellUtil.cloneRow(kv));
// 					if(pwdList.contains(rowkey)){
// 						weakpwdList.add(rowkey);
// 					}
// 					break;
// 				}
// 			}
// 		}catch (Exception e) {
// 			e.printStackTrace();
// 		}
// 		return weakpwdList;
// 	}
//
// 	/**
// 	 *@Description: 密文hbase库弱密码检测
// 	 *<AUTHOR>
// 	 *@date 2020年2月26日
// 	 */
// 	public static List<String> EncAlgMatching (Table table,  List<String> pwdList) {
// 		List<String> weakpwdList = new ArrayList<String>();
// 		try{
// 			Scan scan = new Scan();
// 			ResultScanner rs = table.getScanner(scan);
// 			for(Result r:rs){
// 				for (Cell kv : r.rawCells()) {
// 					String value = Bytes.toString(CellUtil.cloneValue(kv));
// 					if(pwdList.contains(value)){
// 						weakpwdList.add(value);
// 					}
// 				}
// 			}
// 		}catch (Exception e) {
// 			e.printStackTrace();
// 		}
// 		return weakpwdList;
// 	}
//
// 	/**
// 	 *@Description: 加密算法进行检测
// 	 *<AUTHOR>
// 	 *@date 2020年2月26日
// 	 */
// 	public static List<String> CustomAlgIsExist (Table table,  List<String> pwdList, String detecttype) {
// 		List<String> weakpwdList = new ArrayList<String>();
// 		try{
// 			Scan scan = new Scan();
// 			//ValueFilter valueFilter = new ValueFilter(CompareOp.EQUAL, new SubstringComparator(pw));
// 			//scan.setFilter(valueFilter);
// 			ResultScanner rs = table.getScanner(scan);
// 			if(Const.WPRESULT_SUBTASK_ENCRYALG_MD5MD5.equals(detecttype)){
// 				for(Result r:rs){
// 					for (Cell kv : r.rawCells()) {
// 						String rowkey = Bytes.toString(CellUtil.cloneRow(kv));
// 						//md5(md5('www.cmswing.com') + password + md5('Arterli'))
// 						if(pwdList.contains(MD5.md5(MD5.md5("www.cmswing.com")+rowkey+MD5.md5("Arterli")))){
// 							weakpwdList.add(rowkey);
// 						}
// 						break;
// 					}
// 				}
// 			}else if(Const.WPRESULT_SUBTASK_ENCRYALG_MD5_32.equals(detecttype)){
// 				for(Result r:rs){
// 					for (Cell kv : r.rawCells()) {
// 						String rowkey = Bytes.toString(CellUtil.cloneRow(kv));
// 						if(pwdList.contains(MD5.md5(rowkey))){
// 							weakpwdList.add(rowkey);
// 						}
// 						break;
// 					}
// 				}
// 			}else if(Const.WPRESULT_SUBTASK_ENCRYALG_SHA256.equals(detecttype)){
// 				for(Result r:rs){
// 					for (Cell kv : r.rawCells()) {
// 						String rowkey = Bytes.toString(CellUtil.cloneRow(kv));
// 						if(pwdList.contains(SHA256.sha256(rowkey))){
// 							weakpwdList.add(rowkey);
// 						}
// 						break;
// 					}
// 				}
// 			}else if (Const.WPRESULT_SUBTASK_ENCRYALG_MD5_16.equals(detecttype)){
// 				for(Result r:rs){
// 					for (Cell kv : r.rawCells()) {
// 						String rowkey = Bytes.toString(CellUtil.cloneRow(kv));
// 						if(pwdList.contains(MD516BIT.encrypt(rowkey))){
// 							weakpwdList.add(rowkey);
// 						}
// 						break;
// 					}
// 				}
// 			}
//
// 		}catch (Exception e) {
// 			e.printStackTrace();
// 		}
// 		return weakpwdList;
// 	}
//
// 	/**
// 	 *@Description: 获取数据库中所有的库名表名
// 	 *<AUTHOR>
// 	 *@date 2020年2月26日
// 	 */
// 	public static Map<String, String> getAllDbAndTabMap(String url, String username, String password, String dbnames) {
// 		Map<String,String> dbTabMap= new HashMap<>();
// 		Connection conn = getConn(url);
// 		TableName[] tableNames;
// 		try {
// 			Admin admin = conn.getAdmin();
// 			tableNames = admin.listTableNames();
// 			ArrayList<String> tables = new ArrayList<String>();
// 			for (TableName tableName : tableNames) {
// 				dbTabMap.put("default", tableName.getNameAsString());
// 			}
// 		} catch (IOException e) {
// 			e.printStackTrace();
// 		}
// 		return dbTabMap;
// 	}
//
// 	/**
// 	 *@Description: 获取数据库表中所有字段名
// 	 *<AUTHOR>
// 	 *@date 2020年2月26日
// 	 */
// 	public static List<String> getFieldNameList(String url, String username, String password, String dbname,
// 			String tabname) {
// 		List<String> dataList = new ArrayList<String>();
// 		Scan scan = new Scan();
// 		//scan.setMaxResultSize(1);
// 		scan.setMaxResultsPerColumnFamily(1);
// 		Connection conn = getConn(url);
// 		ResultScanner results;
// 		try {
// 			Table table = conn.getTable(TableName.valueOf(tabname));
// 			results = table.getScanner(scan);
// 			for (Result result : results){
// 				Cell[] rawCells = result.rawCells();
// 				for (Cell cell : rawCells) {
// 					String Qualifier = Bytes.toString(CellUtil.cloneQualifier(cell));
// 					//String family = Bytes.toString(CellUtil.cloneFamily(cell));
// 					dataList.add(Qualifier);
// 				}
// 			}
// 		} catch (IOException e) {
// 			e.printStackTrace();
// 		}
// 		return dataList;
// 	}
//
// 	/**
// 	 *@Description: 获取数据库表中前100条数据
// 	 *<AUTHOR>
// 	 *@date 2020年2月26日
// 	 */
// 	public static List<String> getTabDataList(String url, String username, String password, String dbname,
// 			String tabname) {
// 		List<String> dataList = new ArrayList<String>();
// 		Scan scan = new Scan();
// 		//scan.setMaxResultSize(1);
// 		scan.setMaxResultsPerColumnFamily(100);
// 		Connection conn = getConn(url);
// 		ResultScanner results;
// 		try {
// 			Table table = conn.getTable(TableName.valueOf(tabname));
// 			results = table.getScanner(scan);
// 			for (Result result : results){
// 				Cell[] rawCells = result.rawCells();
// 				for (Cell cell : rawCells) {
// 					//String Qualifier = Bytes.toString(CellUtil.cloneQualifier(cell));
// 					String Value = Bytes.toString(CellUtil.cloneValue(cell));
// 					//String family = Bytes.toString(CellUtil.cloneFamily(cell));
// 					dataList.add(Value);
// 				}
// 			}
// 		} catch (IOException e) {
// 			e.printStackTrace();
// 		}
// 		return dataList;
// 	}
//
// 	/**
// 	 *@Description: 获取数据库表数据数量
// 	 *<AUTHOR>
// 	 *@date 2020年2月26日
// 	 */
// 	public static int getTabDataCount(String url, String username, String password, String dbname, String tabname) {
// 		int i = 0;
// 		List<String> dataList = new ArrayList<String>();
// 		Scan scan = new Scan();
// 		Connection conn = getConn(url);
// 		ResultScanner results;
// 		try {
// 			Table table = conn.getTable(TableName.valueOf(tabname));
//
// 			results = table.getScanner(scan);
// 			for (Result result : results){
// 				Cell[] rawCells = result.rawCells();
// 				for (Cell cell : rawCells) {
// 					i++;
// 				}
// 			}
// 		} catch (IOException e) {
// 			e.printStackTrace();
// 		}
// 		return i;
// 	}
//
// 	public static void main(String[] args) {
// 		try {
// 			System.out.println(SHA256.sha256("111"));
// 		} catch (NoSuchAlgorithmException e) {
// 			e.printStackTrace();
// 		}
// 	}
//
//
// }
