package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.ApiAuthcheckresult;
import com.wzsec.modules.ic.service.dto.ApiAuthcheckresultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:26:45+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ApiAuthcheckresultMapperImpl implements ApiAuthcheckresultMapper {

    @Override
    public ApiAuthcheckresultDto toDto(ApiAuthcheckresult entity) {
        if ( entity == null ) {
            return null;
        }

        ApiAuthcheckresultDto apiAuthcheckresultDto = new ApiAuthcheckresultDto();

        apiAuthcheckresultDto.setAk( entity.getAk() );
        apiAuthcheckresultDto.setAkapicode( entity.getAkapicode() );
        apiAuthcheckresultDto.setApicode( entity.getApicode() );
        apiAuthcheckresultDto.setApiname( entity.getApiname() );
        apiAuthcheckresultDto.setApiurl( entity.getApiurl() );
        apiAuthcheckresultDto.setApplyorgname( entity.getApplyorgname() );
        apiAuthcheckresultDto.setAuthinfo( entity.getAuthinfo() );
        apiAuthcheckresultDto.setAuthmethod( entity.getAuthmethod() );
        apiAuthcheckresultDto.setId( entity.getId() );
        apiAuthcheckresultDto.setInserttime( entity.getInserttime() );
        apiAuthcheckresultDto.setProtocol( entity.getProtocol() );
        apiAuthcheckresultDto.setReqformat( entity.getReqformat() );
        apiAuthcheckresultDto.setReqip( entity.getReqip() );
        apiAuthcheckresultDto.setReqmethod( entity.getReqmethod() );
        apiAuthcheckresultDto.setRisk( entity.getRisk() );
        apiAuthcheckresultDto.setServiceip( entity.getServiceip() );
        apiAuthcheckresultDto.setServiceport( entity.getServiceport() );
        apiAuthcheckresultDto.setSparefield1( entity.getSparefield1() );
        apiAuthcheckresultDto.setSparefield2( entity.getSparefield2() );
        apiAuthcheckresultDto.setSparefield3( entity.getSparefield3() );
        apiAuthcheckresultDto.setSparefield4( entity.getSparefield4() );
        apiAuthcheckresultDto.setSystemname( entity.getSystemname() );
        apiAuthcheckresultDto.setUrl( entity.getUrl() );

        return apiAuthcheckresultDto;
    }

    @Override
    public List<ApiAuthcheckresultDto> toDto(List<ApiAuthcheckresult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiAuthcheckresultDto> list = new ArrayList<ApiAuthcheckresultDto>( entityList.size() );
        for ( ApiAuthcheckresult apiAuthcheckresult : entityList ) {
            list.add( toDto( apiAuthcheckresult ) );
        }

        return list;
    }

    @Override
    public ApiAuthcheckresult toEntity(ApiAuthcheckresultDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiAuthcheckresult apiAuthcheckresult = new ApiAuthcheckresult();

        apiAuthcheckresult.setAk( dto.getAk() );
        apiAuthcheckresult.setAkapicode( dto.getAkapicode() );
        apiAuthcheckresult.setApicode( dto.getApicode() );
        apiAuthcheckresult.setApiname( dto.getApiname() );
        apiAuthcheckresult.setApiurl( dto.getApiurl() );
        apiAuthcheckresult.setApplyorgname( dto.getApplyorgname() );
        apiAuthcheckresult.setAuthinfo( dto.getAuthinfo() );
        apiAuthcheckresult.setAuthmethod( dto.getAuthmethod() );
        apiAuthcheckresult.setId( dto.getId() );
        apiAuthcheckresult.setInserttime( dto.getInserttime() );
        apiAuthcheckresult.setProtocol( dto.getProtocol() );
        apiAuthcheckresult.setReqformat( dto.getReqformat() );
        apiAuthcheckresult.setReqip( dto.getReqip() );
        apiAuthcheckresult.setReqmethod( dto.getReqmethod() );
        apiAuthcheckresult.setRisk( dto.getRisk() );
        apiAuthcheckresult.setServiceip( dto.getServiceip() );
        apiAuthcheckresult.setServiceport( dto.getServiceport() );
        apiAuthcheckresult.setSparefield1( dto.getSparefield1() );
        apiAuthcheckresult.setSparefield2( dto.getSparefield2() );
        apiAuthcheckresult.setSparefield3( dto.getSparefield3() );
        apiAuthcheckresult.setSparefield4( dto.getSparefield4() );
        apiAuthcheckresult.setSystemname( dto.getSystemname() );
        apiAuthcheckresult.setUrl( dto.getUrl() );

        return apiAuthcheckresult;
    }

    @Override
    public List<ApiAuthcheckresult> toEntity(List<ApiAuthcheckresultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiAuthcheckresult> list = new ArrayList<ApiAuthcheckresult>( dtoList.size() );
        for ( ApiAuthcheckresultDto apiAuthcheckresultDto : dtoList ) {
            list.add( toEntity( apiAuthcheckresultDto ) );
        }

        return list;
    }
}
