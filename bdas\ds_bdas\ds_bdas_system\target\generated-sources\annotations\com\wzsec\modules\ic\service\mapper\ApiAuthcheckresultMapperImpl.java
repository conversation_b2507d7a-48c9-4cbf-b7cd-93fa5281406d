package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.ApiAuthcheckresult;
import com.wzsec.modules.ic.service.dto.ApiAuthcheckresultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:30+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ApiAuthcheckresultMapperImpl implements ApiAuthcheckresultMapper {

    @Override
    public ApiAuthcheckresult toEntity(ApiAuthcheckresultDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiAuthcheckresult apiAuthcheckresult = new ApiAuthcheckresult();

        apiAuthcheckresult.setId( dto.getId() );
        apiAuthcheckresult.setApicode( dto.getApicode() );
        apiAuthcheckresult.setApiname( dto.getApiname() );
        apiAuthcheckresult.setUrl( dto.getUrl() );
        apiAuthcheckresult.setServiceip( dto.getServiceip() );
        apiAuthcheckresult.setServiceport( dto.getServiceport() );
        apiAuthcheckresult.setProtocol( dto.getProtocol() );
        apiAuthcheckresult.setReqmethod( dto.getReqmethod() );
        apiAuthcheckresult.setReqformat( dto.getReqformat() );
        apiAuthcheckresult.setAuthmethod( dto.getAuthmethod() );
        apiAuthcheckresult.setAuthinfo( dto.getAuthinfo() );
        apiAuthcheckresult.setRisk( dto.getRisk() );
        apiAuthcheckresult.setInserttime( dto.getInserttime() );
        apiAuthcheckresult.setSparefield1( dto.getSparefield1() );
        apiAuthcheckresult.setSparefield2( dto.getSparefield2() );
        apiAuthcheckresult.setSparefield3( dto.getSparefield3() );
        apiAuthcheckresult.setSparefield4( dto.getSparefield4() );
        apiAuthcheckresult.setApiurl( dto.getApiurl() );
        apiAuthcheckresult.setAk( dto.getAk() );
        apiAuthcheckresult.setApplyorgname( dto.getApplyorgname() );
        apiAuthcheckresult.setAkapicode( dto.getAkapicode() );
        apiAuthcheckresult.setSystemname( dto.getSystemname() );
        apiAuthcheckresult.setReqip( dto.getReqip() );

        return apiAuthcheckresult;
    }

    @Override
    public ApiAuthcheckresultDto toDto(ApiAuthcheckresult entity) {
        if ( entity == null ) {
            return null;
        }

        ApiAuthcheckresultDto apiAuthcheckresultDto = new ApiAuthcheckresultDto();

        apiAuthcheckresultDto.setId( entity.getId() );
        apiAuthcheckresultDto.setApicode( entity.getApicode() );
        apiAuthcheckresultDto.setApiname( entity.getApiname() );
        apiAuthcheckresultDto.setUrl( entity.getUrl() );
        apiAuthcheckresultDto.setServiceip( entity.getServiceip() );
        apiAuthcheckresultDto.setServiceport( entity.getServiceport() );
        apiAuthcheckresultDto.setProtocol( entity.getProtocol() );
        apiAuthcheckresultDto.setReqmethod( entity.getReqmethod() );
        apiAuthcheckresultDto.setReqformat( entity.getReqformat() );
        apiAuthcheckresultDto.setAuthmethod( entity.getAuthmethod() );
        apiAuthcheckresultDto.setAuthinfo( entity.getAuthinfo() );
        apiAuthcheckresultDto.setRisk( entity.getRisk() );
        apiAuthcheckresultDto.setInserttime( entity.getInserttime() );
        apiAuthcheckresultDto.setSparefield1( entity.getSparefield1() );
        apiAuthcheckresultDto.setSparefield2( entity.getSparefield2() );
        apiAuthcheckresultDto.setSparefield3( entity.getSparefield3() );
        apiAuthcheckresultDto.setSparefield4( entity.getSparefield4() );
        apiAuthcheckresultDto.setApiurl( entity.getApiurl() );
        apiAuthcheckresultDto.setAk( entity.getAk() );
        apiAuthcheckresultDto.setApplyorgname( entity.getApplyorgname() );
        apiAuthcheckresultDto.setAkapicode( entity.getAkapicode() );
        apiAuthcheckresultDto.setSystemname( entity.getSystemname() );
        apiAuthcheckresultDto.setReqip( entity.getReqip() );

        return apiAuthcheckresultDto;
    }

    @Override
    public List<ApiAuthcheckresult> toEntity(List<ApiAuthcheckresultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiAuthcheckresult> list = new ArrayList<ApiAuthcheckresult>( dtoList.size() );
        for ( ApiAuthcheckresultDto apiAuthcheckresultDto : dtoList ) {
            list.add( toEntity( apiAuthcheckresultDto ) );
        }

        return list;
    }

    @Override
    public List<ApiAuthcheckresultDto> toDto(List<ApiAuthcheckresult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiAuthcheckresultDto> list = new ArrayList<ApiAuthcheckresultDto>( entityList.size() );
        for ( ApiAuthcheckresult apiAuthcheckresult : entityList ) {
            list.add( toDto( apiAuthcheckresult ) );
        }

        return list;
    }
}
