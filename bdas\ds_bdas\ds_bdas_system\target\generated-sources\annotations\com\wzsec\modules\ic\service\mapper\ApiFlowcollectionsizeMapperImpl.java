package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.ApiFlowcollectionsize;
import com.wzsec.modules.ic.service.dto.ApiFlowcollectionsizeDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:28+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ApiFlowcollectionsizeMapperImpl implements ApiFlowcollectionsizeMapper {

    @Override
    public ApiFlowcollectionsizeDto toDto(ApiFlowcollectionsize entity) {
        if ( entity == null ) {
            return null;
        }

        ApiFlowcollectionsizeDto apiFlowcollectionsizeDto = new ApiFlowcollectionsizeDto();

        apiFlowcollectionsizeDto.setAcquisitiontime( entity.getAcquisitiontime() );
        apiFlowcollectionsizeDto.setCreatetime( entity.getCreatetime() );
        apiFlowcollectionsizeDto.setFilename( entity.getFilename() );
        apiFlowcollectionsizeDto.setFilesize( entity.getFilesize() );
        apiFlowcollectionsizeDto.setId( entity.getId() );
        apiFlowcollectionsizeDto.setServerip( entity.getServerip() );
        apiFlowcollectionsizeDto.setServerport( entity.getServerport() );
        apiFlowcollectionsizeDto.setSparefield1( entity.getSparefield1() );
        apiFlowcollectionsizeDto.setSparefield2( entity.getSparefield2() );
        apiFlowcollectionsizeDto.setSparefield3( entity.getSparefield3() );
        apiFlowcollectionsizeDto.setSparefield4( entity.getSparefield4() );
        apiFlowcollectionsizeDto.setSparefield5( entity.getSparefield5() );

        return apiFlowcollectionsizeDto;
    }

    @Override
    public List<ApiFlowcollectionsizeDto> toDto(List<ApiFlowcollectionsize> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiFlowcollectionsizeDto> list = new ArrayList<ApiFlowcollectionsizeDto>( entityList.size() );
        for ( ApiFlowcollectionsize apiFlowcollectionsize : entityList ) {
            list.add( toDto( apiFlowcollectionsize ) );
        }

        return list;
    }

    @Override
    public ApiFlowcollectionsize toEntity(ApiFlowcollectionsizeDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiFlowcollectionsize apiFlowcollectionsize = new ApiFlowcollectionsize();

        apiFlowcollectionsize.setAcquisitiontime( dto.getAcquisitiontime() );
        apiFlowcollectionsize.setCreatetime( dto.getCreatetime() );
        apiFlowcollectionsize.setFilename( dto.getFilename() );
        apiFlowcollectionsize.setFilesize( dto.getFilesize() );
        apiFlowcollectionsize.setId( dto.getId() );
        apiFlowcollectionsize.setServerip( dto.getServerip() );
        apiFlowcollectionsize.setServerport( dto.getServerport() );
        apiFlowcollectionsize.setSparefield1( dto.getSparefield1() );
        apiFlowcollectionsize.setSparefield2( dto.getSparefield2() );
        apiFlowcollectionsize.setSparefield3( dto.getSparefield3() );
        apiFlowcollectionsize.setSparefield4( dto.getSparefield4() );
        apiFlowcollectionsize.setSparefield5( dto.getSparefield5() );

        return apiFlowcollectionsize;
    }

    @Override
    public List<ApiFlowcollectionsize> toEntity(List<ApiFlowcollectionsizeDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiFlowcollectionsize> list = new ArrayList<ApiFlowcollectionsize>( dtoList.size() );
        for ( ApiFlowcollectionsizeDto apiFlowcollectionsizeDto : dtoList ) {
            list.add( toEntity( apiFlowcollectionsizeDto ) );
        }

        return list;
    }
}
