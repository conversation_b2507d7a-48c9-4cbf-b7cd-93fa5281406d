package com.wzsec.modules.system.service.mapper;

import com.wzsec.modules.system.domain.Dict;
import com.wzsec.modules.system.domain.DictDetail;
import com.wzsec.modules.system.service.dto.DictDetailDto;
import com.wzsec.modules.system.service.dto.DictDto;
import com.wzsec.modules.system.service.dto.DictSmallDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:26:47+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class DictMapperImpl implements DictMapper {

    @Override
    public DictDto toDto(Dict entity) {
        if ( entity == null ) {
            return null;
        }

        DictDto dictDto = new DictDto();

        dictDto.setCreateTime( entity.getCreateTime() );
        dictDto.setDictDetails( dictDetailListToDictDetailDtoList( entity.getDictDetails() ) );
        dictDto.setId( entity.getId() );
        dictDto.setName( entity.getName() );
        dictDto.setRemark( entity.getRemark() );

        return dictDto;
    }

    @Override
    public List<DictDto> toDto(List<Dict> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<DictDto> list = new ArrayList<DictDto>( entityList.size() );
        for ( Dict dict : entityList ) {
            list.add( toDto( dict ) );
        }

        return list;
    }

    @Override
    public Dict toEntity(DictDto dto) {
        if ( dto == null ) {
            return null;
        }

        Dict dict = new Dict();

        dict.setCreateTime( dto.getCreateTime() );
        dict.setDictDetails( dictDetailDtoListToDictDetailList( dto.getDictDetails() ) );
        dict.setId( dto.getId() );
        dict.setName( dto.getName() );
        dict.setRemark( dto.getRemark() );

        return dict;
    }

    @Override
    public List<Dict> toEntity(List<DictDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Dict> list = new ArrayList<Dict>( dtoList.size() );
        for ( DictDto dictDto : dtoList ) {
            list.add( toEntity( dictDto ) );
        }

        return list;
    }

    protected DictSmallDto dictToDictSmallDto(Dict dict) {
        if ( dict == null ) {
            return null;
        }

        DictSmallDto dictSmallDto = new DictSmallDto();

        dictSmallDto.setId( dict.getId() );

        return dictSmallDto;
    }

    protected DictDetailDto dictDetailToDictDetailDto(DictDetail dictDetail) {
        if ( dictDetail == null ) {
            return null;
        }

        DictDetailDto dictDetailDto = new DictDetailDto();

        dictDetailDto.setCreateTime( dictDetail.getCreateTime() );
        dictDetailDto.setDict( dictToDictSmallDto( dictDetail.getDict() ) );
        dictDetailDto.setId( dictDetail.getId() );
        dictDetailDto.setLabel( dictDetail.getLabel() );
        dictDetailDto.setSort( dictDetail.getSort() );
        dictDetailDto.setValue( dictDetail.getValue() );

        return dictDetailDto;
    }

    protected List<DictDetailDto> dictDetailListToDictDetailDtoList(List<DictDetail> list) {
        if ( list == null ) {
            return null;
        }

        List<DictDetailDto> list1 = new ArrayList<DictDetailDto>( list.size() );
        for ( DictDetail dictDetail : list ) {
            list1.add( dictDetailToDictDetailDto( dictDetail ) );
        }

        return list1;
    }

    protected Dict dictSmallDtoToDict(DictSmallDto dictSmallDto) {
        if ( dictSmallDto == null ) {
            return null;
        }

        Dict dict = new Dict();

        dict.setId( dictSmallDto.getId() );

        return dict;
    }

    protected DictDetail dictDetailDtoToDictDetail(DictDetailDto dictDetailDto) {
        if ( dictDetailDto == null ) {
            return null;
        }

        DictDetail dictDetail = new DictDetail();

        dictDetail.setCreateTime( dictDetailDto.getCreateTime() );
        dictDetail.setDict( dictSmallDtoToDict( dictDetailDto.getDict() ) );
        dictDetail.setId( dictDetailDto.getId() );
        dictDetail.setLabel( dictDetailDto.getLabel() );
        dictDetail.setSort( dictDetailDto.getSort() );
        dictDetail.setValue( dictDetailDto.getValue() );

        return dictDetail;
    }

    protected List<DictDetail> dictDetailDtoListToDictDetailList(List<DictDetailDto> list) {
        if ( list == null ) {
            return null;
        }

        List<DictDetail> list1 = new ArrayList<DictDetail>( list.size() );
        for ( DictDetailDto dictDetailDto : list ) {
            list1.add( dictDetailDtoToDictDetail( dictDetailDto ) );
        }

        return list1;
    }
}
