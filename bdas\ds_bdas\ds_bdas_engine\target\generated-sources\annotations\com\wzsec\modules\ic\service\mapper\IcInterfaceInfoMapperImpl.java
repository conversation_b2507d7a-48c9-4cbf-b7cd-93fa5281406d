package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcInterfaceInfo;
import com.wzsec.modules.ic.service.dto.IcInterfaceInfoDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:27:08+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class IcInterfaceInfoMapperImpl implements IcInterfaceInfoMapper {

    @Override
    public IcInterfaceInfoDto toDto(IcInterfaceInfo entity) {
        if ( entity == null ) {
            return null;
        }

        IcInterfaceInfoDto icInterfaceInfoDto = new IcInterfaceInfoDto();

        icInterfaceInfoDto.setApicode( entity.getApicode() );
        icInterfaceInfoDto.setApicomponenttype( entity.getApicomponenttype() );
        icInterfaceInfoDto.setApides( entity.getApides() );
        icInterfaceInfoDto.setApiip( entity.getApiip() );
        icInterfaceInfoDto.setApimethod( entity.getApimethod() );
        icInterfaceInfoDto.setApiname( entity.getApiname() );
        icInterfaceInfoDto.setApiport( entity.getApiport() );
        icInterfaceInfoDto.setApitype( entity.getApitype() );
        icInterfaceInfoDto.setApiuse( entity.getApiuse() );
        icInterfaceInfoDto.setApp( entity.getApp() );
        icInterfaceInfoDto.setArea( entity.getArea() );
        icInterfaceInfoDto.setCreatetime( entity.getCreatetime() );
        icInterfaceInfoDto.setCreateuser( entity.getCreateuser() );
        icInterfaceInfoDto.setDataFormat( entity.getDataFormat() );
        icInterfaceInfoDto.setDataSource( entity.getDataSource() );
        icInterfaceInfoDto.setDataSourceMoziOrgId( entity.getDataSourceMoziOrgId() );
        icInterfaceInfoDto.setDeleted( entity.getDeleted() );
        icInterfaceInfoDto.setFailreturn( entity.getFailreturn() );
        icInterfaceInfoDto.setId( entity.getId() );
        icInterfaceInfoDto.setImportance( entity.getImportance() );
        icInterfaceInfoDto.setInparamMean( entity.getInparamMean() );
        icInterfaceInfoDto.setInputparams( entity.getInputparams() );
        icInterfaceInfoDto.setInterfaceStatus( entity.getInterfaceStatus() );
        icInterfaceInfoDto.setInvokeType( entity.getInvokeType() );
        icInterfaceInfoDto.setLabels( entity.getLabels() );
        icInterfaceInfoDto.setLinkMan( entity.getLinkMan() );
        icInterfaceInfoDto.setLinkTel( entity.getLinkTel() );
        icInterfaceInfoDto.setLogsign( entity.getLogsign() );
        icInterfaceInfoDto.setNote( entity.getNote() );
        icInterfaceInfoDto.setOutparamMean( entity.getOutparamMean() );
        icInterfaceInfoDto.setOutputparams( entity.getOutputparams() );
        icInterfaceInfoDto.setRegisterUnit( entity.getRegisterUnit() );
        icInterfaceInfoDto.setRegisterUnitMoziOrgId( entity.getRegisterUnitMoziOrgId() );
        icInterfaceInfoDto.setReqExample( entity.getReqExample() );
        icInterfaceInfoDto.setResourceid( entity.getResourceid() );
        icInterfaceInfoDto.setReturnExample( entity.getReturnExample() );
        icInterfaceInfoDto.setSparefield1( entity.getSparefield1() );
        icInterfaceInfoDto.setSparefield2( entity.getSparefield2() );
        icInterfaceInfoDto.setSparefield3( entity.getSparefield3() );
        icInterfaceInfoDto.setSparefield4( entity.getSparefield4() );
        icInterfaceInfoDto.setSyncstate( entity.getSyncstate() );
        icInterfaceInfoDto.setUpdatetime( entity.getUpdatetime() );
        icInterfaceInfoDto.setUpdateuser( entity.getUpdateuser() );
        icInterfaceInfoDto.setUrl( entity.getUrl() );

        return icInterfaceInfoDto;
    }

    @Override
    public List<IcInterfaceInfoDto> toDto(List<IcInterfaceInfo> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcInterfaceInfoDto> list = new ArrayList<IcInterfaceInfoDto>( entityList.size() );
        for ( IcInterfaceInfo icInterfaceInfo : entityList ) {
            list.add( toDto( icInterfaceInfo ) );
        }

        return list;
    }

    @Override
    public IcInterfaceInfo toEntity(IcInterfaceInfoDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcInterfaceInfo icInterfaceInfo = new IcInterfaceInfo();

        icInterfaceInfo.setApicode( dto.getApicode() );
        icInterfaceInfo.setApicomponenttype( dto.getApicomponenttype() );
        icInterfaceInfo.setApides( dto.getApides() );
        icInterfaceInfo.setApiip( dto.getApiip() );
        icInterfaceInfo.setApimethod( dto.getApimethod() );
        icInterfaceInfo.setApiname( dto.getApiname() );
        icInterfaceInfo.setApiport( dto.getApiport() );
        icInterfaceInfo.setApitype( dto.getApitype() );
        icInterfaceInfo.setApiuse( dto.getApiuse() );
        icInterfaceInfo.setApp( dto.getApp() );
        icInterfaceInfo.setArea( dto.getArea() );
        icInterfaceInfo.setCreatetime( dto.getCreatetime() );
        icInterfaceInfo.setCreateuser( dto.getCreateuser() );
        icInterfaceInfo.setDataFormat( dto.getDataFormat() );
        icInterfaceInfo.setDataSource( dto.getDataSource() );
        icInterfaceInfo.setDataSourceMoziOrgId( dto.getDataSourceMoziOrgId() );
        icInterfaceInfo.setDeleted( dto.getDeleted() );
        icInterfaceInfo.setFailreturn( dto.getFailreturn() );
        icInterfaceInfo.setId( dto.getId() );
        icInterfaceInfo.setImportance( dto.getImportance() );
        icInterfaceInfo.setInparamMean( dto.getInparamMean() );
        icInterfaceInfo.setInputparams( dto.getInputparams() );
        icInterfaceInfo.setInterfaceStatus( dto.getInterfaceStatus() );
        icInterfaceInfo.setInvokeType( dto.getInvokeType() );
        icInterfaceInfo.setLabels( dto.getLabels() );
        icInterfaceInfo.setLinkMan( dto.getLinkMan() );
        icInterfaceInfo.setLinkTel( dto.getLinkTel() );
        icInterfaceInfo.setLogsign( dto.getLogsign() );
        icInterfaceInfo.setNote( dto.getNote() );
        icInterfaceInfo.setOutparamMean( dto.getOutparamMean() );
        icInterfaceInfo.setOutputparams( dto.getOutputparams() );
        icInterfaceInfo.setRegisterUnit( dto.getRegisterUnit() );
        icInterfaceInfo.setRegisterUnitMoziOrgId( dto.getRegisterUnitMoziOrgId() );
        icInterfaceInfo.setReqExample( dto.getReqExample() );
        icInterfaceInfo.setResourceid( dto.getResourceid() );
        icInterfaceInfo.setReturnExample( dto.getReturnExample() );
        icInterfaceInfo.setSparefield1( dto.getSparefield1() );
        icInterfaceInfo.setSparefield2( dto.getSparefield2() );
        icInterfaceInfo.setSparefield3( dto.getSparefield3() );
        icInterfaceInfo.setSparefield4( dto.getSparefield4() );
        icInterfaceInfo.setSyncstate( dto.getSyncstate() );
        icInterfaceInfo.setUpdatetime( dto.getUpdatetime() );
        icInterfaceInfo.setUpdateuser( dto.getUpdateuser() );
        icInterfaceInfo.setUrl( dto.getUrl() );

        return icInterfaceInfo;
    }

    @Override
    public List<IcInterfaceInfo> toEntity(List<IcInterfaceInfoDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcInterfaceInfo> list = new ArrayList<IcInterfaceInfo>( dtoList.size() );
        for ( IcInterfaceInfoDto icInterfaceInfoDto : dtoList ) {
            list.add( toEntity( icInterfaceInfoDto ) );
        }

        return list;
    }
}
