package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcCallschedule;
import com.wzsec.modules.ic.service.dto.IcCallscheduleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:01+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class IcCallscheduleMapperImpl implements IcCallscheduleMapper {

    @Override
    public IcCallschedule toEntity(IcCallscheduleDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcCallschedule icCallschedule = new IcCallschedule();

        icCallschedule.setId( dto.getId() );
        icCallschedule.setApicode( dto.getApicode() );
        icCallschedule.setCalltime( dto.getCalltime() );
        icCallschedule.setTimeframe( dto.getTimeframe() );
        icCallschedule.setCalldate( dto.getCalldate() );
        icCallschedule.setAppkey( dto.getAppkey() );
        icCallschedule.setIp( dto.getIp() );
        icCallschedule.setSparefield1( dto.getSparefield1() );
        icCallschedule.setSparefield2( dto.getSparefield2() );
        icCallschedule.setSparefield3( dto.getSparefield3() );
        icCallschedule.setSparefield4( dto.getSparefield4() );

        return icCallschedule;
    }

    @Override
    public IcCallscheduleDto toDto(IcCallschedule entity) {
        if ( entity == null ) {
            return null;
        }

        IcCallscheduleDto icCallscheduleDto = new IcCallscheduleDto();

        icCallscheduleDto.setId( entity.getId() );
        icCallscheduleDto.setApicode( entity.getApicode() );
        icCallscheduleDto.setCalltime( entity.getCalltime() );
        icCallscheduleDto.setTimeframe( entity.getTimeframe() );
        icCallscheduleDto.setCalldate( entity.getCalldate() );
        icCallscheduleDto.setSparefield1( entity.getSparefield1() );
        icCallscheduleDto.setSparefield2( entity.getSparefield2() );
        icCallscheduleDto.setSparefield3( entity.getSparefield3() );
        icCallscheduleDto.setSparefield4( entity.getSparefield4() );
        icCallscheduleDto.setAppkey( entity.getAppkey() );
        icCallscheduleDto.setIp( entity.getIp() );

        return icCallscheduleDto;
    }

    @Override
    public List<IcCallschedule> toEntity(List<IcCallscheduleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcCallschedule> list = new ArrayList<IcCallschedule>( dtoList.size() );
        for ( IcCallscheduleDto icCallscheduleDto : dtoList ) {
            list.add( toEntity( icCallscheduleDto ) );
        }

        return list;
    }

    @Override
    public List<IcCallscheduleDto> toDto(List<IcCallschedule> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcCallscheduleDto> list = new ArrayList<IcCallscheduleDto>( entityList.size() );
        for ( IcCallschedule icCallschedule : entityList ) {
            list.add( toDto( icCallschedule ) );
        }

        return list;
    }
}
