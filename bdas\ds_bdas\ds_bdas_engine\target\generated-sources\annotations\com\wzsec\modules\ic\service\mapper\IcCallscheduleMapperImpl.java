package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcCallschedule;
import com.wzsec.modules.ic.service.dto.IcCallscheduleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:27:07+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class IcCallscheduleMapperImpl implements IcCallscheduleMapper {

    @Override
    public IcCallscheduleDto toDto(IcCallschedule entity) {
        if ( entity == null ) {
            return null;
        }

        IcCallscheduleDto icCallscheduleDto = new IcCallscheduleDto();

        icCallscheduleDto.setApicode( entity.getApicode() );
        icCallscheduleDto.setAppkey( entity.getAppkey() );
        icCallscheduleDto.setCalldate( entity.getCalldate() );
        icCallscheduleDto.setCalltime( entity.getCalltime() );
        icCallscheduleDto.setId( entity.getId() );
        icCallscheduleDto.setIp( entity.getIp() );
        icCallscheduleDto.setSparefield1( entity.getSparefield1() );
        icCallscheduleDto.setSparefield2( entity.getSparefield2() );
        icCallscheduleDto.setSparefield3( entity.getSparefield3() );
        icCallscheduleDto.setSparefield4( entity.getSparefield4() );
        icCallscheduleDto.setTimeframe( entity.getTimeframe() );

        return icCallscheduleDto;
    }

    @Override
    public List<IcCallscheduleDto> toDto(List<IcCallschedule> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcCallscheduleDto> list = new ArrayList<IcCallscheduleDto>( entityList.size() );
        for ( IcCallschedule icCallschedule : entityList ) {
            list.add( toDto( icCallschedule ) );
        }

        return list;
    }

    @Override
    public IcCallschedule toEntity(IcCallscheduleDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcCallschedule icCallschedule = new IcCallschedule();

        icCallschedule.setApicode( dto.getApicode() );
        icCallschedule.setAppkey( dto.getAppkey() );
        icCallschedule.setCalldate( dto.getCalldate() );
        icCallschedule.setCalltime( dto.getCalltime() );
        icCallschedule.setId( dto.getId() );
        icCallschedule.setIp( dto.getIp() );
        icCallschedule.setSparefield1( dto.getSparefield1() );
        icCallschedule.setSparefield2( dto.getSparefield2() );
        icCallschedule.setSparefield3( dto.getSparefield3() );
        icCallschedule.setSparefield4( dto.getSparefield4() );
        icCallschedule.setTimeframe( dto.getTimeframe() );

        return icCallschedule;
    }

    @Override
    public List<IcCallschedule> toEntity(List<IcCallscheduleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcCallschedule> list = new ArrayList<IcCallschedule>( dtoList.size() );
        for ( IcCallscheduleDto icCallscheduleDto : dtoList ) {
            list.add( toEntity( icCallscheduleDto ) );
        }

        return list;
    }
}
