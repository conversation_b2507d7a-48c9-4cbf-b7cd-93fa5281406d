package com.wzsec.modules.z.service.mapper;

import com.wzsec.modules.z.domain.ZFileinfo;
import com.wzsec.modules.z.service.dto.ZFileinfoDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:32+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ZFileinfoMapperImpl implements ZFileinfoMapper {

    @Override
    public ZFileinfoDto toDto(ZFileinfo entity) {
        if ( entity == null ) {
            return null;
        }

        ZFileinfoDto zFileinfoDto = new ZFileinfoDto();

        zFileinfoDto.setFileextension( entity.getFileextension() );
        zFileinfoDto.setFilename( entity.getFilename() );
        zFileinfoDto.setFilepath( entity.getFilepath() );
        zFileinfoDto.setFilesize( entity.getFilesize() );
        zFileinfoDto.setFiletype( entity.getFiletype() );
        zFileinfoDto.setId( entity.getId() );
        zFileinfoDto.setIshide( entity.getIshide() );
        zFileinfoDto.setIsread( entity.getIsread() );
        zFileinfoDto.setIswrite( entity.getIswrite() );
        zFileinfoDto.setLastupdate( entity.getLastupdate() );
        zFileinfoDto.setNote( entity.getNote() );
        zFileinfoDto.setReservedfield1( entity.getReservedfield1() );
        zFileinfoDto.setReservedfield2( entity.getReservedfield2() );
        zFileinfoDto.setReservedfield3( entity.getReservedfield3() );
        zFileinfoDto.setReservedfield4( entity.getReservedfield4() );

        return zFileinfoDto;
    }

    @Override
    public List<ZFileinfoDto> toDto(List<ZFileinfo> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ZFileinfoDto> list = new ArrayList<ZFileinfoDto>( entityList.size() );
        for ( ZFileinfo zFileinfo : entityList ) {
            list.add( toDto( zFileinfo ) );
        }

        return list;
    }

    @Override
    public ZFileinfo toEntity(ZFileinfoDto dto) {
        if ( dto == null ) {
            return null;
        }

        ZFileinfo zFileinfo = new ZFileinfo();

        zFileinfo.setFileextension( dto.getFileextension() );
        zFileinfo.setFilename( dto.getFilename() );
        zFileinfo.setFilepath( dto.getFilepath() );
        zFileinfo.setFilesize( dto.getFilesize() );
        zFileinfo.setFiletype( dto.getFiletype() );
        zFileinfo.setId( dto.getId() );
        zFileinfo.setIshide( dto.getIshide() );
        zFileinfo.setIsread( dto.getIsread() );
        zFileinfo.setIswrite( dto.getIswrite() );
        zFileinfo.setLastupdate( dto.getLastupdate() );
        zFileinfo.setNote( dto.getNote() );
        zFileinfo.setReservedfield1( dto.getReservedfield1() );
        zFileinfo.setReservedfield2( dto.getReservedfield2() );
        zFileinfo.setReservedfield3( dto.getReservedfield3() );
        zFileinfo.setReservedfield4( dto.getReservedfield4() );

        return zFileinfo;
    }

    @Override
    public List<ZFileinfo> toEntity(List<ZFileinfoDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ZFileinfo> list = new ArrayList<ZFileinfo>( dtoList.size() );
        for ( ZFileinfoDto zFileinfoDto : dtoList ) {
            list.add( toEntity( zFileinfoDto ) );
        }

        return list;
    }
}
