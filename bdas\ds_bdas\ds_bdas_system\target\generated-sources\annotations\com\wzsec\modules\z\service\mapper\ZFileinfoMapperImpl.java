package com.wzsec.modules.z.service.mapper;

import com.wzsec.modules.z.domain.ZFileinfo;
import com.wzsec.modules.z.service.dto.ZFileinfoDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:31+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ZFileinfoMapperImpl implements ZFileinfoMapper {

    @Override
    public ZFileinfo toEntity(ZFileinfoDto dto) {
        if ( dto == null ) {
            return null;
        }

        ZFileinfo zFileinfo = new ZFileinfo();

        zFileinfo.setId( dto.getId() );
        zFileinfo.setFilename( dto.getFilename() );
        zFileinfo.setFilepath( dto.getFilepath() );
        zFileinfo.setFilesize( dto.getFilesize() );
        zFileinfo.setFiletype( dto.getFiletype() );
        zFileinfo.setFileextension( dto.getFileextension() );
        zFileinfo.setIshide( dto.getIshide() );
        zFileinfo.setIsread( dto.getIsread() );
        zFileinfo.setIswrite( dto.getIswrite() );
        zFileinfo.setLastupdate( dto.getLastupdate() );
        zFileinfo.setReservedfield1( dto.getReservedfield1() );
        zFileinfo.setReservedfield2( dto.getReservedfield2() );
        zFileinfo.setReservedfield3( dto.getReservedfield3() );
        zFileinfo.setReservedfield4( dto.getReservedfield4() );
        zFileinfo.setNote( dto.getNote() );

        return zFileinfo;
    }

    @Override
    public ZFileinfoDto toDto(ZFileinfo entity) {
        if ( entity == null ) {
            return null;
        }

        ZFileinfoDto zFileinfoDto = new ZFileinfoDto();

        zFileinfoDto.setId( entity.getId() );
        zFileinfoDto.setFilename( entity.getFilename() );
        zFileinfoDto.setFilepath( entity.getFilepath() );
        zFileinfoDto.setFilesize( entity.getFilesize() );
        zFileinfoDto.setFiletype( entity.getFiletype() );
        zFileinfoDto.setFileextension( entity.getFileextension() );
        zFileinfoDto.setIshide( entity.getIshide() );
        zFileinfoDto.setIsread( entity.getIsread() );
        zFileinfoDto.setIswrite( entity.getIswrite() );
        zFileinfoDto.setLastupdate( entity.getLastupdate() );
        zFileinfoDto.setReservedfield1( entity.getReservedfield1() );
        zFileinfoDto.setReservedfield2( entity.getReservedfield2() );
        zFileinfoDto.setReservedfield3( entity.getReservedfield3() );
        zFileinfoDto.setReservedfield4( entity.getReservedfield4() );
        zFileinfoDto.setNote( entity.getNote() );

        return zFileinfoDto;
    }

    @Override
    public List<ZFileinfo> toEntity(List<ZFileinfoDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ZFileinfo> list = new ArrayList<ZFileinfo>( dtoList.size() );
        for ( ZFileinfoDto zFileinfoDto : dtoList ) {
            list.add( toEntity( zFileinfoDto ) );
        }

        return list;
    }

    @Override
    public List<ZFileinfoDto> toDto(List<ZFileinfo> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ZFileinfoDto> list = new ArrayList<ZFileinfoDto>( entityList.size() );
        for ( ZFileinfo zFileinfo : entityList ) {
            list.add( toDto( zFileinfo ) );
        }

        return list;
    }
}
