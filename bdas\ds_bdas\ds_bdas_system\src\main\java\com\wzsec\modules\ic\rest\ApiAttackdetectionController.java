package com.wzsec.modules.ic.rest;

import cn.hutool.core.lang.Console;
import com.wzsec.aop.log.Log;
import com.wzsec.modules.ic.domain.ApiAttackdetection;
import com.wzsec.modules.ic.service.ApiAttackdetectionService;
import com.wzsec.modules.ic.service.dto.ApiAttackdetectionQueryCriteria;
import com.wzsec.utils.DomainUtil;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-05-21
 */
@RestController
@RequestMapping("/api/apiAttackdetection")
public class ApiAttackdetectionController {

    private final ApiAttackdetectionService apiAttackdetectionService;

    public ApiAttackdetectionController(ApiAttackdetectionService apiAttackdetectionService) {
        this.apiAttackdetectionService = apiAttackdetectionService;
    }

    @Log("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, ApiAttackdetectionQueryCriteria criteria) throws IOException {
        try {
            Timestamp dateStart = criteria.getDateStart();
            Timestamp dateEnd = criteria.getDateEnd();
            List<String> checkTime = new ArrayList<>();
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateStart));
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateEnd));
            criteria.setCheckdate(checkTime);
        } catch (Exception e) {
            Console.log("未填写查询时间");
        }

        // 根据申请部门名称分域
        List<String> domainAkList = DomainUtil.queryDomainAk();
        criteria.setAk(domainAkList);

        apiAttackdetectionService.download(apiAttackdetectionService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询接口攻击检测")
    public ResponseEntity<Object> getApiAttackdetections(ApiAttackdetectionQueryCriteria criteria, Pageable pageable) {
        try {
            Timestamp dateStart = criteria.getDateStart();
            Timestamp dateEnd = criteria.getDateEnd();
            List<String> checkTime = new ArrayList<>();
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateStart));
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateEnd));
            criteria.setCheckdate(checkTime);
        } catch (Exception e) {
            Console.log("未填写查询时间");
        }

        // 根据申请部门名称分域
        List<String> domainAkList = DomainUtil.queryDomainAk();
        criteria.setAk(domainAkList);

        return new ResponseEntity<>(apiAttackdetectionService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增接口攻击检测")
    public ResponseEntity<Object> create(@Validated @RequestBody ApiAttackdetection resources) {
        return new ResponseEntity<>(apiAttackdetectionService.create(resources), HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改接口攻击检测")
    public ResponseEntity<Object> update(@Validated @RequestBody ApiAttackdetection resources) {
        apiAttackdetectionService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除接口攻击检测")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        apiAttackdetectionService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
