package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.ApiSilentaccountcall;
import com.wzsec.modules.ic.service.dto.ApiSilentaccountcallDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:53+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ApiSilentaccountcallMapperImpl implements ApiSilentaccountcallMapper {

    @Override
    public ApiSilentaccountcallDto toDto(ApiSilentaccountcall entity) {
        if ( entity == null ) {
            return null;
        }

        ApiSilentaccountcallDto apiSilentaccountcallDto = new ApiSilentaccountcallDto();

        apiSilentaccountcallDto.setAccount( entity.getAccount() );
        apiSilentaccountcallDto.setAk( entity.getAk() );
        apiSilentaccountcallDto.setAkapicode( entity.getAkapicode() );
        apiSilentaccountcallDto.setApicode( entity.getApicode() );
        apiSilentaccountcallDto.setApiname( entity.getApiname() );
        apiSilentaccountcallDto.setApiurl( entity.getApiurl() );
        apiSilentaccountcallDto.setApplyorgname( entity.getApplyorgname() );
        apiSilentaccountcallDto.setCallnum( entity.getCallnum() );
        apiSilentaccountcallDto.setChecktime( entity.getChecktime() );
        apiSilentaccountcallDto.setId( entity.getId() );
        apiSilentaccountcallDto.setReqip( entity.getReqip() );
        apiSilentaccountcallDto.setRisk( entity.getRisk() );
        apiSilentaccountcallDto.setSparefield1( entity.getSparefield1() );
        apiSilentaccountcallDto.setSparefield2( entity.getSparefield2() );
        apiSilentaccountcallDto.setSparefield3( entity.getSparefield3() );
        apiSilentaccountcallDto.setSparefield4( entity.getSparefield4() );
        apiSilentaccountcallDto.setSystemname( entity.getSystemname() );

        return apiSilentaccountcallDto;
    }

    @Override
    public List<ApiSilentaccountcallDto> toDto(List<ApiSilentaccountcall> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiSilentaccountcallDto> list = new ArrayList<ApiSilentaccountcallDto>( entityList.size() );
        for ( ApiSilentaccountcall apiSilentaccountcall : entityList ) {
            list.add( toDto( apiSilentaccountcall ) );
        }

        return list;
    }

    @Override
    public ApiSilentaccountcall toEntity(ApiSilentaccountcallDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiSilentaccountcall apiSilentaccountcall = new ApiSilentaccountcall();

        apiSilentaccountcall.setAccount( dto.getAccount() );
        apiSilentaccountcall.setAk( dto.getAk() );
        apiSilentaccountcall.setAkapicode( dto.getAkapicode() );
        apiSilentaccountcall.setApicode( dto.getApicode() );
        apiSilentaccountcall.setApiname( dto.getApiname() );
        apiSilentaccountcall.setApiurl( dto.getApiurl() );
        apiSilentaccountcall.setApplyorgname( dto.getApplyorgname() );
        apiSilentaccountcall.setCallnum( dto.getCallnum() );
        apiSilentaccountcall.setChecktime( dto.getChecktime() );
        apiSilentaccountcall.setId( dto.getId() );
        apiSilentaccountcall.setReqip( dto.getReqip() );
        apiSilentaccountcall.setRisk( dto.getRisk() );
        apiSilentaccountcall.setSparefield1( dto.getSparefield1() );
        apiSilentaccountcall.setSparefield2( dto.getSparefield2() );
        apiSilentaccountcall.setSparefield3( dto.getSparefield3() );
        apiSilentaccountcall.setSparefield4( dto.getSparefield4() );
        apiSilentaccountcall.setSystemname( dto.getSystemname() );

        return apiSilentaccountcall;
    }

    @Override
    public List<ApiSilentaccountcall> toEntity(List<ApiSilentaccountcallDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiSilentaccountcall> list = new ArrayList<ApiSilentaccountcall>( dtoList.size() );
        for ( ApiSilentaccountcallDto apiSilentaccountcallDto : dtoList ) {
            list.add( toEntity( apiSilentaccountcallDto ) );
        }

        return list;
    }
}
