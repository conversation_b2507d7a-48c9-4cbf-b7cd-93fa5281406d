package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.ApiSilentaccountcall;
import com.wzsec.modules.ic.service.dto.ApiSilentaccountcallDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:01+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ApiSilentaccountcallMapperImpl implements ApiSilentaccountcallMapper {

    @Override
    public ApiSilentaccountcall toEntity(ApiSilentaccountcallDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiSilentaccountcall apiSilentaccountcall = new ApiSilentaccountcall();

        apiSilentaccountcall.setId( dto.getId() );
        apiSilentaccountcall.setAccount( dto.getAccount() );
        apiSilentaccountcall.setApicode( dto.getApicode() );
        apiSilentaccountcall.setRisk( dto.getRisk() );
        apiSilentaccountcall.setChecktime( dto.getChecktime() );
        apiSilentaccountcall.setSparefield1( dto.getSparefield1() );
        apiSilentaccountcall.setSparefield2( dto.getSparefield2() );
        apiSilentaccountcall.setSparefield3( dto.getSparefield3() );
        apiSilentaccountcall.setSparefield4( dto.getSparefield4() );
        apiSilentaccountcall.setAk( dto.getAk() );
        apiSilentaccountcall.setCallnum( dto.getCallnum() );
        apiSilentaccountcall.setApplyorgname( dto.getApplyorgname() );
        apiSilentaccountcall.setAkapicode( dto.getAkapicode() );
        apiSilentaccountcall.setApiurl( dto.getApiurl() );
        apiSilentaccountcall.setSystemname( dto.getSystemname() );
        apiSilentaccountcall.setApiname( dto.getApiname() );
        apiSilentaccountcall.setReqip( dto.getReqip() );

        return apiSilentaccountcall;
    }

    @Override
    public ApiSilentaccountcallDto toDto(ApiSilentaccountcall entity) {
        if ( entity == null ) {
            return null;
        }

        ApiSilentaccountcallDto apiSilentaccountcallDto = new ApiSilentaccountcallDto();

        apiSilentaccountcallDto.setId( entity.getId() );
        apiSilentaccountcallDto.setAccount( entity.getAccount() );
        apiSilentaccountcallDto.setApicode( entity.getApicode() );
        apiSilentaccountcallDto.setRisk( entity.getRisk() );
        apiSilentaccountcallDto.setChecktime( entity.getChecktime() );
        apiSilentaccountcallDto.setSparefield1( entity.getSparefield1() );
        apiSilentaccountcallDto.setSparefield2( entity.getSparefield2() );
        apiSilentaccountcallDto.setSparefield3( entity.getSparefield3() );
        apiSilentaccountcallDto.setSparefield4( entity.getSparefield4() );
        apiSilentaccountcallDto.setAk( entity.getAk() );
        apiSilentaccountcallDto.setCallnum( entity.getCallnum() );
        apiSilentaccountcallDto.setApplyorgname( entity.getApplyorgname() );
        apiSilentaccountcallDto.setAkapicode( entity.getAkapicode() );
        apiSilentaccountcallDto.setApiurl( entity.getApiurl() );
        apiSilentaccountcallDto.setSystemname( entity.getSystemname() );
        apiSilentaccountcallDto.setApiname( entity.getApiname() );
        apiSilentaccountcallDto.setReqip( entity.getReqip() );

        return apiSilentaccountcallDto;
    }

    @Override
    public List<ApiSilentaccountcall> toEntity(List<ApiSilentaccountcallDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiSilentaccountcall> list = new ArrayList<ApiSilentaccountcall>( dtoList.size() );
        for ( ApiSilentaccountcallDto apiSilentaccountcallDto : dtoList ) {
            list.add( toEntity( apiSilentaccountcallDto ) );
        }

        return list;
    }

    @Override
    public List<ApiSilentaccountcallDto> toDto(List<ApiSilentaccountcall> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiSilentaccountcallDto> list = new ArrayList<ApiSilentaccountcallDto>( entityList.size() );
        for ( ApiSilentaccountcall apiSilentaccountcall : entityList ) {
            list.add( toDto( apiSilentaccountcall ) );
        }

        return list;
    }
}
