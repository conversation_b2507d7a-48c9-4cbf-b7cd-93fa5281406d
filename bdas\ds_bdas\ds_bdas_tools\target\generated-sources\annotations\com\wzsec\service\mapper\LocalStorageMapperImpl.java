package com.wzsec.service.mapper;

import com.wzsec.domain.LocalStorage;
import com.wzsec.service.dto.LocalStorageDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:21+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class LocalStorageMapperImpl implements LocalStorageMapper {

    @Override
    public LocalStorageDto toDto(LocalStorage entity) {
        if ( entity == null ) {
            return null;
        }

        LocalStorageDto localStorageDto = new LocalStorageDto();

        localStorageDto.setCreateTime( entity.getCreateTime() );
        localStorageDto.setId( entity.getId() );
        localStorageDto.setName( entity.getName() );
        localStorageDto.setOperate( entity.getOperate() );
        localStorageDto.setRealName( entity.getRealName() );
        localStorageDto.setSize( entity.getSize() );
        localStorageDto.setSuffix( entity.getSuffix() );
        localStorageDto.setType( entity.getType() );

        return localStorageDto;
    }

    @Override
    public List<LocalStorageDto> toDto(List<LocalStorage> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<LocalStorageDto> list = new ArrayList<LocalStorageDto>( entityList.size() );
        for ( LocalStorage localStorage : entityList ) {
            list.add( toDto( localStorage ) );
        }

        return list;
    }

    @Override
    public LocalStorage toEntity(LocalStorageDto dto) {
        if ( dto == null ) {
            return null;
        }

        LocalStorage localStorage = new LocalStorage();

        localStorage.setCreateTime( dto.getCreateTime() );
        localStorage.setId( dto.getId() );
        localStorage.setName( dto.getName() );
        localStorage.setOperate( dto.getOperate() );
        localStorage.setRealName( dto.getRealName() );
        localStorage.setSize( dto.getSize() );
        localStorage.setSuffix( dto.getSuffix() );
        localStorage.setType( dto.getType() );

        return localStorage;
    }

    @Override
    public List<LocalStorage> toEntity(List<LocalStorageDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<LocalStorage> list = new ArrayList<LocalStorage>( dtoList.size() );
        for ( LocalStorageDto localStorageDto : dtoList ) {
            list.add( toEntity( localStorageDto ) );
        }

        return list;
    }
}
