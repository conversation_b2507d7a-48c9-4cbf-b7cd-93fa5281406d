package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcVenderInvokingRecord;
import com.wzsec.modules.ic.service.dto.IcVenderInvokingRecordDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:52+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class IcVenderInvokingRecordMapperImpl implements IcVenderInvokingRecordMapper {

    @Override
    public IcVenderInvokingRecordDto toDto(IcVenderInvokingRecord entity) {
        if ( entity == null ) {
            return null;
        }

        IcVenderInvokingRecordDto icVenderInvokingRecordDto = new IcVenderInvokingRecordDto();

        icVenderInvokingRecordDto.setApicode( entity.getApicode() );
        icVenderInvokingRecordDto.setApimethod( entity.getApimethod() );
        icVenderInvokingRecordDto.setCustname( entity.getCustname() );
        icVenderInvokingRecordDto.setDate( entity.getDate() );
        icVenderInvokingRecordDto.setId( entity.getId() );
        icVenderInvokingRecordDto.setIpaddress( entity.getIpaddress() );
        icVenderInvokingRecordDto.setIsnormal( entity.getIsnormal() );
        icVenderInvokingRecordDto.setNum( entity.getNum() );
        icVenderInvokingRecordDto.setSparefield1( entity.getSparefield1() );
        icVenderInvokingRecordDto.setSparefield2( entity.getSparefield2() );
        icVenderInvokingRecordDto.setSparefield3( entity.getSparefield3() );
        icVenderInvokingRecordDto.setSparefield4( entity.getSparefield4() );
        icVenderInvokingRecordDto.setTaskname( entity.getTaskname() );
        icVenderInvokingRecordDto.setUserid( entity.getUserid() );
        icVenderInvokingRecordDto.setUsername( entity.getUsername() );

        return icVenderInvokingRecordDto;
    }

    @Override
    public List<IcVenderInvokingRecordDto> toDto(List<IcVenderInvokingRecord> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcVenderInvokingRecordDto> list = new ArrayList<IcVenderInvokingRecordDto>( entityList.size() );
        for ( IcVenderInvokingRecord icVenderInvokingRecord : entityList ) {
            list.add( toDto( icVenderInvokingRecord ) );
        }

        return list;
    }

    @Override
    public IcVenderInvokingRecord toEntity(IcVenderInvokingRecordDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcVenderInvokingRecord icVenderInvokingRecord = new IcVenderInvokingRecord();

        icVenderInvokingRecord.setApicode( dto.getApicode() );
        icVenderInvokingRecord.setApimethod( dto.getApimethod() );
        icVenderInvokingRecord.setCustname( dto.getCustname() );
        icVenderInvokingRecord.setDate( dto.getDate() );
        icVenderInvokingRecord.setId( dto.getId() );
        icVenderInvokingRecord.setIpaddress( dto.getIpaddress() );
        icVenderInvokingRecord.setIsnormal( dto.getIsnormal() );
        icVenderInvokingRecord.setNum( dto.getNum() );
        icVenderInvokingRecord.setSparefield1( dto.getSparefield1() );
        icVenderInvokingRecord.setSparefield2( dto.getSparefield2() );
        icVenderInvokingRecord.setSparefield3( dto.getSparefield3() );
        icVenderInvokingRecord.setSparefield4( dto.getSparefield4() );
        icVenderInvokingRecord.setTaskname( dto.getTaskname() );
        icVenderInvokingRecord.setUserid( dto.getUserid() );
        icVenderInvokingRecord.setUsername( dto.getUsername() );

        return icVenderInvokingRecord;
    }

    @Override
    public List<IcVenderInvokingRecord> toEntity(List<IcVenderInvokingRecordDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcVenderInvokingRecord> list = new ArrayList<IcVenderInvokingRecord>( dtoList.size() );
        for ( IcVenderInvokingRecordDto icVenderInvokingRecordDto : dtoList ) {
            list.add( toEntity( icVenderInvokingRecordDto ) );
        }

        return list;
    }
}
