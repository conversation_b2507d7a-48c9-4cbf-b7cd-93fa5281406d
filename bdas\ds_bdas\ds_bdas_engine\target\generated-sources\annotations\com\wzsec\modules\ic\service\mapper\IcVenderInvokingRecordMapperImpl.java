package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcVenderInvokingRecord;
import com.wzsec.modules.ic.service.dto.IcVenderInvokingRecordDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:01+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class IcVenderInvokingRecordMapperImpl implements IcVenderInvokingRecordMapper {

    @Override
    public IcVenderInvokingRecord toEntity(IcVenderInvokingRecordDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcVenderInvokingRecord icVenderInvokingRecord = new IcVenderInvokingRecord();

        icVenderInvokingRecord.setId( dto.getId() );
        icVenderInvokingRecord.setTaskname( dto.getTaskname() );
        icVenderInvokingRecord.setCustname( dto.getCustname() );
        icVenderInvokingRecord.setUserid( dto.getUserid() );
        icVenderInvokingRecord.setUsername( dto.getUsername() );
        icVenderInvokingRecord.setIpaddress( dto.getIpaddress() );
        icVenderInvokingRecord.setApicode( dto.getApicode() );
        icVenderInvokingRecord.setApimethod( dto.getApimethod() );
        icVenderInvokingRecord.setIsnormal( dto.getIsnormal() );
        icVenderInvokingRecord.setNum( dto.getNum() );
        icVenderInvokingRecord.setDate( dto.getDate() );
        icVenderInvokingRecord.setSparefield1( dto.getSparefield1() );
        icVenderInvokingRecord.setSparefield2( dto.getSparefield2() );
        icVenderInvokingRecord.setSparefield3( dto.getSparefield3() );
        icVenderInvokingRecord.setSparefield4( dto.getSparefield4() );

        return icVenderInvokingRecord;
    }

    @Override
    public IcVenderInvokingRecordDto toDto(IcVenderInvokingRecord entity) {
        if ( entity == null ) {
            return null;
        }

        IcVenderInvokingRecordDto icVenderInvokingRecordDto = new IcVenderInvokingRecordDto();

        icVenderInvokingRecordDto.setId( entity.getId() );
        icVenderInvokingRecordDto.setTaskname( entity.getTaskname() );
        icVenderInvokingRecordDto.setCustname( entity.getCustname() );
        icVenderInvokingRecordDto.setUserid( entity.getUserid() );
        icVenderInvokingRecordDto.setUsername( entity.getUsername() );
        icVenderInvokingRecordDto.setIpaddress( entity.getIpaddress() );
        icVenderInvokingRecordDto.setApicode( entity.getApicode() );
        icVenderInvokingRecordDto.setApimethod( entity.getApimethod() );
        icVenderInvokingRecordDto.setIsnormal( entity.getIsnormal() );
        icVenderInvokingRecordDto.setNum( entity.getNum() );
        icVenderInvokingRecordDto.setDate( entity.getDate() );
        icVenderInvokingRecordDto.setSparefield1( entity.getSparefield1() );
        icVenderInvokingRecordDto.setSparefield2( entity.getSparefield2() );
        icVenderInvokingRecordDto.setSparefield3( entity.getSparefield3() );
        icVenderInvokingRecordDto.setSparefield4( entity.getSparefield4() );

        return icVenderInvokingRecordDto;
    }

    @Override
    public List<IcVenderInvokingRecord> toEntity(List<IcVenderInvokingRecordDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcVenderInvokingRecord> list = new ArrayList<IcVenderInvokingRecord>( dtoList.size() );
        for ( IcVenderInvokingRecordDto icVenderInvokingRecordDto : dtoList ) {
            list.add( toEntity( icVenderInvokingRecordDto ) );
        }

        return list;
    }

    @Override
    public List<IcVenderInvokingRecordDto> toDto(List<IcVenderInvokingRecord> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcVenderInvokingRecordDto> list = new ArrayList<IcVenderInvokingRecordDto>( entityList.size() );
        for ( IcVenderInvokingRecord icVenderInvokingRecord : entityList ) {
            list.add( toDto( icVenderInvokingRecord ) );
        }

        return list;
    }
}
