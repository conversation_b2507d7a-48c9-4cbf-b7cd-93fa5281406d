package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcResultDetail;
import com.wzsec.modules.ic.service.dto.IcResultDetailDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:02+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class IcResultDetailMapperImpl implements IcResultDetailMapper {

    @Override
    public IcResultDetail toEntity(IcResultDetailDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcResultDetail icResultDetail = new IcResultDetail();

        icResultDetail.setId( dto.getId() );
        icResultDetail.setTaskname( dto.getTaskname() );
        icResultDetail.setLogsign( dto.getLogsign() );
        icResultDetail.setCustname( dto.getCustname() );
        icResultDetail.setCustSimplename( dto.getCustSimplename() );
        icResultDetail.setUserid( dto.getUserid() );
        icResultDetail.setAppid( dto.getAppid() );
        icResultDetail.setAppname( dto.getAppname() );
        icResultDetail.setApicode( dto.getApicode() );
        icResultDetail.setApimethod( dto.getApimethod() );
        icResultDetail.setApiname( dto.getApiname() );
        icResultDetail.setApitype( dto.getApitype() );
        icResultDetail.setCheckparam( dto.getCheckparam() );
        icResultDetail.setParamMean( dto.getParamMean() );
        icResultDetail.setImportance( dto.getImportance() );
        icResultDetail.setCheckrule( dto.getCheckrule() );
        icResultDetail.setRisk( dto.getRisk() );
        icResultDetail.setResulttype( dto.getResulttype() );
        icResultDetail.setCheckcount( dto.getCheckcount() );
        icResultDetail.setTotalcount( dto.getTotalcount() );
        icResultDetail.setChecklinecount( dto.getChecklinecount() );
        icResultDetail.setTotallinecount( dto.getTotallinecount() );
        icResultDetail.setRatio( dto.getRatio() );
        icResultDetail.setSensitivedata( dto.getSensitivedata() );
        icResultDetail.setExample( dto.getExample() );
        icResultDetail.setSparefield1( dto.getSparefield1() );
        icResultDetail.setSparefield2( dto.getSparefield2() );
        icResultDetail.setSparefield3( dto.getSparefield3() );
        icResultDetail.setSparefield4( dto.getSparefield4() );
        icResultDetail.setChecktime( dto.getChecktime() );
        icResultDetail.setAk( dto.getAk() );
        icResultDetail.setApplyorgname( dto.getApplyorgname() );
        icResultDetail.setApiurl( dto.getApiurl() );
        icResultDetail.setAkapicode( dto.getAkapicode() );
        icResultDetail.setReqip( dto.getReqip() );

        return icResultDetail;
    }

    @Override
    public IcResultDetailDto toDto(IcResultDetail entity) {
        if ( entity == null ) {
            return null;
        }

        IcResultDetailDto icResultDetailDto = new IcResultDetailDto();

        icResultDetailDto.setId( entity.getId() );
        icResultDetailDto.setTaskname( entity.getTaskname() );
        icResultDetailDto.setLogsign( entity.getLogsign() );
        icResultDetailDto.setCustname( entity.getCustname() );
        icResultDetailDto.setCustSimplename( entity.getCustSimplename() );
        icResultDetailDto.setUserid( entity.getUserid() );
        icResultDetailDto.setAppid( entity.getAppid() );
        icResultDetailDto.setAppname( entity.getAppname() );
        icResultDetailDto.setApicode( entity.getApicode() );
        icResultDetailDto.setApimethod( entity.getApimethod() );
        icResultDetailDto.setApiname( entity.getApiname() );
        icResultDetailDto.setApitype( entity.getApitype() );
        icResultDetailDto.setCheckparam( entity.getCheckparam() );
        icResultDetailDto.setParamMean( entity.getParamMean() );
        icResultDetailDto.setImportance( entity.getImportance() );
        icResultDetailDto.setCheckrule( entity.getCheckrule() );
        icResultDetailDto.setRisk( entity.getRisk() );
        icResultDetailDto.setResulttype( entity.getResulttype() );
        icResultDetailDto.setCheckcount( entity.getCheckcount() );
        icResultDetailDto.setTotalcount( entity.getTotalcount() );
        icResultDetailDto.setChecklinecount( entity.getChecklinecount() );
        icResultDetailDto.setTotallinecount( entity.getTotallinecount() );
        icResultDetailDto.setRatio( entity.getRatio() );
        icResultDetailDto.setSensitivedata( entity.getSensitivedata() );
        icResultDetailDto.setExample( entity.getExample() );
        icResultDetailDto.setSparefield1( entity.getSparefield1() );
        icResultDetailDto.setSparefield2( entity.getSparefield2() );
        icResultDetailDto.setSparefield3( entity.getSparefield3() );
        icResultDetailDto.setSparefield4( entity.getSparefield4() );
        icResultDetailDto.setChecktime( entity.getChecktime() );
        icResultDetailDto.setAk( entity.getAk() );
        icResultDetailDto.setApplyorgname( entity.getApplyorgname() );
        icResultDetailDto.setApiurl( entity.getApiurl() );
        icResultDetailDto.setAkapicode( entity.getAkapicode() );
        icResultDetailDto.setReqip( entity.getReqip() );

        return icResultDetailDto;
    }

    @Override
    public List<IcResultDetail> toEntity(List<IcResultDetailDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcResultDetail> list = new ArrayList<IcResultDetail>( dtoList.size() );
        for ( IcResultDetailDto icResultDetailDto : dtoList ) {
            list.add( toEntity( icResultDetailDto ) );
        }

        return list;
    }

    @Override
    public List<IcResultDetailDto> toDto(List<IcResultDetail> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcResultDetailDto> list = new ArrayList<IcResultDetailDto>( entityList.size() );
        for ( IcResultDetail icResultDetail : entityList ) {
            list.add( toDto( icResultDetail ) );
        }

        return list;
    }
}
