package com.wzsec.modules.z.service.mapper;

import com.wzsec.modules.z.domain.ZSafetyRule;
import com.wzsec.modules.z.service.dto.ZSafetyRuleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:29+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ZSafetyruleMapperImpl implements ZSafetyruleMapper {

    @Override
    public ZSafetyRuleDto toDto(ZSafetyRule entity) {
        if ( entity == null ) {
            return null;
        }

        ZSafetyRuleDto zSafetyRuleDto = new ZSafetyRuleDto();

        zSafetyRuleDto.setBackupfield1( entity.getBackupfield1() );
        zSafetyRuleDto.setBackupfield2( entity.getBackupfield2() );
        zSafetyRuleDto.setBackupfield3( entity.getBackupfield3() );
        zSafetyRuleDto.setCkmethod( entity.getCkmethod() );
        zSafetyRuleDto.setCrule( entity.getCrule() );
        zSafetyRuleDto.setErule( entity.getErule() );
        zSafetyRuleDto.setId( entity.getId() );
        zSafetyRuleDto.setNote( entity.getNote() );
        zSafetyRuleDto.setRulestatus( entity.getRulestatus() );
        zSafetyRuleDto.setSafetylevel( entity.getSafetylevel() );

        return zSafetyRuleDto;
    }

    @Override
    public List<ZSafetyRuleDto> toDto(List<ZSafetyRule> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ZSafetyRuleDto> list = new ArrayList<ZSafetyRuleDto>( entityList.size() );
        for ( ZSafetyRule zSafetyRule : entityList ) {
            list.add( toDto( zSafetyRule ) );
        }

        return list;
    }

    @Override
    public ZSafetyRule toEntity(ZSafetyRuleDto dto) {
        if ( dto == null ) {
            return null;
        }

        ZSafetyRule zSafetyRule = new ZSafetyRule();

        zSafetyRule.setBackupfield1( dto.getBackupfield1() );
        zSafetyRule.setBackupfield2( dto.getBackupfield2() );
        zSafetyRule.setBackupfield3( dto.getBackupfield3() );
        zSafetyRule.setCkmethod( dto.getCkmethod() );
        zSafetyRule.setCrule( dto.getCrule() );
        zSafetyRule.setErule( dto.getErule() );
        zSafetyRule.setId( dto.getId() );
        zSafetyRule.setNote( dto.getNote() );
        zSafetyRule.setRulestatus( dto.getRulestatus() );
        zSafetyRule.setSafetylevel( dto.getSafetylevel() );

        return zSafetyRule;
    }

    @Override
    public List<ZSafetyRule> toEntity(List<ZSafetyRuleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ZSafetyRule> list = new ArrayList<ZSafetyRule>( dtoList.size() );
        for ( ZSafetyRuleDto zSafetyRuleDto : dtoList ) {
            list.add( toEntity( zSafetyRuleDto ) );
        }

        return list;
    }
}
