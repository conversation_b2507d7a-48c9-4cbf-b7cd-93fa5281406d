package com.wzsec.modules.z.service.mapper;

import com.wzsec.modules.z.domain.ZSafetyRule;
import com.wzsec.modules.z.service.dto.ZSafetyRuleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:30+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ZSafetyruleMapperImpl implements ZSafetyruleMapper {

    @Override
    public ZSafetyRule toEntity(ZSafetyRuleDto dto) {
        if ( dto == null ) {
            return null;
        }

        ZSafetyRule zSafetyRule = new ZSafetyRule();

        zSafetyRule.setId( dto.getId() );
        zSafetyRule.setErule( dto.getErule() );
        zSafetyRule.setCrule( dto.getCrule() );
        zSafetyRule.setCkmethod( dto.getCkmethod() );
        zSafetyRule.setSafetylevel( dto.getSafetylevel() );
        zSafetyRule.setRulestatus( dto.getRulestatus() );
        zSafetyRule.setNote( dto.getNote() );
        zSafetyRule.setBackupfield1( dto.getBackupfield1() );
        zSafetyRule.setBackupfield2( dto.getBackupfield2() );
        zSafetyRule.setBackupfield3( dto.getBackupfield3() );

        return zSafetyRule;
    }

    @Override
    public ZSafetyRuleDto toDto(ZSafetyRule entity) {
        if ( entity == null ) {
            return null;
        }

        ZSafetyRuleDto zSafetyRuleDto = new ZSafetyRuleDto();

        zSafetyRuleDto.setId( entity.getId() );
        zSafetyRuleDto.setErule( entity.getErule() );
        zSafetyRuleDto.setCrule( entity.getCrule() );
        zSafetyRuleDto.setCkmethod( entity.getCkmethod() );
        zSafetyRuleDto.setSafetylevel( entity.getSafetylevel() );
        zSafetyRuleDto.setRulestatus( entity.getRulestatus() );
        zSafetyRuleDto.setNote( entity.getNote() );
        zSafetyRuleDto.setBackupfield1( entity.getBackupfield1() );
        zSafetyRuleDto.setBackupfield2( entity.getBackupfield2() );
        zSafetyRuleDto.setBackupfield3( entity.getBackupfield3() );

        return zSafetyRuleDto;
    }

    @Override
    public List<ZSafetyRule> toEntity(List<ZSafetyRuleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ZSafetyRule> list = new ArrayList<ZSafetyRule>( dtoList.size() );
        for ( ZSafetyRuleDto zSafetyRuleDto : dtoList ) {
            list.add( toEntity( zSafetyRuleDto ) );
        }

        return list;
    }

    @Override
    public List<ZSafetyRuleDto> toDto(List<ZSafetyRule> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ZSafetyRuleDto> list = new ArrayList<ZSafetyRuleDto>( entityList.size() );
        for ( ZSafetyRule zSafetyRule : entityList ) {
            list.add( toDto( zSafetyRule ) );
        }

        return list;
    }
}
