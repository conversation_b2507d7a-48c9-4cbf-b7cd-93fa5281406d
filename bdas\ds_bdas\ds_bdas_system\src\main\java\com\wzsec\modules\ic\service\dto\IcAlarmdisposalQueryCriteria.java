package com.wzsec.modules.ic.service.dto;

import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

import com.wzsec.annotation.Query;
import org.apache.poi.ss.formula.functions.T;

/**
 * <AUTHOR>
 * @date 2022-10-09
 */
@Data
public class IcAlarmdisposalQueryCriteria {

    /** 支持精确查询和IN查询：单个值时精确查询，逗号分隔字符串或List时IN查询 */
    @Query(type = Query.Type.IN)
    private Object ak;

    private Timestamp dateStart;

    private Timestamp dateEnd;

    @Query(type = Query.Type.IN)
    private List<String> apicode;

    @Query
    private String risk;

    @Query
    private String treatmentstate;

    @Query
    private String detectionmodel;

    @Query(type = Query.Type.BETWEEN)
    private List<String> checktime;

    @Query(blurry = "apicode,apiname,detectionmodel,circumstantiality,risk,treatmentstate,sourceip,destinationip,ak,apiurl,reqdepartment,icresdepartment")
    private String blurry;
}
