package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcDetectionrule;
import com.wzsec.modules.ic.service.dto.IcDetectionruleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:26:47+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class IcDetectionruleMapperImpl implements IcDetectionruleMapper {

    @Override
    public IcDetectionruleDto toDto(IcDetectionrule entity) {
        if ( entity == null ) {
            return null;
        }

        IcDetectionruleDto icDetectionruleDto = new IcDetectionruleDto();

        icDetectionruleDto.setCname( entity.getCname() );
        icDetectionruleDto.setEname( entity.getEname() );
        icDetectionruleDto.setId( entity.getId() );
        icDetectionruleDto.setNote( entity.getNote() );
        icDetectionruleDto.setReservefield1( entity.getReservefield1() );
        icDetectionruleDto.setReservefield2( entity.getReservefield2() );
        icDetectionruleDto.setReservefield3( entity.getReservefield3() );
        icDetectionruleDto.setReservefield4( entity.getReservefield4() );
        icDetectionruleDto.setRulecontent( entity.getRulecontent() );
        icDetectionruleDto.setRuledescription( entity.getRuledescription() );
        icDetectionruleDto.setRulelevel( entity.getRulelevel() );
        icDetectionruleDto.setRuletype( entity.getRuletype() );

        return icDetectionruleDto;
    }

    @Override
    public List<IcDetectionruleDto> toDto(List<IcDetectionrule> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcDetectionruleDto> list = new ArrayList<IcDetectionruleDto>( entityList.size() );
        for ( IcDetectionrule icDetectionrule : entityList ) {
            list.add( toDto( icDetectionrule ) );
        }

        return list;
    }

    @Override
    public IcDetectionrule toEntity(IcDetectionruleDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcDetectionrule icDetectionrule = new IcDetectionrule();

        icDetectionrule.setCname( dto.getCname() );
        icDetectionrule.setEname( dto.getEname() );
        icDetectionrule.setId( dto.getId() );
        icDetectionrule.setNote( dto.getNote() );
        icDetectionrule.setReservefield1( dto.getReservefield1() );
        icDetectionrule.setReservefield2( dto.getReservefield2() );
        icDetectionrule.setReservefield3( dto.getReservefield3() );
        icDetectionrule.setReservefield4( dto.getReservefield4() );
        icDetectionrule.setRulecontent( dto.getRulecontent() );
        icDetectionrule.setRuledescription( dto.getRuledescription() );
        icDetectionrule.setRulelevel( dto.getRulelevel() );
        icDetectionrule.setRuletype( dto.getRuletype() );

        return icDetectionrule;
    }

    @Override
    public List<IcDetectionrule> toEntity(List<IcDetectionruleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcDetectionrule> list = new ArrayList<IcDetectionrule>( dtoList.size() );
        for ( IcDetectionruleDto icDetectionruleDto : dtoList ) {
            list.add( toEntity( icDetectionruleDto ) );
        }

        return list;
    }
}
