package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.ApiUnusedcheckresult;
import com.wzsec.modules.ic.service.dto.ApiUnusedcheckresultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:01+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ApiUnusedcheckresultMapperImpl implements ApiUnusedcheckresultMapper {

    @Override
    public ApiUnusedcheckresult toEntity(ApiUnusedcheckresultDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiUnusedcheckresult apiUnusedcheckresult = new ApiUnusedcheckresult();

        apiUnusedcheckresult.setId( dto.getId() );
        apiUnusedcheckresult.setApicode( dto.getApicode() );
        apiUnusedcheckresult.setChecktime( dto.getChecktime() );
        apiUnusedcheckresult.setSparefield1( dto.getSparefield1() );
        apiUnusedcheckresult.setSparefield2( dto.getSparefield2() );
        apiUnusedcheckresult.setSparefield3( dto.getSparefield3() );
        apiUnusedcheckresult.setSparefield4( dto.getSparefield4() );
        apiUnusedcheckresult.setRisk( dto.getRisk() );
        apiUnusedcheckresult.setApiurl( dto.getApiurl() );
        apiUnusedcheckresult.setSystemname( dto.getSystemname() );
        apiUnusedcheckresult.setApiname( dto.getApiname() );

        return apiUnusedcheckresult;
    }

    @Override
    public ApiUnusedcheckresultDto toDto(ApiUnusedcheckresult entity) {
        if ( entity == null ) {
            return null;
        }

        ApiUnusedcheckresultDto apiUnusedcheckresultDto = new ApiUnusedcheckresultDto();

        apiUnusedcheckresultDto.setId( entity.getId() );
        apiUnusedcheckresultDto.setApicode( entity.getApicode() );
        apiUnusedcheckresultDto.setChecktime( entity.getChecktime() );
        apiUnusedcheckresultDto.setSparefield1( entity.getSparefield1() );
        apiUnusedcheckresultDto.setSparefield2( entity.getSparefield2() );
        apiUnusedcheckresultDto.setSparefield3( entity.getSparefield3() );
        apiUnusedcheckresultDto.setSparefield4( entity.getSparefield4() );
        apiUnusedcheckresultDto.setRisk( entity.getRisk() );
        apiUnusedcheckresultDto.setApiurl( entity.getApiurl() );
        apiUnusedcheckresultDto.setSystemname( entity.getSystemname() );
        apiUnusedcheckresultDto.setApiname( entity.getApiname() );

        return apiUnusedcheckresultDto;
    }

    @Override
    public List<ApiUnusedcheckresult> toEntity(List<ApiUnusedcheckresultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiUnusedcheckresult> list = new ArrayList<ApiUnusedcheckresult>( dtoList.size() );
        for ( ApiUnusedcheckresultDto apiUnusedcheckresultDto : dtoList ) {
            list.add( toEntity( apiUnusedcheckresultDto ) );
        }

        return list;
    }

    @Override
    public List<ApiUnusedcheckresultDto> toDto(List<ApiUnusedcheckresult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiUnusedcheckresultDto> list = new ArrayList<ApiUnusedcheckresultDto>( entityList.size() );
        for ( ApiUnusedcheckresult apiUnusedcheckresult : entityList ) {
            list.add( toDto( apiUnusedcheckresult ) );
        }

        return list;
    }
}
