package com.wzsec.modules.weakpwd.service.mapper;

import com.wzsec.modules.weakpwd.domain.WPTask;
import com.wzsec.modules.weakpwd.service.dto.WPTaskDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:33+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class WPTaskMapperImpl implements WPTaskMapper {

    @Override
    public WPTaskDto toDto(WPTask entity) {
        if ( entity == null ) {
            return null;
        }

        WPTaskDto wPTaskDto = new WPTaskDto();

        wPTaskDto.setCreatetime( entity.getCreatetime() );
        wPTaskDto.setCreateuser( entity.getCreateuser() );
        wPTaskDto.setCron( entity.getCron() );
        wPTaskDto.setExecutestate( entity.getExecutestate() );
        wPTaskDto.setId( entity.getId() );
        wPTaskDto.setNote( entity.getNote() );
        wPTaskDto.setSparefield1( entity.getSparefield1() );
        wPTaskDto.setSparefield2( entity.getSparefield2() );
        wPTaskDto.setSparefield3( entity.getSparefield3() );
        wPTaskDto.setSparefield4( entity.getSparefield4() );
        wPTaskDto.setSparefield5( entity.getSparefield5() );
        wPTaskDto.setState( entity.getState() );
        wPTaskDto.setSubmittype( entity.getSubmittype() );
        wPTaskDto.setTaskno( entity.getTaskno() );
        wPTaskDto.setUpdatetime( entity.getUpdatetime() );
        wPTaskDto.setUpdateuser( entity.getUpdateuser() );

        return wPTaskDto;
    }

    @Override
    public List<WPTaskDto> toDto(List<WPTask> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<WPTaskDto> list = new ArrayList<WPTaskDto>( entityList.size() );
        for ( WPTask wPTask : entityList ) {
            list.add( toDto( wPTask ) );
        }

        return list;
    }

    @Override
    public WPTask toEntity(WPTaskDto dto) {
        if ( dto == null ) {
            return null;
        }

        WPTask wPTask = new WPTask();

        wPTask.setCreatetime( dto.getCreatetime() );
        wPTask.setCreateuser( dto.getCreateuser() );
        wPTask.setCron( dto.getCron() );
        wPTask.setExecutestate( dto.getExecutestate() );
        wPTask.setId( dto.getId() );
        wPTask.setNote( dto.getNote() );
        wPTask.setSparefield1( dto.getSparefield1() );
        wPTask.setSparefield2( dto.getSparefield2() );
        wPTask.setSparefield3( dto.getSparefield3() );
        wPTask.setSparefield4( dto.getSparefield4() );
        wPTask.setSparefield5( dto.getSparefield5() );
        wPTask.setState( dto.getState() );
        wPTask.setSubmittype( dto.getSubmittype() );
        wPTask.setTaskno( dto.getTaskno() );
        wPTask.setUpdatetime( dto.getUpdatetime() );
        wPTask.setUpdateuser( dto.getUpdateuser() );

        return wPTask;
    }

    @Override
    public List<WPTask> toEntity(List<WPTaskDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<WPTask> list = new ArrayList<WPTask>( dtoList.size() );
        for ( WPTaskDto wPTaskDto : dtoList ) {
            list.add( toEntity( wPTaskDto ) );
        }

        return list;
    }
}
