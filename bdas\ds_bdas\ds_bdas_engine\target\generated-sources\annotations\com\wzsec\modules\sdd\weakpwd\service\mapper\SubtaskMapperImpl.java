package com.wzsec.modules.sdd.weakpwd.service.mapper;

import com.wzsec.modules.sdd.weakpwd.domain.Subtask;
import com.wzsec.modules.sdd.weakpwd.service.dto.SubtaskDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:01+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class SubtaskMapperImpl implements SubtaskMapper {

    @Override
    public Subtask toEntity(SubtaskDto dto) {
        if ( dto == null ) {
            return null;
        }

        Subtask subtask = new Subtask();

        subtask.setId( dto.getId() );
        subtask.setTaskno( dto.getTaskno() );
        subtask.setSubtaskno( dto.getSubtaskno() );
        subtask.setDatasourceid( dto.getDatasourceid() );
        subtask.setTablename( dto.getTablename() );
        subtask.setLoginnamefield( dto.getLoginnamefield() );
        subtask.setUsernamefield( dto.getUsernamefield() );
        subtask.setPwdfield( dto.getPwdfield() );
        subtask.setNote( dto.getNote() );
        subtask.setCreateuser( dto.getCreateuser() );
        subtask.setCreatetime( dto.getCreatetime() );
        subtask.setUpdateuser( dto.getUpdateuser() );
        subtask.setUpdatetime( dto.getUpdatetime() );
        subtask.setSparefield1( dto.getSparefield1() );
        subtask.setSparefield2( dto.getSparefield2() );
        subtask.setSparefield3( dto.getSparefield3() );
        subtask.setSparefield4( dto.getSparefield4() );
        subtask.setSparefield5( dto.getSparefield5() );

        return subtask;
    }

    @Override
    public SubtaskDto toDto(Subtask entity) {
        if ( entity == null ) {
            return null;
        }

        SubtaskDto subtaskDto = new SubtaskDto();

        subtaskDto.setId( entity.getId() );
        subtaskDto.setTaskno( entity.getTaskno() );
        subtaskDto.setSubtaskno( entity.getSubtaskno() );
        subtaskDto.setDatasourceid( entity.getDatasourceid() );
        subtaskDto.setTablename( entity.getTablename() );
        subtaskDto.setLoginnamefield( entity.getLoginnamefield() );
        subtaskDto.setUsernamefield( entity.getUsernamefield() );
        subtaskDto.setPwdfield( entity.getPwdfield() );
        subtaskDto.setNote( entity.getNote() );
        subtaskDto.setCreateuser( entity.getCreateuser() );
        subtaskDto.setCreatetime( entity.getCreatetime() );
        subtaskDto.setUpdateuser( entity.getUpdateuser() );
        subtaskDto.setUpdatetime( entity.getUpdatetime() );
        subtaskDto.setSparefield1( entity.getSparefield1() );
        subtaskDto.setSparefield2( entity.getSparefield2() );
        subtaskDto.setSparefield3( entity.getSparefield3() );
        subtaskDto.setSparefield4( entity.getSparefield4() );
        subtaskDto.setSparefield5( entity.getSparefield5() );

        return subtaskDto;
    }

    @Override
    public List<Subtask> toEntity(List<SubtaskDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Subtask> list = new ArrayList<Subtask>( dtoList.size() );
        for ( SubtaskDto subtaskDto : dtoList ) {
            list.add( toEntity( subtaskDto ) );
        }

        return list;
    }

    @Override
    public List<SubtaskDto> toDto(List<Subtask> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<SubtaskDto> list = new ArrayList<SubtaskDto>( entityList.size() );
        for ( Subtask subtask : entityList ) {
            list.add( toDto( subtask ) );
        }

        return list;
    }
}
