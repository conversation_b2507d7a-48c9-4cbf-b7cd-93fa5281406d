package com.wzsec.modules.oc.service.mapper;

import com.wzsec.modules.oc.domain.OcDbResult;
import com.wzsec.modules.oc.service.dto.OcDbResultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:53+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class OcDbResultMapperImpl implements OcDbResultMapper {

    @Override
    public OcDbResultDto toDto(OcDbResult entity) {
        if ( entity == null ) {
            return null;
        }

        OcDbResultDto ocDbResultDto = new OcDbResultDto();

        ocDbResultDto.setChecktime( entity.getChecktime() );
        ocDbResultDto.setDbtype( entity.getDbtype() );
        ocDbResultDto.setId( entity.getId() );
        ocDbResultDto.setIpaddress( entity.getIpaddress() );
        ocDbResultDto.setOperation( entity.getOperation() );
        ocDbResultDto.setOperationtime( entity.getOperationtime() );
        ocDbResultDto.setOperationtype( entity.getOperationtype() );
        ocDbResultDto.setRisk( entity.getRisk() );
        ocDbResultDto.setSparefield1( entity.getSparefield1() );
        ocDbResultDto.setSparefield2( entity.getSparefield2() );
        ocDbResultDto.setSparefield3( entity.getSparefield3() );
        ocDbResultDto.setSparefield4( entity.getSparefield4() );
        ocDbResultDto.setTaskname( entity.getTaskname() );
        ocDbResultDto.setUsername( entity.getUsername() );

        return ocDbResultDto;
    }

    @Override
    public List<OcDbResultDto> toDto(List<OcDbResult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<OcDbResultDto> list = new ArrayList<OcDbResultDto>( entityList.size() );
        for ( OcDbResult ocDbResult : entityList ) {
            list.add( toDto( ocDbResult ) );
        }

        return list;
    }

    @Override
    public OcDbResult toEntity(OcDbResultDto dto) {
        if ( dto == null ) {
            return null;
        }

        OcDbResult ocDbResult = new OcDbResult();

        ocDbResult.setChecktime( dto.getChecktime() );
        ocDbResult.setDbtype( dto.getDbtype() );
        ocDbResult.setId( dto.getId() );
        ocDbResult.setIpaddress( dto.getIpaddress() );
        ocDbResult.setOperation( dto.getOperation() );
        ocDbResult.setOperationtime( dto.getOperationtime() );
        ocDbResult.setOperationtype( dto.getOperationtype() );
        ocDbResult.setRisk( dto.getRisk() );
        ocDbResult.setSparefield1( dto.getSparefield1() );
        ocDbResult.setSparefield2( dto.getSparefield2() );
        ocDbResult.setSparefield3( dto.getSparefield3() );
        ocDbResult.setSparefield4( dto.getSparefield4() );
        ocDbResult.setTaskname( dto.getTaskname() );
        ocDbResult.setUsername( dto.getUsername() );

        return ocDbResult;
    }

    @Override
    public List<OcDbResult> toEntity(List<OcDbResultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<OcDbResult> list = new ArrayList<OcDbResult>( dtoList.size() );
        for ( OcDbResultDto ocDbResultDto : dtoList ) {
            list.add( toEntity( ocDbResultDto ) );
        }

        return list;
    }
}
