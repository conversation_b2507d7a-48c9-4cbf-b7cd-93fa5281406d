package com.wzsec.utils.file.office;

import org.apache.poi.hslf.extractor.PowerPointExtractor;
import org.apache.poi.hssf.record.crypto.Biff8EncryptionKey;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.extractor.WordExtractor;
import org.apache.poi.poifs.crypt.Decryptor;
import org.apache.poi.poifs.crypt.EncryptionInfo;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xslf.extractor.XSLFPowerPointExtractor;
import org.apache.poi.xslf.usermodel.XMLSlideShow;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.extractor.XWPFWordExtractor;
import org.apache.poi.xwpf.usermodel.XWPFDocument;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

public class OfficeUtil {

    /**
     * 读取excel文件，解析后返回
     *
     * @param filePath 文件路径
     * @param pwd      文件密码
     * @return List<String>
     * @throws Exception 异常
     */
    public static List<String> getExcelStr(String filePath, String pwd) throws Exception {
        List<String> list = new ArrayList<>();
        File file = new File(filePath);
        if (file.length() > 5242880) { // 超大excel读取
            return ExcelReader.readerExcel(filePath);
        }
        Workbook workbook = getWorkBook(file, pwd);
        if (workbook != null) {
            for (int sheetNum = 0; sheetNum < workbook.getNumberOfSheets(); sheetNum++) {
                Sheet sheet = workbook.getSheetAt(sheetNum);
                if (sheet == null) {
                    continue;
                }
                for (int rowNum = 0; rowNum <= sheet.getLastRowNum(); rowNum++) {
                    Row row = sheet.getRow(rowNum);
                    if (row == null) {
                        continue;
                    }
                    for (int cellNum = 0; cellNum < row.getLastCellNum(); cellNum++) {
                        Cell cell = row.getCell(cellNum);
                        if (cell != null) {
                            String str = getCellValue(cell);
                            list.add(str);
                        }
                    }
                }
            }
            workbook.close();
        }
        return list;
    }

    /**
     * 创建Workbook工作薄对象
     *
     * @param file 文件
     * @param pwd  密码
     * @return Workbook
     * @throws Exception 异常
     */
    private static Workbook getWorkBook(File file, String pwd) throws Exception {
        Workbook workbook = null;
        InputStream is = null;
        try {
            is = new FileInputStream(file);
            if (isOffice2007(file.getAbsolutePath())) {
                workbook = new XSSFWorkbook(is);
            } else {
                workbook = new HSSFWorkbook(is);
            }
            is.close();
        } catch (Exception e) {
            try {
                is = new FileInputStream(file);
                POIFSFileSystem pfs = new POIFSFileSystem(is);
                EncryptionInfo encInfo = new EncryptionInfo(pfs);
                Decryptor decryptor = Decryptor.getInstance(encInfo);
                if (decryptor.verifyPassword(pwd)) {
                    workbook = new XSSFWorkbook(decryptor.getDataStream(pfs));
                } else {
                    throw new RuntimeException("解析xlsx文件失败,解密密码错误");
                }
                is.close();
            } catch (Exception e1) {
                try {
                    is = new FileInputStream(file);
                    Biff8EncryptionKey.setCurrentUserPassword(pwd);
                    workbook = new HSSFWorkbook(is);
                    is.close();
                } catch (Exception e2) {
                    throw new RuntimeException("解析xls文件失败,不支持2003版本加密的Excel文件");
                }
            }
        } finally {
            if (is != null) {
                is.close();
            }
        }
        return workbook;
    }

    /**
     * 读取PPT文本内容
     *
     * @param filePath 文件地址
     * @return PPT文本内容
     * @throws IOException
     */
    public static String getPPTStr(String filePath) throws IOException {
        StringBuilder result = new StringBuilder();
        InputStream fis = null;
        try {
            fis = new FileInputStream(filePath);
            if (isOffice2007(filePath)) {
                XMLSlideShow slideShow = new XMLSlideShow(fis);
                XSLFPowerPointExtractor extractor = new XSLFPowerPointExtractor(slideShow);
                result.append(extractor.getText());
            } else {
                PowerPointExtractor extractor = new PowerPointExtractor(fis);
                result.append(extractor.getText());
            }
        } finally {
            if (fis != null) {
                fis.close();
            }
        }
        return result.toString();
    }

    /**
     * 读取word文本内容
     *
     * @param filePath 文件地址
     * @return word文本内容
     * @throws IOException
     */
    public static String getWordStr(String filePath) throws IOException {
        String result = null;
        InputStream in = null;
        StringBuilder str = new StringBuilder();
        try {
            in = new FileInputStream(filePath);
            if (isOffice2007(filePath)) {
                XWPFDocument document = new XWPFDocument(in);
                XWPFWordExtractor extractor = new XWPFWordExtractor(document);
                result = extractor.getText();
            } else {
                HWPFDocument document = new HWPFDocument(in);
                WordExtractor extractor = new WordExtractor(document);
                result = extractor.getText();
            }
        } finally {
            if (in != null) {
                in.close();
            }
        }
        return result;
    }

    /**
     * 获取单元格数据类型
     *
     * @param cell 单元格
     * @return String
     */
    private static String getCellValue(Cell cell) {
        String cellValue = "";
        if (cell == null) {
            return cellValue;
        }
        if (cell.getCellType() == CellType.NUMERIC) {
            cell.setCellType(CellType.STRING);
        }
        switch (cell.getCellType()) {
            case STRING:
                cellValue = cell.getStringCellValue();
                break;
            case NUMERIC:
                cellValue = String.valueOf(cell.getNumericCellValue());
                break;
            case BOOLEAN:
                cellValue = String.valueOf(cell.getBooleanCellValue());
                break;
            case FORMULA:
                cellValue = cell.getCellFormula();
                break;
            case BLANK:
                cellValue = "";
                break;
            case ERROR:
                cellValue = "非法字符";
                break;
            default:
                cellValue = "未知类型";
                break;
        }
        return cellValue;
    }

    /**
     * 是否是2007及以上版本
     *
     * @param filePath 文件地址
     * @return true 是 false 不是
     * @throws IOException 异常
     */
    public static boolean isOffice2007(String filePath) throws IOException {
        boolean is2007Format = false;
        FileInputStream fis = null;
        try {
            fis = new FileInputStream(filePath);
            is2007Format = WorkbookFactory.create(fis) instanceof XSSFWorkbook;
        } finally {
            if (fis != null) {
                fis.close();
            }
        }
        return is2007Format;
    }
}
