package com.wzsec.modules.oc.service.mapper;

import com.wzsec.modules.oc.domain.OcDbResult;
import com.wzsec.modules.oc.service.dto.OcDbResultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:31+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class OcDbResultMapperImpl implements OcDbResultMapper {

    @Override
    public OcDbResult toEntity(OcDbResultDto dto) {
        if ( dto == null ) {
            return null;
        }

        OcDbResult ocDbResult = new OcDbResult();

        ocDbResult.setId( dto.getId() );
        ocDbResult.setTaskname( dto.getTaskname() );
        ocDbResult.setDbtype( dto.getDbtype() );
        ocDbResult.setOperationtime( dto.getOperationtime() );
        ocDbResult.setUsername( dto.getUsername() );
        ocDbResult.setIpaddress( dto.getIpaddress() );
        ocDbResult.setOperationtype( dto.getOperationtype() );
        ocDbResult.setOperation( dto.getOperation() );
        ocDbResult.setRisk( dto.getRisk() );
        ocDbResult.setChecktime( dto.getChecktime() );
        ocDbResult.setSparefield1( dto.getSparefield1() );
        ocDbResult.setSparefield2( dto.getSparefield2() );
        ocDbResult.setSparefield3( dto.getSparefield3() );
        ocDbResult.setSparefield4( dto.getSparefield4() );

        return ocDbResult;
    }

    @Override
    public OcDbResultDto toDto(OcDbResult entity) {
        if ( entity == null ) {
            return null;
        }

        OcDbResultDto ocDbResultDto = new OcDbResultDto();

        ocDbResultDto.setId( entity.getId() );
        ocDbResultDto.setTaskname( entity.getTaskname() );
        ocDbResultDto.setDbtype( entity.getDbtype() );
        ocDbResultDto.setOperationtime( entity.getOperationtime() );
        ocDbResultDto.setUsername( entity.getUsername() );
        ocDbResultDto.setIpaddress( entity.getIpaddress() );
        ocDbResultDto.setOperationtype( entity.getOperationtype() );
        ocDbResultDto.setOperation( entity.getOperation() );
        ocDbResultDto.setRisk( entity.getRisk() );
        ocDbResultDto.setChecktime( entity.getChecktime() );
        ocDbResultDto.setSparefield1( entity.getSparefield1() );
        ocDbResultDto.setSparefield2( entity.getSparefield2() );
        ocDbResultDto.setSparefield3( entity.getSparefield3() );
        ocDbResultDto.setSparefield4( entity.getSparefield4() );

        return ocDbResultDto;
    }

    @Override
    public List<OcDbResult> toEntity(List<OcDbResultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<OcDbResult> list = new ArrayList<OcDbResult>( dtoList.size() );
        for ( OcDbResultDto ocDbResultDto : dtoList ) {
            list.add( toEntity( ocDbResultDto ) );
        }

        return list;
    }

    @Override
    public List<OcDbResultDto> toDto(List<OcDbResult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<OcDbResultDto> list = new ArrayList<OcDbResultDto>( entityList.size() );
        for ( OcDbResult ocDbResult : entityList ) {
            list.add( toDto( ocDbResult ) );
        }

        return list;
    }
}
