package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.Apidiscovery;
import com.wzsec.modules.ic.service.dto.ApidiscoveryDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:31+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ApidiscoveryMapperImpl implements ApidiscoveryMapper {

    @Override
    public Apidiscovery toEntity(ApidiscoveryDto dto) {
        if ( dto == null ) {
            return null;
        }

        Apidiscovery apidiscovery = new Apidiscovery();

        apidiscovery.setId( dto.getId() );
        apidiscovery.setApicode( dto.getApicode() );
        apidiscovery.setApiname( dto.getApiname() );
        apidiscovery.setApiip( dto.getApiip() );
        apidiscovery.setApiport( dto.getApiport() );
        apidiscovery.setUrl( dto.getUrl() );
        apidiscovery.setReqExample( dto.getReqExample() );
        apidiscovery.setResExample( dto.getResExample() );
        apidiscovery.setCategory( dto.getCategory() );
        apidiscovery.setRisk( dto.getRisk() );
        apidiscovery.setLabels( dto.getLabels() );
        apidiscovery.setAccessdomain( dto.getAccessdomain() );
        apidiscovery.setApistatus( dto.getApistatus() );
        apidiscovery.setInserttime( dto.getInserttime() );
        apidiscovery.setUpdateuser( dto.getUpdateuser() );
        apidiscovery.setUpdatetime( dto.getUpdatetime() );
        apidiscovery.setSparefield1( dto.getSparefield1() );
        apidiscovery.setSparefield2( dto.getSparefield2() );
        apidiscovery.setSparefield3( dto.getSparefield3() );
        apidiscovery.setSparefield4( dto.getSparefield4() );

        return apidiscovery;
    }

    @Override
    public ApidiscoveryDto toDto(Apidiscovery entity) {
        if ( entity == null ) {
            return null;
        }

        ApidiscoveryDto apidiscoveryDto = new ApidiscoveryDto();

        apidiscoveryDto.setId( entity.getId() );
        apidiscoveryDto.setApicode( entity.getApicode() );
        apidiscoveryDto.setApiname( entity.getApiname() );
        apidiscoveryDto.setApiip( entity.getApiip() );
        apidiscoveryDto.setApiport( entity.getApiport() );
        apidiscoveryDto.setUrl( entity.getUrl() );
        apidiscoveryDto.setReqExample( entity.getReqExample() );
        apidiscoveryDto.setResExample( entity.getResExample() );
        apidiscoveryDto.setCategory( entity.getCategory() );
        apidiscoveryDto.setRisk( entity.getRisk() );
        apidiscoveryDto.setLabels( entity.getLabels() );
        apidiscoveryDto.setAccessdomain( entity.getAccessdomain() );
        apidiscoveryDto.setApistatus( entity.getApistatus() );
        apidiscoveryDto.setInserttime( entity.getInserttime() );
        apidiscoveryDto.setUpdateuser( entity.getUpdateuser() );
        apidiscoveryDto.setUpdatetime( entity.getUpdatetime() );
        apidiscoveryDto.setSparefield1( entity.getSparefield1() );
        apidiscoveryDto.setSparefield2( entity.getSparefield2() );
        apidiscoveryDto.setSparefield3( entity.getSparefield3() );
        apidiscoveryDto.setSparefield4( entity.getSparefield4() );

        return apidiscoveryDto;
    }

    @Override
    public List<Apidiscovery> toEntity(List<ApidiscoveryDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Apidiscovery> list = new ArrayList<Apidiscovery>( dtoList.size() );
        for ( ApidiscoveryDto apidiscoveryDto : dtoList ) {
            list.add( toEntity( apidiscoveryDto ) );
        }

        return list;
    }

    @Override
    public List<ApidiscoveryDto> toDto(List<Apidiscovery> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApidiscoveryDto> list = new ArrayList<ApidiscoveryDto>( entityList.size() );
        for ( Apidiscovery apidiscovery : entityList ) {
            list.add( toDto( apidiscovery ) );
        }

        return list;
    }
}
