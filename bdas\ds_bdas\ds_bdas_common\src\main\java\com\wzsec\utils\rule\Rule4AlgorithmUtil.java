package com.wzsec.utils.rule;

import com.wzsec.utils.StringUtils;


/**
 * 加密算法规则工具类，自定义规则在Rule4ProgramUtil.java
 *
 * <AUTHOR>
 * @date 2020-4-28
 */
public class Rule4AlgorithmUtil {


    /**
     * 必须包含大小写字母及数字
     *
     * @param data
     * @param type 1为大写字母 ，2为小写字母 ,3为大小写字母
     * @return
     */
    private static boolean isContainLetterDigit(String data, String type) {
        if (StringUtils.isEmpty(data))
            return false;
        boolean isRight = false;// 定义一个boolean值，用来表示最后的结果
        boolean isDigit = false;// 定义一个boolean值，用来表示是否包含数字
        boolean isUpperCase = false;// 定义一个boolean值，用来表示是否包含大写字母
        boolean isLowerCase = false;// 定义一个boolean值，用来表示是否包含小写字母
        for (int i = 0; i < data.length(); i++) {
            if (Character.isDigit(data.charAt(i))) { // 用char包装类中的判断数字的方法判断每一个字符
                isDigit = true;
            } else if (Character.isUpperCase(data.charAt(i))) { // 用char包装类中的判断字母的方法判断每一个字符
                isUpperCase = true;
            } else if (Character.isLowerCase(data.charAt(i))) { // 用char包装类中的判断字母的方法判断每一个字符
                isLowerCase = true;
            } else {
                return isRight;
            }
        }
        if (type.equals("1")) {
            isRight = isDigit && isUpperCase;
        } else if (type.equals("2")) {
            isRight = isDigit && isLowerCase;
        } else {
            isRight = isDigit && isUpperCase && isLowerCase;
        }
        return isRight;
    }

    /**
     * @Description:AES校验
     * <AUTHOR> by dongs
     * @date 2019-1-16
     */
    public static boolean p_checkAES(String data) {
        if (StringUtils.isEmpty(data))
            return false;
        if (data.length() == 32) {
            if (isContainLetterDigit(data, "1")) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    /**
     * @Description:3DES校验
     * <AUTHOR> by dongs
     * @date 2019-1-16
     */
    public static boolean p_check3DES(String data) {
        if (StringUtils.isEmpty(data))
            return false;
        // 12和24位 最后一位是= 数字和大小写字母必须有 不包含汉字 没有|和,和分号
        // 32位 数字和大小写字母 不包含汉字 没有|和,和分号
        boolean isRight = false;// 定义一个boolean值，用来表示最后的结果
        boolean isDigit = false;// 定义一个boolean值，用来表示是否包含数字
        boolean isUpperCase = false;// 定义一个boolean值，用来表示是否包含大写字母
        boolean isLowerCase = false;// 定义一个boolean值，用来表示是否包含小写字母
        if (data.length() == 12 || data.length() == 24) {
            if (!"=".equals(data.substring(data.length() - 1))) {
                return false;
            }
            for (int i = 0; i < data.length(); i++) {
                char s = data.charAt(i);
                if (Character.isDigit(s)) { // 用char包装类中的判断数字的方法判断每一个字符
                    isDigit = true;
                } else if (Character.isUpperCase(s)) { // 用char包装类中的判断字母的方法判断每一个字符
                    isUpperCase = true;
                } else if (Character.isLowerCase(s)) { // 用char包装类中的判断字母的方法判断每一个字符
                    isLowerCase = true;
                } else if (String.valueOf(s).matches("[\u4e00-\u9fa5]")) { // 判断不包含汉字
                    return false;
                } else if ("|".equals(String.valueOf(s)) || ",".equals(String.valueOf(s))
                        || ";".equals(String.valueOf(s))) {
                    return false;
                } else if (data.contains(" ")) {
                    return false;
                }
            }
            isRight = isDigit && isUpperCase && isLowerCase;
        } else if (data.length() == 32) {
            for (int i = 0; i < data.length(); i++) {
                char s = data.charAt(i);
                if (Character.isDigit(s)) { // 用char包装类中的判断数字的方法判断每一个字符
                    isDigit = true;
                } else if (Character.isUpperCase(s)) { // 用char包装类中的判断字母的方法判断每一个字符
                    isUpperCase = true;
                } else if (Character.isLowerCase(s)) { // 用char包装类中的判断字母的方法判断每一个字符
                    isLowerCase = true;
                } else if (String.valueOf(s).matches("[\u4e00-\u9fa5]")) { // 判断不包含汉字
                    return false;
                } else if ("|".equals(String.valueOf(s)) || ",".equals(String.valueOf(s))
                        || ";".equals(String.valueOf(s))) { //
                    return false;
                } else if (data.contains(" ")) {
                    return false;
                }
            }
            isRight = isDigit && isUpperCase && isLowerCase;
        } else {
            return false;
        }

        return isRight;
    }

    /**
     * @Description:MD5校验
     * <AUTHOR> by dongs
     * @date 2019-1-16
     */
    public static boolean p_checkMD5(String data) {
        if (StringUtils.isEmpty(data))
            return false;
        if (data.length() == 32) {
            if (isContainLetterDigit(data, "2")) {//数字和小写字母
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    /**
     * @Description:sha256校验
     * <AUTHOR> by dongs
     * @date 2019-1-16
     */
    public static boolean p_checkSHA256(String data) {
        if (StringUtils.isEmpty(data))
            return false;
        if (data.length() == 64) {
            if (isContainLetterDigit(data, "2")) {//数字和小写字母
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

}
