package com.wzsec.modules.ic.service.dto;

import lombok.Data;

import java.sql.Timestamp;
import java.util.List;
import com.wzsec.annotation.Query;

/**
* <AUTHOR>
* @date 2022-12-28
*/
@Data
public class ApiInvolvesaccountpasswordQueryCriteria{

    /** 支持精确查询和IN查询：单个值时精确查询，逗号分隔字符串或List时IN查询 */
    @Query(type = Query.Type.IN)
    private Object ak;

    private Timestamp dateStart;

    private Timestamp dateEnd;

    /**
     * 模糊
     */
    @Query(type = Query.Type.INNER_LIKE)
    private String apicode;

    /**
     * 模糊
     */
    @Query(type = Query.Type.INNER_LIKE)
    private String involvesaccount;

    /**
     * 精确
     */
    @Query
    private String requesttype;

    /**
     * 精确
     */
    @Query
    private String sparefield1;

    /**
     * 精确
     */
    @Query
    private String risk;
    /**
     * BETWEEN
     */
    @Query(type = Query.Type.BETWEEN)
    private List<String> checktime;

    /**
     * 模糊
     */
    @Query(type = Query.Type.INNER_LIKE)
    private String apiname;

    @Query(blurry = "apicode,involvesaccount,requesttype,risk,sparefield1,apiname")
    private String blurry;
}
