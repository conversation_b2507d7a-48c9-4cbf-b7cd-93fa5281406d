package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.ApiComplianceaudit;
import com.wzsec.modules.ic.service.dto.ApiComplianceauditDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:30+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ApiComplianceauditMapperImpl implements ApiComplianceauditMapper {

    @Override
    public ApiComplianceaudit toEntity(ApiComplianceauditDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiComplianceaudit apiComplianceaudit = new ApiComplianceaudit();

        apiComplianceaudit.setId( dto.getId() );
        apiComplianceaudit.setApicode( dto.getApicode() );
        apiComplianceaudit.setApiname( dto.getApiname() );
        apiComplianceaudit.setProtocol( dto.getProtocol() );
        apiComplianceaudit.setInputparams( dto.getInputparams() );
        apiComplianceaudit.setActualinput( dto.getActualinput() );
        apiComplianceaudit.setOutputparams( dto.getOutputparams() );
        apiComplianceaudit.setActualoutput( dto.getActualoutput() );
        apiComplianceaudit.setCompliance( dto.getCompliance() );
        apiComplianceaudit.setRisk( dto.getRisk() );
        apiComplianceaudit.setStatus( dto.getStatus() );
        apiComplianceaudit.setTime( dto.getTime() );
        apiComplianceaudit.setSparefield1( dto.getSparefield1() );
        apiComplianceaudit.setSparefield2( dto.getSparefield2() );
        apiComplianceaudit.setSparefield3( dto.getSparefield3() );
        apiComplianceaudit.setSparefield4( dto.getSparefield4() );

        return apiComplianceaudit;
    }

    @Override
    public ApiComplianceauditDto toDto(ApiComplianceaudit entity) {
        if ( entity == null ) {
            return null;
        }

        ApiComplianceauditDto apiComplianceauditDto = new ApiComplianceauditDto();

        apiComplianceauditDto.setId( entity.getId() );
        apiComplianceauditDto.setApicode( entity.getApicode() );
        apiComplianceauditDto.setApiname( entity.getApiname() );
        apiComplianceauditDto.setProtocol( entity.getProtocol() );
        apiComplianceauditDto.setInputparams( entity.getInputparams() );
        apiComplianceauditDto.setActualinput( entity.getActualinput() );
        apiComplianceauditDto.setOutputparams( entity.getOutputparams() );
        apiComplianceauditDto.setActualoutput( entity.getActualoutput() );
        apiComplianceauditDto.setCompliance( entity.getCompliance() );
        apiComplianceauditDto.setRisk( entity.getRisk() );
        apiComplianceauditDto.setStatus( entity.getStatus() );
        apiComplianceauditDto.setTime( entity.getTime() );
        apiComplianceauditDto.setSparefield1( entity.getSparefield1() );
        apiComplianceauditDto.setSparefield2( entity.getSparefield2() );
        apiComplianceauditDto.setSparefield3( entity.getSparefield3() );
        apiComplianceauditDto.setSparefield4( entity.getSparefield4() );

        return apiComplianceauditDto;
    }

    @Override
    public List<ApiComplianceaudit> toEntity(List<ApiComplianceauditDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiComplianceaudit> list = new ArrayList<ApiComplianceaudit>( dtoList.size() );
        for ( ApiComplianceauditDto apiComplianceauditDto : dtoList ) {
            list.add( toEntity( apiComplianceauditDto ) );
        }

        return list;
    }

    @Override
    public List<ApiComplianceauditDto> toDto(List<ApiComplianceaudit> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiComplianceauditDto> list = new ArrayList<ApiComplianceauditDto>( entityList.size() );
        for ( ApiComplianceaudit apiComplianceaudit : entityList ) {
            list.add( toDto( apiComplianceaudit ) );
        }

        return list;
    }
}
