package com.wzsec.service.mapper;

import com.wzsec.domain.EmailConfig;
import com.wzsec.service.dto.EmailConfigDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T13:59:50+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class EmailConfigMapperImpl implements EmailConfigMapper {

    @Override
    public EmailConfig toEntity(EmailConfigDto dto) {
        if ( dto == null ) {
            return null;
        }

        EmailConfig emailConfig = new EmailConfig();

        emailConfig.setId( dto.getId() );
        emailConfig.setHost( dto.getHost() );
        emailConfig.setPort( dto.getPort() );
        emailConfig.setUser( dto.getUser() );
        emailConfig.setPass( dto.getPass() );
        emailConfig.setChecktime( dto.getChecktime() );
        emailConfig.setReceiver( dto.getReceiver() );
        emailConfig.setFromUser( dto.getFromUser() );
        emailConfig.setMotif( dto.getMotif() );

        return emailConfig;
    }

    @Override
    public EmailConfigDto toDto(EmailConfig entity) {
        if ( entity == null ) {
            return null;
        }

        EmailConfigDto emailConfigDto = new EmailConfigDto();

        emailConfigDto.setId( entity.getId() );
        emailConfigDto.setHost( entity.getHost() );
        emailConfigDto.setPort( entity.getPort() );
        emailConfigDto.setUser( entity.getUser() );
        emailConfigDto.setPass( entity.getPass() );
        emailConfigDto.setChecktime( entity.getChecktime() );
        emailConfigDto.setReceiver( entity.getReceiver() );
        emailConfigDto.setFromUser( entity.getFromUser() );
        emailConfigDto.setMotif( entity.getMotif() );

        return emailConfigDto;
    }

    @Override
    public List<EmailConfig> toEntity(List<EmailConfigDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<EmailConfig> list = new ArrayList<EmailConfig>( dtoList.size() );
        for ( EmailConfigDto emailConfigDto : dtoList ) {
            list.add( toEntity( emailConfigDto ) );
        }

        return list;
    }

    @Override
    public List<EmailConfigDto> toDto(List<EmailConfig> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<EmailConfigDto> list = new ArrayList<EmailConfigDto>( entityList.size() );
        for ( EmailConfig emailConfig : entityList ) {
            list.add( toDto( emailConfig ) );
        }

        return list;
    }
}
