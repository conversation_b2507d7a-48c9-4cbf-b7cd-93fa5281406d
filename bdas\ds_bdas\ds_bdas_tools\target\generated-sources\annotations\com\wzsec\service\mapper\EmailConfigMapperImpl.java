package com.wzsec.service.mapper;

import com.wzsec.domain.EmailConfig;
import com.wzsec.service.dto.EmailConfigDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:21+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class EmailConfigMapperImpl implements EmailConfigMapper {

    @Override
    public EmailConfigDto toDto(EmailConfig entity) {
        if ( entity == null ) {
            return null;
        }

        EmailConfigDto emailConfigDto = new EmailConfigDto();

        emailConfigDto.setChecktime( entity.getChecktime() );
        emailConfigDto.setFromUser( entity.getFromUser() );
        emailConfigDto.setHost( entity.getHost() );
        emailConfigDto.setId( entity.getId() );
        emailConfigDto.setMotif( entity.getMotif() );
        emailConfigDto.setPass( entity.getPass() );
        emailConfigDto.setPort( entity.getPort() );
        emailConfigDto.setReceiver( entity.getReceiver() );
        emailConfigDto.setUser( entity.getUser() );

        return emailConfigDto;
    }

    @Override
    public List<EmailConfigDto> toDto(List<EmailConfig> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<EmailConfigDto> list = new ArrayList<EmailConfigDto>( entityList.size() );
        for ( EmailConfig emailConfig : entityList ) {
            list.add( toDto( emailConfig ) );
        }

        return list;
    }

    @Override
    public EmailConfig toEntity(EmailConfigDto dto) {
        if ( dto == null ) {
            return null;
        }

        EmailConfig emailConfig = new EmailConfig();

        emailConfig.setChecktime( dto.getChecktime() );
        emailConfig.setFromUser( dto.getFromUser() );
        emailConfig.setHost( dto.getHost() );
        emailConfig.setId( dto.getId() );
        emailConfig.setMotif( dto.getMotif() );
        emailConfig.setPass( dto.getPass() );
        emailConfig.setPort( dto.getPort() );
        emailConfig.setReceiver( dto.getReceiver() );
        emailConfig.setUser( dto.getUser() );

        return emailConfig;
    }

    @Override
    public List<EmailConfig> toEntity(List<EmailConfigDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<EmailConfig> list = new ArrayList<EmailConfig>( dtoList.size() );
        for ( EmailConfigDto emailConfigDto : dtoList ) {
            list.add( toEntity( emailConfigDto ) );
        }

        return list;
    }
}
