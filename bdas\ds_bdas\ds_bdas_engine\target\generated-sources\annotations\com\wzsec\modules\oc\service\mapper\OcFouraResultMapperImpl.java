package com.wzsec.modules.oc.service.mapper;

import com.wzsec.modules.oc.domain.OcFouraResult;
import com.wzsec.modules.oc.service.dto.OcFouraResultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:27:07+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class OcFouraResultMapperImpl implements OcFouraResultMapper {

    @Override
    public OcFouraResultDto toDto(OcFouraResult entity) {
        if ( entity == null ) {
            return null;
        }

        OcFouraResultDto ocFouraResultDto = new OcFouraResultDto();

        ocFouraResultDto.setChecktime( entity.getChecktime() );
        ocFouraResultDto.setId( entity.getId() );
        ocFouraResultDto.setIpaddress( entity.getIpaddress() );
        ocFouraResultDto.setOperation( entity.getOperation() );
        ocFouraResultDto.setOperationtime( entity.getOperationtime() );
        ocFouraResultDto.setOperationtype( entity.getOperationtype() );
        ocFouraResultDto.setResource_account( entity.getResource_account() );
        ocFouraResultDto.setResource_name( entity.getResource_name() );
        ocFouraResultDto.setRisk( entity.getRisk() );
        ocFouraResultDto.setSparefield1( entity.getSparefield1() );
        ocFouraResultDto.setSparefield2( entity.getSparefield2() );
        ocFouraResultDto.setSparefield3( entity.getSparefield3() );
        ocFouraResultDto.setSparefield4( entity.getSparefield4() );
        ocFouraResultDto.setTaskname( entity.getTaskname() );
        ocFouraResultDto.setTool_name( entity.getTool_name() );
        ocFouraResultDto.setUsername( entity.getUsername() );

        return ocFouraResultDto;
    }

    @Override
    public List<OcFouraResultDto> toDto(List<OcFouraResult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<OcFouraResultDto> list = new ArrayList<OcFouraResultDto>( entityList.size() );
        for ( OcFouraResult ocFouraResult : entityList ) {
            list.add( toDto( ocFouraResult ) );
        }

        return list;
    }

    @Override
    public OcFouraResult toEntity(OcFouraResultDto dto) {
        if ( dto == null ) {
            return null;
        }

        OcFouraResult ocFouraResult = new OcFouraResult();

        ocFouraResult.setChecktime( dto.getChecktime() );
        ocFouraResult.setId( dto.getId() );
        ocFouraResult.setIpaddress( dto.getIpaddress() );
        ocFouraResult.setOperation( dto.getOperation() );
        ocFouraResult.setOperationtime( dto.getOperationtime() );
        ocFouraResult.setOperationtype( dto.getOperationtype() );
        ocFouraResult.setResource_account( dto.getResource_account() );
        ocFouraResult.setResource_name( dto.getResource_name() );
        ocFouraResult.setRisk( dto.getRisk() );
        ocFouraResult.setSparefield1( dto.getSparefield1() );
        ocFouraResult.setSparefield2( dto.getSparefield2() );
        ocFouraResult.setSparefield3( dto.getSparefield3() );
        ocFouraResult.setSparefield4( dto.getSparefield4() );
        ocFouraResult.setTaskname( dto.getTaskname() );
        ocFouraResult.setTool_name( dto.getTool_name() );
        ocFouraResult.setUsername( dto.getUsername() );

        return ocFouraResult;
    }

    @Override
    public List<OcFouraResult> toEntity(List<OcFouraResultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<OcFouraResult> list = new ArrayList<OcFouraResult>( dtoList.size() );
        for ( OcFouraResultDto ocFouraResultDto : dtoList ) {
            list.add( toEntity( ocFouraResultDto ) );
        }

        return list;
    }
}
