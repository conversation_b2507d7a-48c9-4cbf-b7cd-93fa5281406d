package com.wzsec.modules.oc.service.mapper;

import com.wzsec.modules.oc.domain.OcFouraResult;
import com.wzsec.modules.oc.service.dto.OcFouraResultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:01+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class OcFouraResultMapperImpl implements OcFouraResultMapper {

    @Override
    public OcFouraResult toEntity(OcFouraResultDto dto) {
        if ( dto == null ) {
            return null;
        }

        OcFouraResult ocFouraResult = new OcFouraResult();

        ocFouraResult.setId( dto.getId() );
        ocFouraResult.setTaskname( dto.getTaskname() );
        ocFouraResult.setOperationtime( dto.getOperationtime() );
        ocFouraResult.setUsername( dto.getUsername() );
        ocFouraResult.setIpaddress( dto.getIpaddress() );
        ocFouraResult.setOperationtype( dto.getOperationtype() );
        ocFouraResult.setOperation( dto.getOperation() );
        ocFouraResult.setRisk( dto.getRisk() );
        ocFouraResult.setChecktime( dto.getChecktime() );
        ocFouraResult.setResource_name( dto.getResource_name() );
        ocFouraResult.setResource_account( dto.getResource_account() );
        ocFouraResult.setTool_name( dto.getTool_name() );
        ocFouraResult.setSparefield1( dto.getSparefield1() );
        ocFouraResult.setSparefield2( dto.getSparefield2() );
        ocFouraResult.setSparefield3( dto.getSparefield3() );
        ocFouraResult.setSparefield4( dto.getSparefield4() );

        return ocFouraResult;
    }

    @Override
    public OcFouraResultDto toDto(OcFouraResult entity) {
        if ( entity == null ) {
            return null;
        }

        OcFouraResultDto ocFouraResultDto = new OcFouraResultDto();

        ocFouraResultDto.setId( entity.getId() );
        ocFouraResultDto.setTaskname( entity.getTaskname() );
        ocFouraResultDto.setOperationtime( entity.getOperationtime() );
        ocFouraResultDto.setUsername( entity.getUsername() );
        ocFouraResultDto.setIpaddress( entity.getIpaddress() );
        ocFouraResultDto.setOperationtype( entity.getOperationtype() );
        ocFouraResultDto.setOperation( entity.getOperation() );
        ocFouraResultDto.setRisk( entity.getRisk() );
        ocFouraResultDto.setChecktime( entity.getChecktime() );
        ocFouraResultDto.setResource_name( entity.getResource_name() );
        ocFouraResultDto.setResource_account( entity.getResource_account() );
        ocFouraResultDto.setTool_name( entity.getTool_name() );
        ocFouraResultDto.setSparefield1( entity.getSparefield1() );
        ocFouraResultDto.setSparefield2( entity.getSparefield2() );
        ocFouraResultDto.setSparefield3( entity.getSparefield3() );
        ocFouraResultDto.setSparefield4( entity.getSparefield4() );

        return ocFouraResultDto;
    }

    @Override
    public List<OcFouraResult> toEntity(List<OcFouraResultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<OcFouraResult> list = new ArrayList<OcFouraResult>( dtoList.size() );
        for ( OcFouraResultDto ocFouraResultDto : dtoList ) {
            list.add( toEntity( ocFouraResultDto ) );
        }

        return list;
    }

    @Override
    public List<OcFouraResultDto> toDto(List<OcFouraResult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<OcFouraResultDto> list = new ArrayList<OcFouraResultDto>( entityList.size() );
        for ( OcFouraResult ocFouraResult : entityList ) {
            list.add( toDto( ocFouraResult ) );
        }

        return list;
    }
}
