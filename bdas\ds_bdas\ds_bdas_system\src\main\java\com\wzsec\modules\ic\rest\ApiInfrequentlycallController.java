package com.wzsec.modules.ic.rest;

import cn.hutool.core.lang.Console;
import com.wzsec.aop.log.Log;
import com.wzsec.modules.ic.domain.ApiInfrequentlycall;
import com.wzsec.modules.ic.service.ApiInfrequentlycallService;
import com.wzsec.modules.ic.service.dto.ApiInfrequentlycallQueryCriteria;
import com.wzsec.utils.DomainUtil;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-05-19
 */
// @Api(tags = "不常调用API检测管理")
@RestController
@RequestMapping("/api/apiInfrequentlycall")
public class ApiInfrequentlycallController {

    private final ApiInfrequentlycallService apiInfrequentlycallService;

    public ApiInfrequentlycallController(ApiInfrequentlycallService apiInfrequentlycallService) {
        this.apiInfrequentlycallService = apiInfrequentlycallService;
    }

    @Log("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, ApiInfrequentlycallQueryCriteria criteria) throws IOException {
        try {
            Timestamp dateStart = criteria.getDateStart();
            Timestamp dateEnd = criteria.getDateEnd();
            List<String> checkTime = new ArrayList<>();
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateStart));
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateEnd));
            criteria.setChecktime(checkTime);
        } catch (Exception e) {
            Console.log("未填写查询时间");
        }

        // 根据申请部门名称分域
        List<String> domainAkList = DomainUtil.queryDomainAk();
        criteria.setAk(domainAkList);

        apiInfrequentlycallService.download(apiInfrequentlycallService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询不常调用API检测")
    public ResponseEntity<Object> getApiInfrequentlycalls(ApiInfrequentlycallQueryCriteria criteria, Pageable pageable) {
        try {
            Timestamp dateStart = criteria.getDateStart();
            Timestamp dateEnd = criteria.getDateEnd();
            List<String> checkTime = new ArrayList<>();
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateStart));
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateEnd));
            criteria.setChecktime(checkTime);
        } catch (Exception e) {
            Console.log("未填写查询时间");
        }

        // 根据申请部门名称分域
        List<String> domainAkList = DomainUtil.queryDomainAk();
        criteria.setAk(domainAkList);

        return new ResponseEntity<>(apiInfrequentlycallService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增不常调用API检测")
    public ResponseEntity<Object> create(@Validated @RequestBody ApiInfrequentlycall resources) {
        return new ResponseEntity<>(apiInfrequentlycallService.create(resources), HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改不常调用API检测")
    public ResponseEntity<Object> update(@Validated @RequestBody ApiInfrequentlycall resources) {
        apiInfrequentlycallService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除不常调用API检测")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        apiInfrequentlycallService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
