package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcInvokingRecord;
import com.wzsec.modules.ic.service.dto.IcInvokingRecordDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:31+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class IcInvokingRecordMapperImpl implements IcInvokingRecordMapper {

    @Override
    public IcInvokingRecord toEntity(IcInvokingRecordDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcInvokingRecord icInvokingRecord = new IcInvokingRecord();

        icInvokingRecord.setId( dto.getId() );
        icInvokingRecord.setApicode( dto.getApicode() );
        icInvokingRecord.setApimethod( dto.getApimethod() );
        icInvokingRecord.setNum( dto.getNum() );
        icInvokingRecord.setAvgcall( dto.getAvgcall() );
        icInvokingRecord.setRisk( dto.getRisk() );
        icInvokingRecord.setDate( dto.getDate() );
        icInvokingRecord.setInsertdatetime( dto.getInsertdatetime() );
        icInvokingRecord.setSparefield1( dto.getSparefield1() );
        icInvokingRecord.setSparefield2( dto.getSparefield2() );
        icInvokingRecord.setSparefield3( dto.getSparefield3() );
        icInvokingRecord.setSparefield4( dto.getSparefield4() );
        icInvokingRecord.setSystemname( dto.getSystemname() );
        icInvokingRecord.setApiurl( dto.getApiurl() );
        icInvokingRecord.setReqip( dto.getReqip() );

        return icInvokingRecord;
    }

    @Override
    public IcInvokingRecordDto toDto(IcInvokingRecord entity) {
        if ( entity == null ) {
            return null;
        }

        IcInvokingRecordDto icInvokingRecordDto = new IcInvokingRecordDto();

        icInvokingRecordDto.setId( entity.getId() );
        icInvokingRecordDto.setApicode( entity.getApicode() );
        icInvokingRecordDto.setApimethod( entity.getApimethod() );
        icInvokingRecordDto.setNum( entity.getNum() );
        icInvokingRecordDto.setDate( entity.getDate() );
        icInvokingRecordDto.setInsertdatetime( entity.getInsertdatetime() );
        icInvokingRecordDto.setAvgcall( entity.getAvgcall() );
        icInvokingRecordDto.setRisk( entity.getRisk() );
        icInvokingRecordDto.setSparefield1( entity.getSparefield1() );
        icInvokingRecordDto.setSparefield2( entity.getSparefield2() );
        icInvokingRecordDto.setSparefield3( entity.getSparefield3() );
        icInvokingRecordDto.setSparefield4( entity.getSparefield4() );
        icInvokingRecordDto.setSystemname( entity.getSystemname() );
        icInvokingRecordDto.setApiurl( entity.getApiurl() );
        icInvokingRecordDto.setReqip( entity.getReqip() );

        return icInvokingRecordDto;
    }

    @Override
    public List<IcInvokingRecord> toEntity(List<IcInvokingRecordDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcInvokingRecord> list = new ArrayList<IcInvokingRecord>( dtoList.size() );
        for ( IcInvokingRecordDto icInvokingRecordDto : dtoList ) {
            list.add( toEntity( icInvokingRecordDto ) );
        }

        return list;
    }

    @Override
    public List<IcInvokingRecordDto> toDto(List<IcInvokingRecord> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcInvokingRecordDto> list = new ArrayList<IcInvokingRecordDto>( entityList.size() );
        for ( IcInvokingRecord icInvokingRecord : entityList ) {
            list.add( toDto( icInvokingRecord ) );
        }

        return list;
    }
}
