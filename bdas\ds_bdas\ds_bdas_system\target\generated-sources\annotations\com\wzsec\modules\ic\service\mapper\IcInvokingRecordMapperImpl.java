package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcInvokingRecord;
import com.wzsec.modules.ic.service.dto.IcInvokingRecordDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:30+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class IcInvokingRecordMapperImpl implements IcInvokingRecordMapper {

    @Override
    public IcInvokingRecordDto toDto(IcInvokingRecord entity) {
        if ( entity == null ) {
            return null;
        }

        IcInvokingRecordDto icInvokingRecordDto = new IcInvokingRecordDto();

        icInvokingRecordDto.setApicode( entity.getApicode() );
        icInvokingRecordDto.setApimethod( entity.getApimethod() );
        icInvokingRecordDto.setApiurl( entity.getApiurl() );
        icInvokingRecordDto.setAvgcall( entity.getAvgcall() );
        icInvokingRecordDto.setDate( entity.getDate() );
        icInvokingRecordDto.setId( entity.getId() );
        icInvokingRecordDto.setInsertdatetime( entity.getInsertdatetime() );
        icInvokingRecordDto.setNum( entity.getNum() );
        icInvokingRecordDto.setReqip( entity.getReqip() );
        icInvokingRecordDto.setRisk( entity.getRisk() );
        icInvokingRecordDto.setSparefield1( entity.getSparefield1() );
        icInvokingRecordDto.setSparefield2( entity.getSparefield2() );
        icInvokingRecordDto.setSparefield3( entity.getSparefield3() );
        icInvokingRecordDto.setSparefield4( entity.getSparefield4() );
        icInvokingRecordDto.setSystemname( entity.getSystemname() );

        return icInvokingRecordDto;
    }

    @Override
    public List<IcInvokingRecordDto> toDto(List<IcInvokingRecord> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcInvokingRecordDto> list = new ArrayList<IcInvokingRecordDto>( entityList.size() );
        for ( IcInvokingRecord icInvokingRecord : entityList ) {
            list.add( toDto( icInvokingRecord ) );
        }

        return list;
    }

    @Override
    public IcInvokingRecord toEntity(IcInvokingRecordDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcInvokingRecord icInvokingRecord = new IcInvokingRecord();

        icInvokingRecord.setApicode( dto.getApicode() );
        icInvokingRecord.setApimethod( dto.getApimethod() );
        icInvokingRecord.setApiurl( dto.getApiurl() );
        icInvokingRecord.setAvgcall( dto.getAvgcall() );
        icInvokingRecord.setDate( dto.getDate() );
        icInvokingRecord.setId( dto.getId() );
        icInvokingRecord.setInsertdatetime( dto.getInsertdatetime() );
        icInvokingRecord.setNum( dto.getNum() );
        icInvokingRecord.setReqip( dto.getReqip() );
        icInvokingRecord.setRisk( dto.getRisk() );
        icInvokingRecord.setSparefield1( dto.getSparefield1() );
        icInvokingRecord.setSparefield2( dto.getSparefield2() );
        icInvokingRecord.setSparefield3( dto.getSparefield3() );
        icInvokingRecord.setSparefield4( dto.getSparefield4() );
        icInvokingRecord.setSystemname( dto.getSystemname() );

        return icInvokingRecord;
    }

    @Override
    public List<IcInvokingRecord> toEntity(List<IcInvokingRecordDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcInvokingRecord> list = new ArrayList<IcInvokingRecord>( dtoList.size() );
        for ( IcInvokingRecordDto icInvokingRecordDto : dtoList ) {
            list.add( toEntity( icInvokingRecordDto ) );
        }

        return list;
    }
}
