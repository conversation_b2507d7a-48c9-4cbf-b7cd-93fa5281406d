package com.wzsec.modules.oc.service.mapper;

import com.wzsec.modules.oc.domain.OcLinuxResult;
import com.wzsec.modules.oc.service.dto.OcLinuxResultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:28+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class OcLinuxResultMapperImpl implements OcLinuxResultMapper {

    @Override
    public OcLinuxResultDto toDto(OcLinuxResult entity) {
        if ( entity == null ) {
            return null;
        }

        OcLinuxResultDto ocLinuxResultDto = new OcLinuxResultDto();

        ocLinuxResultDto.setChecktime( entity.getChecktime() );
        ocLinuxResultDto.setId( entity.getId() );
        ocLinuxResultDto.setIpaddress( entity.getIpaddress() );
        ocLinuxResultDto.setOperation( entity.getOperation() );
        ocLinuxResultDto.setOperationtime( entity.getOperationtime() );
        ocLinuxResultDto.setOperationtype( entity.getOperationtype() );
        ocLinuxResultDto.setRisk( entity.getRisk() );
        ocLinuxResultDto.setSparefield1( entity.getSparefield1() );
        ocLinuxResultDto.setSparefield2( entity.getSparefield2() );
        ocLinuxResultDto.setSparefield3( entity.getSparefield3() );
        ocLinuxResultDto.setSparefield4( entity.getSparefield4() );
        ocLinuxResultDto.setTaskname( entity.getTaskname() );
        ocLinuxResultDto.setUsername( entity.getUsername() );

        return ocLinuxResultDto;
    }

    @Override
    public List<OcLinuxResultDto> toDto(List<OcLinuxResult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<OcLinuxResultDto> list = new ArrayList<OcLinuxResultDto>( entityList.size() );
        for ( OcLinuxResult ocLinuxResult : entityList ) {
            list.add( toDto( ocLinuxResult ) );
        }

        return list;
    }

    @Override
    public OcLinuxResult toEntity(OcLinuxResultDto dto) {
        if ( dto == null ) {
            return null;
        }

        OcLinuxResult ocLinuxResult = new OcLinuxResult();

        ocLinuxResult.setChecktime( dto.getChecktime() );
        ocLinuxResult.setId( dto.getId() );
        ocLinuxResult.setIpaddress( dto.getIpaddress() );
        ocLinuxResult.setOperation( dto.getOperation() );
        ocLinuxResult.setOperationtime( dto.getOperationtime() );
        ocLinuxResult.setOperationtype( dto.getOperationtype() );
        ocLinuxResult.setRisk( dto.getRisk() );
        ocLinuxResult.setSparefield1( dto.getSparefield1() );
        ocLinuxResult.setSparefield2( dto.getSparefield2() );
        ocLinuxResult.setSparefield3( dto.getSparefield3() );
        ocLinuxResult.setSparefield4( dto.getSparefield4() );
        ocLinuxResult.setTaskname( dto.getTaskname() );
        ocLinuxResult.setUsername( dto.getUsername() );

        return ocLinuxResult;
    }

    @Override
    public List<OcLinuxResult> toEntity(List<OcLinuxResultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<OcLinuxResult> list = new ArrayList<OcLinuxResult>( dtoList.size() );
        for ( OcLinuxResultDto ocLinuxResultDto : dtoList ) {
            list.add( toEntity( ocLinuxResultDto ) );
        }

        return list;
    }
}
