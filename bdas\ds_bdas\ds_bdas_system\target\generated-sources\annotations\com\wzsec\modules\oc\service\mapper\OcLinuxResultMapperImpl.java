package com.wzsec.modules.oc.service.mapper;

import com.wzsec.modules.oc.domain.OcLinuxResult;
import com.wzsec.modules.oc.service.dto.OcLinuxResultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:30+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class OcLinuxResultMapperImpl implements OcLinuxResultMapper {

    @Override
    public OcLinuxResult toEntity(OcLinuxResultDto dto) {
        if ( dto == null ) {
            return null;
        }

        OcLinuxResult ocLinuxResult = new OcLinuxResult();

        ocLinuxResult.setId( dto.getId() );
        ocLinuxResult.setTaskname( dto.getTaskname() );
        ocLinuxResult.setOperationtime( dto.getOperationtime() );
        ocLinuxResult.setUsername( dto.getUsername() );
        ocLinuxResult.setIpaddress( dto.getIpaddress() );
        ocLinuxResult.setOperationtype( dto.getOperationtype() );
        ocLinuxResult.setOperation( dto.getOperation() );
        ocLinuxResult.setRisk( dto.getRisk() );
        ocLinuxResult.setChecktime( dto.getChecktime() );
        ocLinuxResult.setSparefield1( dto.getSparefield1() );
        ocLinuxResult.setSparefield2( dto.getSparefield2() );
        ocLinuxResult.setSparefield3( dto.getSparefield3() );
        ocLinuxResult.setSparefield4( dto.getSparefield4() );

        return ocLinuxResult;
    }

    @Override
    public OcLinuxResultDto toDto(OcLinuxResult entity) {
        if ( entity == null ) {
            return null;
        }

        OcLinuxResultDto ocLinuxResultDto = new OcLinuxResultDto();

        ocLinuxResultDto.setId( entity.getId() );
        ocLinuxResultDto.setTaskname( entity.getTaskname() );
        ocLinuxResultDto.setOperationtime( entity.getOperationtime() );
        ocLinuxResultDto.setUsername( entity.getUsername() );
        ocLinuxResultDto.setIpaddress( entity.getIpaddress() );
        ocLinuxResultDto.setOperationtype( entity.getOperationtype() );
        ocLinuxResultDto.setOperation( entity.getOperation() );
        ocLinuxResultDto.setRisk( entity.getRisk() );
        ocLinuxResultDto.setChecktime( entity.getChecktime() );
        ocLinuxResultDto.setSparefield1( entity.getSparefield1() );
        ocLinuxResultDto.setSparefield2( entity.getSparefield2() );
        ocLinuxResultDto.setSparefield3( entity.getSparefield3() );
        ocLinuxResultDto.setSparefield4( entity.getSparefield4() );

        return ocLinuxResultDto;
    }

    @Override
    public List<OcLinuxResult> toEntity(List<OcLinuxResultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<OcLinuxResult> list = new ArrayList<OcLinuxResult>( dtoList.size() );
        for ( OcLinuxResultDto ocLinuxResultDto : dtoList ) {
            list.add( toEntity( ocLinuxResultDto ) );
        }

        return list;
    }

    @Override
    public List<OcLinuxResultDto> toDto(List<OcLinuxResult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<OcLinuxResultDto> list = new ArrayList<OcLinuxResultDto>( entityList.size() );
        for ( OcLinuxResult ocLinuxResult : entityList ) {
            list.add( toDto( ocLinuxResult ) );
        }

        return list;
    }
}
