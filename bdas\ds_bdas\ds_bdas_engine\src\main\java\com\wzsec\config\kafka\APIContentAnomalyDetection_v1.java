package com.wzsec.config.kafka;

import cn.hutool.core.lang.Console;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.wzsec.config.domin.KafkaEvent;
import com.wzsec.modules.ic.domain.IcAlarmdisposal;
import com.wzsec.modules.ic.domain.enums.RiskEnum;
import com.wzsec.modules.system.service.DictDetailService;
import com.wzsec.utils.Const;
import com.wzsec.utils.ConstEngine;
import com.wzsec.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * API 内容异常检测
 *
 * <AUTHOR>
 * @date 2024-04-02
 */
@Slf4j
@Component
public class APIContentAnomalyDetection_v1 {

    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;

    @Autowired
    private DictDetailService dictDetailService;

    private static APIContentAnomalyDetection_v1 apiContentAnomalyDetection;

    private static final String TOPIC = ConstEngine.sddEngineConfs.get("alarmInformationKafka.topic");

    private static final String EVENT_CATEGORY = "api"; //类别,固定值
    private static final String EVENT_ZONE = "维祯API"; //区域,固定值

    private static final String RULE_CATEGORY = "share"; //share,规则类型 固定值
    private static final String RULE_SUBCATEGORY = "api"; // api,规则子类型 固定值
    private static final String RULE_TYPE = "external"; //external 固定值
    private static final String RULE_LIFECYCLE = "交换"; //交换 固定值
    private static final String RULE_TAGS = "{\"非法访问\", \"内容异常检测\", \"共享交换平台\"}"; //标签,自定义,非必填

    private static final String DICT_WHETHER_INTO_KAFKA = "whether_into_kafka"; // 字典名(查询字段名称,及是否推送kafka)
    private static final String DICT_KAFKA_PUSH_INTERFACE = "kafka_push_interface"; //字典名(推送接口列表)

    @PostConstruct
    public void init() {
        apiContentAnomalyDetection = this;
        apiContentAnomalyDetection.kafkaTemplate = this.kafkaTemplate;
        apiContentAnomalyDetection.dictDetailService = this.dictDetailService;
    }

    /**
     * 查询是否推送
     *
     * @return {@link Boolean}
     */
    private static Boolean queryWhetherToPush(IcAlarmdisposal icAlarmdisposal) {
        // 字段获取是否予以推送 whether_into_kafka
        Map<String, String> whetherIntoKafkaMap = apiContentAnomalyDetection.dictDetailService.getDictDetailMap(DICT_WHETHER_INTO_KAFKA);

        // 检查是否有相关键 whether_into_kafka
        if (!whetherIntoKafkaMap.containsKey(DICT_WHETHER_INTO_KAFKA)) {
            return false;
        }

        // 检查是否需要推送到 Kafka
        if (!whetherIntoKafkaMap.get(DICT_WHETHER_INTO_KAFKA).equals(Const.EVENT_INTO_KAFKA)) {
            return false;
        }

        // 检查是否存在推送接口列表
        if (!whetherIntoKafkaMap.containsKey(DICT_KAFKA_PUSH_INTERFACE)) {
            return true; // 如果没有推送接口列表字段，则默认需要推送所有内容
        }

        // 获取推送接口列表并检查是否为空或包含当前接口
        List<String> itemList = Arrays.asList(whetherIntoKafkaMap.get(DICT_KAFKA_PUSH_INTERFACE).split(","));
        List<String> filteredList = itemList.stream()
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        return filteredList.isEmpty() || (filteredList.contains(icAlarmdisposal.getApicode()));
    }

    /**
     * 查询是否推送(告警过滤菜单配置黑名单)
     *
     * @return {@link Boolean}
     */
    private static Boolean queryWhetherToPush(IcAlarmdisposal icAlarmdisposal, List<String> blackApiCodeList) {
        // 字段获取是否予以推送 whether_into_kafka
        Map<String, String> whetherIntoKafkaMap = apiContentAnomalyDetection.dictDetailService.getDictDetailMap(DICT_WHETHER_INTO_KAFKA);

        // 检查是否有相关键 whether_into_kafka
        if (!whetherIntoKafkaMap.containsKey(DICT_WHETHER_INTO_KAFKA)) {
            return false;
        }

        // 检查是否需要推送到 Kafka
        if (!whetherIntoKafkaMap.get(DICT_WHETHER_INTO_KAFKA).equals(Const.EVENT_INTO_KAFKA)) {
            return false;
        }

        return blackApiCodeList.isEmpty() || (blackApiCodeList.contains(icAlarmdisposal.getApicode()));
    }


    /**
     * 非预期内容送Kafka [AP001, AP002, AP003]
     *
     * @param icAlarmdisposal 告警对象
     * @param subProjects     子项
     */
    public static void unexpectedFieldValuePushKafka(IcAlarmdisposal icAlarmdisposal, String subProjects, List<String> blackApiCodeList) {

        if (queryWhetherToPush(icAlarmdisposal, blackApiCodeList)) {
            KafkaEvent kafkaEvent = new KafkaEvent();
            kafkaEvent.setEvent(new KafkaEvent.Event(
                    IdUtil.simpleUUID(),
                    DateUtils.getDateTime(),
                    EVENT_CATEGORY,
                    EVENT_ZONE));

            if (subProjects.equals(Const.API_UNEXPECTED_METHOD)) { //方法
                kafkaEvent.setRule(new KafkaEvent.Rule(
                        Const.API_ALERT_UNEXPECTED_METHOD,
                        Const.API_UNEXPECTED_METHOD,
                        Const.API_ALERT_UNEXPECTED_METHOD_DESCRIPTION,
                        RiskEnum.getRisk(icAlarmdisposal.getRisk()) + "危",
                        RULE_CATEGORY,
                        RULE_SUBCATEGORY,
                        RULE_TYPE,
                        RULE_LIFECYCLE,
                        RULE_TAGS));
            } else if (subProjects.equals(Const.API_UNEXPECTED_FIELD_NAME)) { //字段名
                kafkaEvent.setRule(new KafkaEvent.Rule(
                        Const.API_ALERT_UNEXPECTED_FIELD_NAME,
                        Const.API_UNEXPECTED_FIELD_VALUE,
                        Const.API_ALERT_UNEXPECTED_FIELD_NAME_DESCRIPTION,
                        RiskEnum.getRisk(icAlarmdisposal.getRisk()) + "危",
                        RULE_CATEGORY,
                        RULE_SUBCATEGORY,
                        RULE_TYPE,
                        RULE_LIFECYCLE,
                        RULE_TAGS));
            } else if (subProjects.equals(Const.API_UNEXPECTED_FIELD_VALUE)) { //字段值
                kafkaEvent.setRule(new KafkaEvent.Rule(
                        Const.API_ALERT_UNEXPECTED_FIELD_VALUE,
                        Const.API_UNEXPECTED_FIELD_VALUE,
                        Const.API_ALERT_UNEXPECTED_FIELD_VALUE_DESCRIPTION,
                        RiskEnum.getRisk(icAlarmdisposal.getRisk()) + "危",
                        RULE_CATEGORY,
                        RULE_SUBCATEGORY,
                        RULE_TYPE,
                        RULE_LIFECYCLE,
                        RULE_TAGS));
            }

            kafkaEvent.setMessage(icAlarmdisposal.getCircumstantiality() == null ? " " : icAlarmdisposal.getCircumstantiality());
            kafkaEvent.setDestination(new KafkaEvent.Destination(
                    icAlarmdisposal.getSourceip() == null ? "" : icAlarmdisposal.getSourceip(),
                    ""));
            kafkaEvent.setSource(new KafkaEvent.Source(
                    icAlarmdisposal.getDestinationip() == null ? "" : icAlarmdisposal.getDestinationip(),
                    ""));
            kafkaEvent.setUser(new KafkaEvent.User(icAlarmdisposal.getAk()));
            kafkaEvent.setProtocol("http");
            kafkaEvent.setUrl(icAlarmdisposal.getApiurl());
            kafkaEvent.setSrcDepartmentName(icAlarmdisposal.getReqdepartment());
            kafkaEvent.setSrcBusinessName(icAlarmdisposal.getReqapp());
            kafkaEvent.setDepartmentName(icAlarmdisposal.getIcresdepartment());
            kafkaEvent.setBusinessName(icAlarmdisposal.getIcresdepartment());
            String message = JSONUtil.toJsonStr(kafkaEvent);
            log.info(message);

            ListenableFuture<SendResult<String, String>> future
                    = apiContentAnomalyDetection.kafkaTemplate.send(TOPIC, message);

            future.addCallback(success -> Console.log("非预期内容 " + subProjects + "发送消息成功！"),
                    fail -> Console.log("非预期内容 " + subProjects + " 发送消息失败！"));
        }
    }


    /**
     * API暴露敏感数据推送kafka [AP013]
     *
     * @param icAlarmdisposal 告警对象
     */
    public static void apiExposingSensitiveDataPushKafka_v1(IcAlarmdisposal icAlarmdisposal, List<String> blackApiCodeList) {

        if (queryWhetherToPush(icAlarmdisposal, blackApiCodeList)) {
            KafkaEvent kafkaEvent = new KafkaEvent();
            kafkaEvent.setEvent(new KafkaEvent.Event(
                    IdUtil.simpleUUID(),
                    DateUtils.getDateTime(),
                    EVENT_CATEGORY,
                    EVENT_ZONE));
            kafkaEvent.setRule(new KafkaEvent.Rule(
                    Const.API_ALERT_EXPOSE_SENSITIVE,
                    Const.API_EXPOSE_SENSITIVE_DATA,
                    Const.API_ALERT_EXPOSE_SENSITIVE_DESCRIPTION,
                    RiskEnum.getRisk(icAlarmdisposal.getRisk()) + "危",
                    RULE_CATEGORY,
                    RULE_SUBCATEGORY,
                    RULE_TYPE,
                    RULE_LIFECYCLE,
                    RULE_TAGS));

            kafkaEvent.setMessage(icAlarmdisposal.getCircumstantiality() == null ? " " : icAlarmdisposal.getCircumstantiality());
            kafkaEvent.setDestination(new KafkaEvent.Destination(
                    icAlarmdisposal.getSourceip() == null ? "" : icAlarmdisposal.getSourceip(),
                    ""));
            kafkaEvent.setSource(new KafkaEvent.Source(
                    icAlarmdisposal.getDestinationip() == null ? "" : icAlarmdisposal.getDestinationip(),
                    ""));
            kafkaEvent.setUser(new KafkaEvent.User(icAlarmdisposal.getAk()));
            kafkaEvent.setProtocol("http");
            kafkaEvent.setUrl(icAlarmdisposal.getApiurl());
            kafkaEvent.setSrcDepartmentName(icAlarmdisposal.getReqdepartment()); //源部门名称
            kafkaEvent.setSrcBusinessName(icAlarmdisposal.getReqapp()); //源业务系统名称
            kafkaEvent.setDepartmentName(icAlarmdisposal.getIcresdepartment()); //目标部门名称
            kafkaEvent.setBusinessName(icAlarmdisposal.getIcresdepartment()); //目标业务系统名称
            String message = JSONUtil.toJsonStr(kafkaEvent);
            log.info(message);

            ListenableFuture<SendResult<String, String>> future
                    = apiContentAnomalyDetection.kafkaTemplate.send(TOPIC, message);

            future.addCallback(success -> Console.log("[AP013]_API暴露敏感数据 发送消息成功！"),
                    fail -> Console.log("[AP013]_API暴露敏感数据 发送消息失败！"));
        }
    }


    /**
     * API攻击检测告警信息推送Kafka [AP015]
     *
     * @param icAlarmdisposal 告警对象
     */
    public static void apiAttackDetectionAlarmInformationPushedKafka(IcAlarmdisposal icAlarmdisposal, List<String> blackApiCodeList) {

        if (queryWhetherToPush(icAlarmdisposal, blackApiCodeList)) {
            KafkaEvent kafkaEvent = new KafkaEvent();
            kafkaEvent.setEvent(new KafkaEvent.Event(
                    IdUtil.simpleUUID(),
                    DateUtils.getDateTime(),
                    EVENT_CATEGORY,
                    EVENT_ZONE));
            kafkaEvent.setRule(new KafkaEvent.Rule(
                    Const.API_ALERT_PARAMETER_ENUMERATION_ATTACK,
                    Const.API_PARAMETER_ENUMERATION_ATTACK,
                    Const.API_ALERT_PARAMETER_ENUMERATION_ATTACK_DESCRIPTION,
                    RiskEnum.getRisk(icAlarmdisposal.getRisk()) + "危",
                    RULE_CATEGORY,
                    RULE_SUBCATEGORY,
                    RULE_TYPE,
                    RULE_LIFECYCLE,
                    RULE_TAGS));

            kafkaEvent.setMessage(icAlarmdisposal.getCircumstantiality() == null ? " " : icAlarmdisposal.getCircumstantiality());
            kafkaEvent.setDestination(new KafkaEvent.Destination(
                    icAlarmdisposal.getSourceip() == null ? "" : icAlarmdisposal.getSourceip(),
                    ""));
            kafkaEvent.setSource(new KafkaEvent.Source(
                    icAlarmdisposal.getDestinationip() == null ? "" : icAlarmdisposal.getDestinationip(),
                    ""));
            kafkaEvent.setUser(new KafkaEvent.User(icAlarmdisposal.getAk()));
            kafkaEvent.setProtocol("http");
            kafkaEvent.setUrl(icAlarmdisposal.getApiurl());
            kafkaEvent.setSrcDepartmentName(icAlarmdisposal.getReqdepartment());
            kafkaEvent.setSrcBusinessName(icAlarmdisposal.getReqapp());
            kafkaEvent.setDepartmentName(icAlarmdisposal.getIcresdepartment());
            kafkaEvent.setBusinessName(icAlarmdisposal.getIcresdepartment());
            String message = JSONUtil.toJsonStr(kafkaEvent);
            log.info(message);

            ListenableFuture<SendResult<String, String>> future
                    = apiContentAnomalyDetection.kafkaTemplate.send(TOPIC, message);
            future.addCallback(success -> Console.log("[AP015]_API攻击检测 发送消息成功！"),
                    fail -> Console.log("[AP015]_API攻击检测 发送消息失败！"));
        }
    }

}
