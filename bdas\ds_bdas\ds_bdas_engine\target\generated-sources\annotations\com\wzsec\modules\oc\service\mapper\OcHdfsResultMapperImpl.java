package com.wzsec.modules.oc.service.mapper;

import com.wzsec.modules.oc.domain.OcHdfsResult;
import com.wzsec.modules.oc.service.dto.OcHdfsResultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:52+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class OcHdfsResultMapperImpl implements OcHdfsResultMapper {

    @Override
    public OcHdfsResultDto toDto(OcHdfsResult entity) {
        if ( entity == null ) {
            return null;
        }

        OcHdfsResultDto ocHdfsResultDto = new OcHdfsResultDto();

        ocHdfsResultDto.setChecktime( entity.getChecktime() );
        ocHdfsResultDto.setId( entity.getId() );
        ocHdfsResultDto.setIpaddress( entity.getIpaddress() );
        ocHdfsResultDto.setOperation( entity.getOperation() );
        ocHdfsResultDto.setOperationtime( entity.getOperationtime() );
        ocHdfsResultDto.setOperationtype( entity.getOperationtype() );
        ocHdfsResultDto.setPath( entity.getPath() );
        ocHdfsResultDto.setRisk( entity.getRisk() );
        ocHdfsResultDto.setSparefield1( entity.getSparefield1() );
        ocHdfsResultDto.setSparefield2( entity.getSparefield2() );
        ocHdfsResultDto.setSparefield3( entity.getSparefield3() );
        ocHdfsResultDto.setSparefield4( entity.getSparefield4() );
        ocHdfsResultDto.setTaskname( entity.getTaskname() );
        ocHdfsResultDto.setUsername( entity.getUsername() );

        return ocHdfsResultDto;
    }

    @Override
    public List<OcHdfsResultDto> toDto(List<OcHdfsResult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<OcHdfsResultDto> list = new ArrayList<OcHdfsResultDto>( entityList.size() );
        for ( OcHdfsResult ocHdfsResult : entityList ) {
            list.add( toDto( ocHdfsResult ) );
        }

        return list;
    }

    @Override
    public OcHdfsResult toEntity(OcHdfsResultDto dto) {
        if ( dto == null ) {
            return null;
        }

        OcHdfsResult ocHdfsResult = new OcHdfsResult();

        ocHdfsResult.setChecktime( dto.getChecktime() );
        ocHdfsResult.setId( dto.getId() );
        ocHdfsResult.setIpaddress( dto.getIpaddress() );
        ocHdfsResult.setOperation( dto.getOperation() );
        ocHdfsResult.setOperationtime( dto.getOperationtime() );
        ocHdfsResult.setOperationtype( dto.getOperationtype() );
        ocHdfsResult.setPath( dto.getPath() );
        ocHdfsResult.setRisk( dto.getRisk() );
        ocHdfsResult.setSparefield1( dto.getSparefield1() );
        ocHdfsResult.setSparefield2( dto.getSparefield2() );
        ocHdfsResult.setSparefield3( dto.getSparefield3() );
        ocHdfsResult.setSparefield4( dto.getSparefield4() );
        ocHdfsResult.setTaskname( dto.getTaskname() );
        ocHdfsResult.setUsername( dto.getUsername() );

        return ocHdfsResult;
    }

    @Override
    public List<OcHdfsResult> toEntity(List<OcHdfsResultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<OcHdfsResult> list = new ArrayList<OcHdfsResult>( dtoList.size() );
        for ( OcHdfsResultDto ocHdfsResultDto : dtoList ) {
            list.add( toEntity( ocHdfsResultDto ) );
        }

        return list;
    }
}
