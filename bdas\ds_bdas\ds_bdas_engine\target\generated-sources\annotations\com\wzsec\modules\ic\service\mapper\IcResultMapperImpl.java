package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcResult;
import com.wzsec.modules.ic.service.dto.IcResultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:02+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class IcResultMapperImpl implements IcResultMapper {

    @Override
    public IcResult toEntity(IcResultDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcResult icResult = new IcResult();

        icResult.setId( dto.getId() );
        icResult.setTaskname( dto.getTaskname() );
        icResult.setLogsign( dto.getLogsign() );
        icResult.setApicode( dto.getApicode() );
        icResult.setApimethod( dto.getApimethod() );
        icResult.setUserid( dto.getUserid() );
        icResult.setRisk( dto.getRisk() );
        icResult.setCheckcount( dto.getCheckcount() );
        icResult.setChecktime( dto.getChecktime() );
        icResult.setSparefield1( dto.getSparefield1() );
        icResult.setSparefield2( dto.getSparefield2() );
        icResult.setSparefield3( dto.getSparefield3() );
        icResult.setSparefield4( dto.getSparefield4() );
        icResult.setApiname( dto.getApiname() );
        icResult.setApiurl( dto.getApiurl() );
        icResult.setAk( dto.getAk() );
        icResult.setApplyorgname( dto.getApplyorgname() );
        icResult.setAkapicode( dto.getAkapicode() );

        return icResult;
    }

    @Override
    public IcResultDto toDto(IcResult entity) {
        if ( entity == null ) {
            return null;
        }

        IcResultDto icResultDto = new IcResultDto();

        icResultDto.setId( entity.getId() );
        icResultDto.setTaskname( entity.getTaskname() );
        icResultDto.setLogsign( entity.getLogsign() );
        icResultDto.setApicode( entity.getApicode() );
        icResultDto.setApimethod( entity.getApimethod() );
        icResultDto.setUserid( entity.getUserid() );
        icResultDto.setRisk( entity.getRisk() );
        icResultDto.setCheckcount( entity.getCheckcount() );
        icResultDto.setChecktime( entity.getChecktime() );
        icResultDto.setSparefield1( entity.getSparefield1() );
        icResultDto.setSparefield2( entity.getSparefield2() );
        icResultDto.setSparefield3( entity.getSparefield3() );
        icResultDto.setSparefield4( entity.getSparefield4() );
        icResultDto.setApiname( entity.getApiname() );
        icResultDto.setApiurl( entity.getApiurl() );
        icResultDto.setAk( entity.getAk() );
        icResultDto.setApplyorgname( entity.getApplyorgname() );
        icResultDto.setAkapicode( entity.getAkapicode() );

        return icResultDto;
    }

    @Override
    public List<IcResult> toEntity(List<IcResultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcResult> list = new ArrayList<IcResult>( dtoList.size() );
        for ( IcResultDto icResultDto : dtoList ) {
            list.add( toEntity( icResultDto ) );
        }

        return list;
    }

    @Override
    public List<IcResultDto> toDto(List<IcResult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcResultDto> list = new ArrayList<IcResultDto>( entityList.size() );
        for ( IcResult icResult : entityList ) {
            list.add( toDto( icResult ) );
        }

        return list;
    }
}
