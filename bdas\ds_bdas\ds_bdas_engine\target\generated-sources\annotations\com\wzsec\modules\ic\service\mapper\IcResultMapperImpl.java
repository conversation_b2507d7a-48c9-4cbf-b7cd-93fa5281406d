package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcResult;
import com.wzsec.modules.ic.service.dto.IcResultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:53+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class IcResultMapperImpl implements IcResultMapper {

    @Override
    public IcResultDto toDto(IcResult entity) {
        if ( entity == null ) {
            return null;
        }

        IcResultDto icResultDto = new IcResultDto();

        icResultDto.setAk( entity.getAk() );
        icResultDto.setAkapicode( entity.getAkapicode() );
        icResultDto.setApicode( entity.getApicode() );
        icResultDto.setApimethod( entity.getApimethod() );
        icResultDto.setApiname( entity.getApiname() );
        icResultDto.setApiurl( entity.getApiurl() );
        icResultDto.setApplyorgname( entity.getApplyorgname() );
        icResultDto.setCheckcount( entity.getCheckcount() );
        icResultDto.setChecktime( entity.getChecktime() );
        icResultDto.setId( entity.getId() );
        icResultDto.setLogsign( entity.getLogsign() );
        icResultDto.setRisk( entity.getRisk() );
        icResultDto.setSparefield1( entity.getSparefield1() );
        icResultDto.setSparefield2( entity.getSparefield2() );
        icResultDto.setSparefield3( entity.getSparefield3() );
        icResultDto.setSparefield4( entity.getSparefield4() );
        icResultDto.setTaskname( entity.getTaskname() );
        icResultDto.setUserid( entity.getUserid() );

        return icResultDto;
    }

    @Override
    public List<IcResultDto> toDto(List<IcResult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcResultDto> list = new ArrayList<IcResultDto>( entityList.size() );
        for ( IcResult icResult : entityList ) {
            list.add( toDto( icResult ) );
        }

        return list;
    }

    @Override
    public IcResult toEntity(IcResultDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcResult icResult = new IcResult();

        icResult.setAk( dto.getAk() );
        icResult.setAkapicode( dto.getAkapicode() );
        icResult.setApicode( dto.getApicode() );
        icResult.setApimethod( dto.getApimethod() );
        icResult.setApiname( dto.getApiname() );
        icResult.setApiurl( dto.getApiurl() );
        icResult.setApplyorgname( dto.getApplyorgname() );
        icResult.setCheckcount( dto.getCheckcount() );
        icResult.setChecktime( dto.getChecktime() );
        icResult.setId( dto.getId() );
        icResult.setLogsign( dto.getLogsign() );
        icResult.setRisk( dto.getRisk() );
        icResult.setSparefield1( dto.getSparefield1() );
        icResult.setSparefield2( dto.getSparefield2() );
        icResult.setSparefield3( dto.getSparefield3() );
        icResult.setSparefield4( dto.getSparefield4() );
        icResult.setTaskname( dto.getTaskname() );
        icResult.setUserid( dto.getUserid() );

        return icResult;
    }

    @Override
    public List<IcResult> toEntity(List<IcResultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcResult> list = new ArrayList<IcResult>( dtoList.size() );
        for ( IcResultDto icResultDto : dtoList ) {
            list.add( toEntity( icResultDto ) );
        }

        return list;
    }
}
