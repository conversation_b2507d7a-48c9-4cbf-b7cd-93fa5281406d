package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcCheckmodel;
import com.wzsec.modules.ic.service.dto.IcCheckmodelDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:02+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class IcCheckmodelMapperImpl implements IcCheckmodelMapper {

    @Override
    public IcCheckmodel toEntity(IcCheckmodelDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcCheckmodel icCheckmodel = new IcCheckmodel();

        icCheckmodel.setId( dto.getId() );
        icCheckmodel.setEventid( dto.getEventid() );
        icCheckmodel.setEventname( dto.getEventname() );
        icCheckmodel.setBusinesscatagory( dto.getBusinesscatagory() );
        icCheckmodel.setBehaviourcatagory( dto.getBehaviourcatagory() );
        icCheckmodel.setSource( dto.getSource() );
        icCheckmodel.setRuledescription( dto.getRuledescription() );
        icCheckmodel.setEventlevel( dto.getEventlevel() );
        icCheckmodel.setStatus( dto.getStatus() );
        icCheckmodel.setModelconfig( dto.getModelconfig() );
        icCheckmodel.setModeltask( dto.getModeltask() );
        icCheckmodel.setSparefield1( dto.getSparefield1() );
        icCheckmodel.setSparefield2( dto.getSparefield2() );
        icCheckmodel.setSparefield3( dto.getSparefield3() );
        icCheckmodel.setSparefield4( dto.getSparefield4() );
        icCheckmodel.setModelparameter( dto.getModelparameter() );

        return icCheckmodel;
    }

    @Override
    public IcCheckmodelDto toDto(IcCheckmodel entity) {
        if ( entity == null ) {
            return null;
        }

        IcCheckmodelDto icCheckmodelDto = new IcCheckmodelDto();

        icCheckmodelDto.setId( entity.getId() );
        icCheckmodelDto.setEventid( entity.getEventid() );
        icCheckmodelDto.setEventname( entity.getEventname() );
        icCheckmodelDto.setBusinesscatagory( entity.getBusinesscatagory() );
        icCheckmodelDto.setBehaviourcatagory( entity.getBehaviourcatagory() );
        icCheckmodelDto.setSource( entity.getSource() );
        icCheckmodelDto.setRuledescription( entity.getRuledescription() );
        icCheckmodelDto.setEventlevel( entity.getEventlevel() );
        icCheckmodelDto.setStatus( entity.getStatus() );
        icCheckmodelDto.setModelconfig( entity.getModelconfig() );
        icCheckmodelDto.setModeltask( entity.getModeltask() );
        icCheckmodelDto.setSparefield1( entity.getSparefield1() );
        icCheckmodelDto.setSparefield2( entity.getSparefield2() );
        icCheckmodelDto.setSparefield3( entity.getSparefield3() );
        icCheckmodelDto.setSparefield4( entity.getSparefield4() );
        icCheckmodelDto.setModelparameter( entity.getModelparameter() );

        return icCheckmodelDto;
    }

    @Override
    public List<IcCheckmodel> toEntity(List<IcCheckmodelDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcCheckmodel> list = new ArrayList<IcCheckmodel>( dtoList.size() );
        for ( IcCheckmodelDto icCheckmodelDto : dtoList ) {
            list.add( toEntity( icCheckmodelDto ) );
        }

        return list;
    }

    @Override
    public List<IcCheckmodelDto> toDto(List<IcCheckmodel> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcCheckmodelDto> list = new ArrayList<IcCheckmodelDto>( entityList.size() );
        for ( IcCheckmodel icCheckmodel : entityList ) {
            list.add( toDto( icCheckmodel ) );
        }

        return list;
    }
}
