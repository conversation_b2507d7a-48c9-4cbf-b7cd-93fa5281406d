package com.wzsec.modules.oc.service.mapper;

import com.wzsec.modules.oc.domain.OcUseraccount;
import com.wzsec.modules.oc.service.dto.OcUseraccountDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:27:08+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class OcUseraccountMapperImpl implements OcUseraccountMapper {

    @Override
    public OcUseraccountDto toDto(OcUseraccount entity) {
        if ( entity == null ) {
            return null;
        }

        OcUseraccountDto ocUseraccountDto = new OcUseraccountDto();

        ocUseraccountDto.setAccount( entity.getAccount() );
        ocUseraccountDto.setAccountarea( entity.getAccountarea() );
        ocUseraccountDto.setAccountstatus( entity.getAccountstatus() );
        ocUseraccountDto.setAccounttype( entity.getAccounttype() );
        ocUseraccountDto.setCname( entity.getCname() );
        ocUseraccountDto.setConnecttype( entity.getConnecttype() );
        ocUseraccountDto.setCreatetime( entity.getCreatetime() );
        ocUseraccountDto.setDepartment( entity.getDepartment() );
        ocUseraccountDto.setEname( entity.getEname() );
        ocUseraccountDto.setGroupname( entity.getGroupname() );
        ocUseraccountDto.setId( entity.getId() );
        ocUseraccountDto.setJobstatus( entity.getJobstatus() );
        ocUseraccountDto.setMail( entity.getMail() );
        ocUseraccountDto.setNote( entity.getNote() );
        ocUseraccountDto.setProperty( entity.getProperty() );
        ocUseraccountDto.setPurpose( entity.getPurpose() );
        ocUseraccountDto.setReservefield1( entity.getReservefield1() );
        ocUseraccountDto.setReservefield2( entity.getReservefield2() );
        ocUseraccountDto.setReservefield3( entity.getReservefield3() );
        ocUseraccountDto.setReservefield4( entity.getReservefield4() );
        ocUseraccountDto.setServerip( entity.getServerip() );
        ocUseraccountDto.setUpdatetime( entity.getUpdatetime() );
        ocUseraccountDto.setWorkunit( entity.getWorkunit() );

        return ocUseraccountDto;
    }

    @Override
    public List<OcUseraccountDto> toDto(List<OcUseraccount> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<OcUseraccountDto> list = new ArrayList<OcUseraccountDto>( entityList.size() );
        for ( OcUseraccount ocUseraccount : entityList ) {
            list.add( toDto( ocUseraccount ) );
        }

        return list;
    }

    @Override
    public OcUseraccount toEntity(OcUseraccountDto dto) {
        if ( dto == null ) {
            return null;
        }

        OcUseraccount ocUseraccount = new OcUseraccount();

        ocUseraccount.setAccount( dto.getAccount() );
        ocUseraccount.setAccountarea( dto.getAccountarea() );
        ocUseraccount.setAccountstatus( dto.getAccountstatus() );
        ocUseraccount.setAccounttype( dto.getAccounttype() );
        ocUseraccount.setCname( dto.getCname() );
        ocUseraccount.setConnecttype( dto.getConnecttype() );
        ocUseraccount.setCreatetime( dto.getCreatetime() );
        ocUseraccount.setDepartment( dto.getDepartment() );
        ocUseraccount.setEname( dto.getEname() );
        ocUseraccount.setGroupname( dto.getGroupname() );
        ocUseraccount.setId( dto.getId() );
        ocUseraccount.setJobstatus( dto.getJobstatus() );
        ocUseraccount.setMail( dto.getMail() );
        ocUseraccount.setNote( dto.getNote() );
        ocUseraccount.setProperty( dto.getProperty() );
        ocUseraccount.setPurpose( dto.getPurpose() );
        ocUseraccount.setReservefield1( dto.getReservefield1() );
        ocUseraccount.setReservefield2( dto.getReservefield2() );
        ocUseraccount.setReservefield3( dto.getReservefield3() );
        ocUseraccount.setReservefield4( dto.getReservefield4() );
        ocUseraccount.setServerip( dto.getServerip() );
        ocUseraccount.setUpdatetime( dto.getUpdatetime() );
        ocUseraccount.setWorkunit( dto.getWorkunit() );

        return ocUseraccount;
    }

    @Override
    public List<OcUseraccount> toEntity(List<OcUseraccountDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<OcUseraccount> list = new ArrayList<OcUseraccount>( dtoList.size() );
        for ( OcUseraccountDto ocUseraccountDto : dtoList ) {
            list.add( toEntity( ocUseraccountDto ) );
        }

        return list;
    }
}
