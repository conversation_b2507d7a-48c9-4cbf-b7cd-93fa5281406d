package com.wzsec.modules.oc.service.mapper;

import com.wzsec.modules.oc.domain.OcUseraccount;
import com.wzsec.modules.oc.service.dto.OcUseraccountDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:02+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class OcUseraccountMapperImpl implements OcUseraccountMapper {

    @Override
    public OcUseraccount toEntity(OcUseraccountDto dto) {
        if ( dto == null ) {
            return null;
        }

        OcUseraccount ocUseraccount = new OcUseraccount();

        ocUseraccount.setId( dto.getId() );
        ocUseraccount.setCname( dto.getCname() );
        ocUseraccount.setEname( dto.getEname() );
        ocUseraccount.setMail( dto.getMail() );
        ocUseraccount.setWorkunit( dto.getWorkunit() );
        ocUseraccount.setDepartment( dto.getDepartment() );
        ocUseraccount.setGroupname( dto.getGroupname() );
        ocUseraccount.setProperty( dto.getProperty() );
        ocUseraccount.setServerip( dto.getServerip() );
        ocUseraccount.setConnecttype( dto.getConnecttype() );
        ocUseraccount.setAccounttype( dto.getAccounttype() );
        ocUseraccount.setAccount( dto.getAccount() );
        ocUseraccount.setPurpose( dto.getPurpose() );
        ocUseraccount.setAccountstatus( dto.getAccountstatus() );
        ocUseraccount.setCreatetime( dto.getCreatetime() );
        ocUseraccount.setUpdatetime( dto.getUpdatetime() );
        ocUseraccount.setJobstatus( dto.getJobstatus() );
        ocUseraccount.setAccountarea( dto.getAccountarea() );
        ocUseraccount.setNote( dto.getNote() );
        ocUseraccount.setReservefield1( dto.getReservefield1() );
        ocUseraccount.setReservefield2( dto.getReservefield2() );
        ocUseraccount.setReservefield3( dto.getReservefield3() );
        ocUseraccount.setReservefield4( dto.getReservefield4() );

        return ocUseraccount;
    }

    @Override
    public OcUseraccountDto toDto(OcUseraccount entity) {
        if ( entity == null ) {
            return null;
        }

        OcUseraccountDto ocUseraccountDto = new OcUseraccountDto();

        ocUseraccountDto.setId( entity.getId() );
        ocUseraccountDto.setCname( entity.getCname() );
        ocUseraccountDto.setEname( entity.getEname() );
        ocUseraccountDto.setMail( entity.getMail() );
        ocUseraccountDto.setWorkunit( entity.getWorkunit() );
        ocUseraccountDto.setDepartment( entity.getDepartment() );
        ocUseraccountDto.setGroupname( entity.getGroupname() );
        ocUseraccountDto.setProperty( entity.getProperty() );
        ocUseraccountDto.setServerip( entity.getServerip() );
        ocUseraccountDto.setConnecttype( entity.getConnecttype() );
        ocUseraccountDto.setAccounttype( entity.getAccounttype() );
        ocUseraccountDto.setAccount( entity.getAccount() );
        ocUseraccountDto.setPurpose( entity.getPurpose() );
        ocUseraccountDto.setAccountstatus( entity.getAccountstatus() );
        ocUseraccountDto.setCreatetime( entity.getCreatetime() );
        ocUseraccountDto.setUpdatetime( entity.getUpdatetime() );
        ocUseraccountDto.setJobstatus( entity.getJobstatus() );
        ocUseraccountDto.setAccountarea( entity.getAccountarea() );
        ocUseraccountDto.setNote( entity.getNote() );
        ocUseraccountDto.setReservefield1( entity.getReservefield1() );
        ocUseraccountDto.setReservefield2( entity.getReservefield2() );
        ocUseraccountDto.setReservefield3( entity.getReservefield3() );
        ocUseraccountDto.setReservefield4( entity.getReservefield4() );

        return ocUseraccountDto;
    }

    @Override
    public List<OcUseraccount> toEntity(List<OcUseraccountDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<OcUseraccount> list = new ArrayList<OcUseraccount>( dtoList.size() );
        for ( OcUseraccountDto ocUseraccountDto : dtoList ) {
            list.add( toEntity( ocUseraccountDto ) );
        }

        return list;
    }

    @Override
    public List<OcUseraccountDto> toDto(List<OcUseraccount> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<OcUseraccountDto> list = new ArrayList<OcUseraccountDto>( entityList.size() );
        for ( OcUseraccount ocUseraccount : entityList ) {
            list.add( toDto( ocUseraccount ) );
        }

        return list;
    }
}
