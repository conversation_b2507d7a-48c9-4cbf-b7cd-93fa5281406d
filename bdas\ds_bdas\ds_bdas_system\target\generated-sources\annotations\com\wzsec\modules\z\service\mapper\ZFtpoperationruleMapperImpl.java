package com.wzsec.modules.z.service.mapper;

import com.wzsec.modules.z.domain.ZFtpoperationrule;
import com.wzsec.modules.z.service.dto.ZFtpoperationruleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:28+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ZFtpoperationruleMapperImpl implements ZFtpoperationruleMapper {

    @Override
    public ZFtpoperationruleDto toDto(ZFtpoperationrule entity) {
        if ( entity == null ) {
            return null;
        }

        ZFtpoperationruleDto zFtpoperationruleDto = new ZFtpoperationruleDto();

        zFtpoperationruleDto.setContent( entity.getContent() );
        zFtpoperationruleDto.setCreatetime( entity.getCreatetime() );
        zFtpoperationruleDto.setCreateuser( entity.getCreateuser() );
        zFtpoperationruleDto.setEventcode( entity.getEventcode() );
        zFtpoperationruleDto.setEventname( entity.getEventname() );
        zFtpoperationruleDto.setId( entity.getId() );
        zFtpoperationruleDto.setIsvalid( entity.getIsvalid() );
        zFtpoperationruleDto.setNote( entity.getNote() );
        zFtpoperationruleDto.setRisk( entity.getRisk() );
        zFtpoperationruleDto.setSparefield1( entity.getSparefield1() );
        zFtpoperationruleDto.setSparefield2( entity.getSparefield2() );
        zFtpoperationruleDto.setUpdatetime( entity.getUpdatetime() );
        zFtpoperationruleDto.setUpdateuser( entity.getUpdateuser() );

        return zFtpoperationruleDto;
    }

    @Override
    public List<ZFtpoperationruleDto> toDto(List<ZFtpoperationrule> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ZFtpoperationruleDto> list = new ArrayList<ZFtpoperationruleDto>( entityList.size() );
        for ( ZFtpoperationrule zFtpoperationrule : entityList ) {
            list.add( toDto( zFtpoperationrule ) );
        }

        return list;
    }

    @Override
    public ZFtpoperationrule toEntity(ZFtpoperationruleDto dto) {
        if ( dto == null ) {
            return null;
        }

        ZFtpoperationrule zFtpoperationrule = new ZFtpoperationrule();

        zFtpoperationrule.setContent( dto.getContent() );
        zFtpoperationrule.setCreatetime( dto.getCreatetime() );
        zFtpoperationrule.setCreateuser( dto.getCreateuser() );
        zFtpoperationrule.setEventcode( dto.getEventcode() );
        zFtpoperationrule.setEventname( dto.getEventname() );
        zFtpoperationrule.setId( dto.getId() );
        zFtpoperationrule.setIsvalid( dto.getIsvalid() );
        zFtpoperationrule.setNote( dto.getNote() );
        zFtpoperationrule.setRisk( dto.getRisk() );
        zFtpoperationrule.setSparefield1( dto.getSparefield1() );
        zFtpoperationrule.setSparefield2( dto.getSparefield2() );
        zFtpoperationrule.setUpdatetime( dto.getUpdatetime() );
        zFtpoperationrule.setUpdateuser( dto.getUpdateuser() );

        return zFtpoperationrule;
    }

    @Override
    public List<ZFtpoperationrule> toEntity(List<ZFtpoperationruleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ZFtpoperationrule> list = new ArrayList<ZFtpoperationrule>( dtoList.size() );
        for ( ZFtpoperationruleDto zFtpoperationruleDto : dtoList ) {
            list.add( toEntity( zFtpoperationruleDto ) );
        }

        return list;
    }
}
