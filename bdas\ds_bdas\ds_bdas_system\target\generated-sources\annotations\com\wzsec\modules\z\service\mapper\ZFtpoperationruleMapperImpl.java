package com.wzsec.modules.z.service.mapper;

import com.wzsec.modules.z.domain.ZFtpoperationrule;
import com.wzsec.modules.z.service.dto.ZFtpoperationruleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:30+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ZFtpoperationruleMapperImpl implements ZFtpoperationruleMapper {

    @Override
    public ZFtpoperationrule toEntity(ZFtpoperationruleDto dto) {
        if ( dto == null ) {
            return null;
        }

        ZFtpoperationrule zFtpoperationrule = new ZFtpoperationrule();

        zFtpoperationrule.setId( dto.getId() );
        zFtpoperationrule.setEventcode( dto.getEventcode() );
        zFtpoperationrule.setEventname( dto.getEventname() );
        zFtpoperationrule.setContent( dto.getContent() );
        zFtpoperationrule.setRisk( dto.getRisk() );
        zFtpoperationrule.setIsvalid( dto.getIsvalid() );
        zFtpoperationrule.setCreatetime( dto.getCreatetime() );
        zFtpoperationrule.setCreateuser( dto.getCreateuser() );
        zFtpoperationrule.setUpdatetime( dto.getUpdatetime() );
        zFtpoperationrule.setUpdateuser( dto.getUpdateuser() );
        zFtpoperationrule.setNote( dto.getNote() );
        zFtpoperationrule.setSparefield1( dto.getSparefield1() );
        zFtpoperationrule.setSparefield2( dto.getSparefield2() );

        return zFtpoperationrule;
    }

    @Override
    public ZFtpoperationruleDto toDto(ZFtpoperationrule entity) {
        if ( entity == null ) {
            return null;
        }

        ZFtpoperationruleDto zFtpoperationruleDto = new ZFtpoperationruleDto();

        zFtpoperationruleDto.setId( entity.getId() );
        zFtpoperationruleDto.setEventcode( entity.getEventcode() );
        zFtpoperationruleDto.setEventname( entity.getEventname() );
        zFtpoperationruleDto.setContent( entity.getContent() );
        zFtpoperationruleDto.setRisk( entity.getRisk() );
        zFtpoperationruleDto.setIsvalid( entity.getIsvalid() );
        zFtpoperationruleDto.setCreatetime( entity.getCreatetime() );
        zFtpoperationruleDto.setCreateuser( entity.getCreateuser() );
        zFtpoperationruleDto.setUpdatetime( entity.getUpdatetime() );
        zFtpoperationruleDto.setUpdateuser( entity.getUpdateuser() );
        zFtpoperationruleDto.setNote( entity.getNote() );
        zFtpoperationruleDto.setSparefield1( entity.getSparefield1() );
        zFtpoperationruleDto.setSparefield2( entity.getSparefield2() );

        return zFtpoperationruleDto;
    }

    @Override
    public List<ZFtpoperationrule> toEntity(List<ZFtpoperationruleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ZFtpoperationrule> list = new ArrayList<ZFtpoperationrule>( dtoList.size() );
        for ( ZFtpoperationruleDto zFtpoperationruleDto : dtoList ) {
            list.add( toEntity( zFtpoperationruleDto ) );
        }

        return list;
    }

    @Override
    public List<ZFtpoperationruleDto> toDto(List<ZFtpoperationrule> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ZFtpoperationruleDto> list = new ArrayList<ZFtpoperationruleDto>( entityList.size() );
        for ( ZFtpoperationrule zFtpoperationrule : entityList ) {
            list.add( toDto( zFtpoperationrule ) );
        }

        return list;
    }
}
