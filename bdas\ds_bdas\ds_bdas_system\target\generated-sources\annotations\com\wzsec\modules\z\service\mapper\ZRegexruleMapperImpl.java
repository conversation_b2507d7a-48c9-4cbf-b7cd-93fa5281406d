package com.wzsec.modules.z.service.mapper;

import com.wzsec.modules.z.domain.ZRegexrule;
import com.wzsec.modules.z.service.dto.ZRegexruleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:29+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ZRegexruleMapperImpl implements ZRegexruleMapper {

    @Override
    public ZRegexruleDto toDto(ZRegexrule entity) {
        if ( entity == null ) {
            return null;
        }

        ZRegexruleDto zRegexruleDto = new ZRegexruleDto();

        zRegexruleDto.setDes( entity.getDes() );
        zRegexruleDto.setEnname( entity.getEnname() );
        zRegexruleDto.setId( entity.getId() );
        zRegexruleDto.setLevel( entity.getLevel() );
        zRegexruleDto.setMatchnumber( entity.getMatchnumber() );
        zRegexruleDto.setName( entity.getName() );
        zRegexruleDto.setNote( entity.getNote() );
        zRegexruleDto.setRegexps( entity.getRegexps() );

        return zRegexruleDto;
    }

    @Override
    public List<ZRegexruleDto> toDto(List<ZRegexrule> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ZRegexruleDto> list = new ArrayList<ZRegexruleDto>( entityList.size() );
        for ( ZRegexrule zRegexrule : entityList ) {
            list.add( toDto( zRegexrule ) );
        }

        return list;
    }

    @Override
    public ZRegexrule toEntity(ZRegexruleDto dto) {
        if ( dto == null ) {
            return null;
        }

        ZRegexrule zRegexrule = new ZRegexrule();

        zRegexrule.setDes( dto.getDes() );
        zRegexrule.setEnname( dto.getEnname() );
        zRegexrule.setId( dto.getId() );
        zRegexrule.setLevel( dto.getLevel() );
        zRegexrule.setMatchnumber( dto.getMatchnumber() );
        zRegexrule.setName( dto.getName() );
        zRegexrule.setNote( dto.getNote() );
        zRegexrule.setRegexps( dto.getRegexps() );

        return zRegexrule;
    }

    @Override
    public List<ZRegexrule> toEntity(List<ZRegexruleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ZRegexrule> list = new ArrayList<ZRegexrule>( dtoList.size() );
        for ( ZRegexruleDto zRegexruleDto : dtoList ) {
            list.add( toEntity( zRegexruleDto ) );
        }

        return list;
    }
}
