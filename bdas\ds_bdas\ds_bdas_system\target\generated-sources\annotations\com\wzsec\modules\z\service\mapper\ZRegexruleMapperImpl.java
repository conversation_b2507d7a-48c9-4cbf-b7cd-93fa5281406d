package com.wzsec.modules.z.service.mapper;

import com.wzsec.modules.z.domain.ZRegexrule;
import com.wzsec.modules.z.service.dto.ZRegexruleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:30+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ZRegexruleMapperImpl implements ZRegexruleMapper {

    @Override
    public ZRegexrule toEntity(ZRegexruleDto dto) {
        if ( dto == null ) {
            return null;
        }

        ZRegexrule zRegexrule = new ZRegexrule();

        zRegexrule.setId( dto.getId() );
        zRegexrule.setName( dto.getName() );
        zRegexrule.setEnname( dto.getEnname() );
        zRegexrule.setDes( dto.getDes() );
        zRegexrule.setLevel( dto.getLevel() );
        zRegexrule.setRegexps( dto.getRegexps() );
        zRegexrule.setMatchnumber( dto.getMatchnumber() );
        zRegexrule.setNote( dto.getNote() );

        return zRegexrule;
    }

    @Override
    public ZRegexruleDto toDto(ZRegexrule entity) {
        if ( entity == null ) {
            return null;
        }

        ZRegexruleDto zRegexruleDto = new ZRegexruleDto();

        zRegexruleDto.setId( entity.getId() );
        zRegexruleDto.setName( entity.getName() );
        zRegexruleDto.setEnname( entity.getEnname() );
        zRegexruleDto.setDes( entity.getDes() );
        zRegexruleDto.setLevel( entity.getLevel() );
        zRegexruleDto.setRegexps( entity.getRegexps() );
        zRegexruleDto.setMatchnumber( entity.getMatchnumber() );
        zRegexruleDto.setNote( entity.getNote() );

        return zRegexruleDto;
    }

    @Override
    public List<ZRegexrule> toEntity(List<ZRegexruleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ZRegexrule> list = new ArrayList<ZRegexrule>( dtoList.size() );
        for ( ZRegexruleDto zRegexruleDto : dtoList ) {
            list.add( toEntity( zRegexruleDto ) );
        }

        return list;
    }

    @Override
    public List<ZRegexruleDto> toDto(List<ZRegexrule> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ZRegexruleDto> list = new ArrayList<ZRegexruleDto>( entityList.size() );
        for ( ZRegexrule zRegexrule : entityList ) {
            list.add( toDto( zRegexrule ) );
        }

        return list;
    }
}
