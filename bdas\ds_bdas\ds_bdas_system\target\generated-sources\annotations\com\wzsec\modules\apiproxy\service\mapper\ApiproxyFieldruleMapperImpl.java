package com.wzsec.modules.apiproxy.service.mapper;

import com.wzsec.modules.apiproxy.domain.ApiproxyFieldrule;
import com.wzsec.modules.apiproxy.service.dto.ApiproxyFieldruleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:30+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ApiproxyFieldruleMapperImpl implements ApiproxyFieldruleMapper {

    @Override
    public ApiproxyFieldruleDto toDto(ApiproxyFieldrule entity) {
        if ( entity == null ) {
            return null;
        }

        ApiproxyFieldruleDto apiproxyFieldruleDto = new ApiproxyFieldruleDto();

        apiproxyFieldruleDto.setApiruleid( entity.getApiruleid() );
        apiproxyFieldruleDto.setCreatetime( entity.getCreatetime() );
        apiproxyFieldruleDto.setCreateuser( entity.getCreateuser() );
        apiproxyFieldruleDto.setId( entity.getId() );
        apiproxyFieldruleDto.setNote( entity.getNote() );
        apiproxyFieldruleDto.setOutparammean( entity.getOutparammean() );
        apiproxyFieldruleDto.setOutputparams( entity.getOutputparams() );
        apiproxyFieldruleDto.setPredicates( entity.getPredicates() );
        apiproxyFieldruleDto.setRegister( entity.getRegister() );
        apiproxyFieldruleDto.setRegulatory( entity.getRegulatory() );
        apiproxyFieldruleDto.setServerhost( entity.getServerhost() );
        apiproxyFieldruleDto.setServerport( entity.getServerport() );
        apiproxyFieldruleDto.setSparefield1( entity.getSparefield1() );
        apiproxyFieldruleDto.setSparefield2( entity.getSparefield2() );
        apiproxyFieldruleDto.setSparefield3( entity.getSparefield3() );
        apiproxyFieldruleDto.setSparefield4( entity.getSparefield4() );
        apiproxyFieldruleDto.setSparefield5( entity.getSparefield5() );
        apiproxyFieldruleDto.setStatus( entity.getStatus() );
        apiproxyFieldruleDto.setUpdatetime( entity.getUpdatetime() );
        apiproxyFieldruleDto.setUpdateuser( entity.getUpdateuser() );

        return apiproxyFieldruleDto;
    }

    @Override
    public List<ApiproxyFieldruleDto> toDto(List<ApiproxyFieldrule> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiproxyFieldruleDto> list = new ArrayList<ApiproxyFieldruleDto>( entityList.size() );
        for ( ApiproxyFieldrule apiproxyFieldrule : entityList ) {
            list.add( toDto( apiproxyFieldrule ) );
        }

        return list;
    }

    @Override
    public ApiproxyFieldrule toEntity(ApiproxyFieldruleDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiproxyFieldrule apiproxyFieldrule = new ApiproxyFieldrule();

        apiproxyFieldrule.setApiruleid( dto.getApiruleid() );
        apiproxyFieldrule.setCreatetime( dto.getCreatetime() );
        apiproxyFieldrule.setCreateuser( dto.getCreateuser() );
        apiproxyFieldrule.setId( dto.getId() );
        apiproxyFieldrule.setNote( dto.getNote() );
        apiproxyFieldrule.setOutparammean( dto.getOutparammean() );
        apiproxyFieldrule.setOutputparams( dto.getOutputparams() );
        apiproxyFieldrule.setPredicates( dto.getPredicates() );
        apiproxyFieldrule.setRegister( dto.getRegister() );
        apiproxyFieldrule.setRegulatory( dto.getRegulatory() );
        apiproxyFieldrule.setServerhost( dto.getServerhost() );
        apiproxyFieldrule.setServerport( dto.getServerport() );
        apiproxyFieldrule.setSparefield1( dto.getSparefield1() );
        apiproxyFieldrule.setSparefield2( dto.getSparefield2() );
        apiproxyFieldrule.setSparefield3( dto.getSparefield3() );
        apiproxyFieldrule.setSparefield4( dto.getSparefield4() );
        apiproxyFieldrule.setSparefield5( dto.getSparefield5() );
        apiproxyFieldrule.setStatus( dto.getStatus() );
        apiproxyFieldrule.setUpdatetime( dto.getUpdatetime() );
        apiproxyFieldrule.setUpdateuser( dto.getUpdateuser() );

        return apiproxyFieldrule;
    }

    @Override
    public List<ApiproxyFieldrule> toEntity(List<ApiproxyFieldruleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiproxyFieldrule> list = new ArrayList<ApiproxyFieldrule>( dtoList.size() );
        for ( ApiproxyFieldruleDto apiproxyFieldruleDto : dtoList ) {
            list.add( toEntity( apiproxyFieldruleDto ) );
        }

        return list;
    }
}
