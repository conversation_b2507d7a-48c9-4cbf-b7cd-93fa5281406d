package com.wzsec.modules.apiproxy.service.mapper;

import com.wzsec.modules.apiproxy.domain.ApiproxyFieldrule;
import com.wzsec.modules.apiproxy.service.dto.ApiproxyFieldruleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:30+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ApiproxyFieldruleMapperImpl implements ApiproxyFieldruleMapper {

    @Override
    public ApiproxyFieldrule toEntity(ApiproxyFieldruleDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiproxyFieldrule apiproxyFieldrule = new ApiproxyFieldrule();

        apiproxyFieldrule.setId( dto.getId() );
        apiproxyFieldrule.setRegister( dto.getRegister() );
        apiproxyFieldrule.setRegulatory( dto.getRegulatory() );
        apiproxyFieldrule.setServerhost( dto.getServerhost() );
        apiproxyFieldrule.setServerport( dto.getServerport() );
        apiproxyFieldrule.setPredicates( dto.getPredicates() );
        apiproxyFieldrule.setOutputparams( dto.getOutputparams() );
        apiproxyFieldrule.setOutparammean( dto.getOutparammean() );
        apiproxyFieldrule.setApiruleid( dto.getApiruleid() );
        apiproxyFieldrule.setStatus( dto.getStatus() );
        apiproxyFieldrule.setCreateuser( dto.getCreateuser() );
        apiproxyFieldrule.setCreatetime( dto.getCreatetime() );
        apiproxyFieldrule.setUpdateuser( dto.getUpdateuser() );
        apiproxyFieldrule.setUpdatetime( dto.getUpdatetime() );
        apiproxyFieldrule.setNote( dto.getNote() );
        apiproxyFieldrule.setSparefield1( dto.getSparefield1() );
        apiproxyFieldrule.setSparefield2( dto.getSparefield2() );
        apiproxyFieldrule.setSparefield3( dto.getSparefield3() );
        apiproxyFieldrule.setSparefield4( dto.getSparefield4() );
        apiproxyFieldrule.setSparefield5( dto.getSparefield5() );

        return apiproxyFieldrule;
    }

    @Override
    public ApiproxyFieldruleDto toDto(ApiproxyFieldrule entity) {
        if ( entity == null ) {
            return null;
        }

        ApiproxyFieldruleDto apiproxyFieldruleDto = new ApiproxyFieldruleDto();

        apiproxyFieldruleDto.setId( entity.getId() );
        apiproxyFieldruleDto.setRegister( entity.getRegister() );
        apiproxyFieldruleDto.setRegulatory( entity.getRegulatory() );
        apiproxyFieldruleDto.setServerhost( entity.getServerhost() );
        apiproxyFieldruleDto.setServerport( entity.getServerport() );
        apiproxyFieldruleDto.setPredicates( entity.getPredicates() );
        apiproxyFieldruleDto.setOutputparams( entity.getOutputparams() );
        apiproxyFieldruleDto.setOutparammean( entity.getOutparammean() );
        apiproxyFieldruleDto.setApiruleid( entity.getApiruleid() );
        apiproxyFieldruleDto.setStatus( entity.getStatus() );
        apiproxyFieldruleDto.setCreateuser( entity.getCreateuser() );
        apiproxyFieldruleDto.setCreatetime( entity.getCreatetime() );
        apiproxyFieldruleDto.setUpdateuser( entity.getUpdateuser() );
        apiproxyFieldruleDto.setUpdatetime( entity.getUpdatetime() );
        apiproxyFieldruleDto.setNote( entity.getNote() );
        apiproxyFieldruleDto.setSparefield1( entity.getSparefield1() );
        apiproxyFieldruleDto.setSparefield2( entity.getSparefield2() );
        apiproxyFieldruleDto.setSparefield3( entity.getSparefield3() );
        apiproxyFieldruleDto.setSparefield4( entity.getSparefield4() );
        apiproxyFieldruleDto.setSparefield5( entity.getSparefield5() );

        return apiproxyFieldruleDto;
    }

    @Override
    public List<ApiproxyFieldrule> toEntity(List<ApiproxyFieldruleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiproxyFieldrule> list = new ArrayList<ApiproxyFieldrule>( dtoList.size() );
        for ( ApiproxyFieldruleDto apiproxyFieldruleDto : dtoList ) {
            list.add( toEntity( apiproxyFieldruleDto ) );
        }

        return list;
    }

    @Override
    public List<ApiproxyFieldruleDto> toDto(List<ApiproxyFieldrule> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiproxyFieldruleDto> list = new ArrayList<ApiproxyFieldruleDto>( entityList.size() );
        for ( ApiproxyFieldrule apiproxyFieldrule : entityList ) {
            list.add( toDto( apiproxyFieldrule ) );
        }

        return list;
    }
}
