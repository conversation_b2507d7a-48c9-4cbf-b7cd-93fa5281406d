package com.wzsec.modules.sdd.source.service.mapper;

import com.wzsec.modules.sdd.source.domain.Datasource;
import com.wzsec.modules.sdd.source.service.dto.DatasourceSmallDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:27:07+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class DatasourceSmallMapperImpl implements DatasourceSmallMapper {

    @Override
    public DatasourceSmallDto toDto(Datasource entity) {
        if ( entity == null ) {
            return null;
        }

        DatasourceSmallDto datasourceSmallDto = new DatasourceSmallDto();

        datasourceSmallDto.setDbname( entity.getDbname() );
        datasourceSmallDto.setDriverprogram( entity.getDriverprogram() );
        datasourceSmallDto.setId( entity.getId() );
        datasourceSmallDto.setSrcname( entity.getSrcname() );
        datasourceSmallDto.setSrcurl( entity.getSrcurl() );
        datasourceSmallDto.setType( entity.getType() );

        return datasourceSmallDto;
    }

    @Override
    public List<DatasourceSmallDto> toDto(List<Datasource> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<DatasourceSmallDto> list = new ArrayList<DatasourceSmallDto>( entityList.size() );
        for ( Datasource datasource : entityList ) {
            list.add( toDto( datasource ) );
        }

        return list;
    }

    @Override
    public Datasource toEntity(DatasourceSmallDto dto) {
        if ( dto == null ) {
            return null;
        }

        Datasource datasource = new Datasource();

        datasource.setDbname( dto.getDbname() );
        datasource.setDriverprogram( dto.getDriverprogram() );
        datasource.setId( dto.getId() );
        datasource.setSrcname( dto.getSrcname() );
        datasource.setSrcurl( dto.getSrcurl() );
        datasource.setType( dto.getType() );

        return datasource;
    }

    @Override
    public List<Datasource> toEntity(List<DatasourceSmallDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Datasource> list = new ArrayList<Datasource>( dtoList.size() );
        for ( DatasourceSmallDto datasourceSmallDto : dtoList ) {
            list.add( toEntity( datasourceSmallDto ) );
        }

        return list;
    }
}
