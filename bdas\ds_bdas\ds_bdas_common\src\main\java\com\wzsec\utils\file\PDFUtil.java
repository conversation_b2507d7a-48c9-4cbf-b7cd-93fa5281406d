package com.wzsec.utils.file;

import cn.hutool.core.lang.Console;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.encryption.InvalidPasswordException;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.pdfbox.text.PDFTextStripperByArea;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * PDF文件
 *
 * <AUTHOR>
 * @date 2020-4-26
 */
@Slf4j
public class PDFUtil {

    /**
     * 读取pdf文本内容
     *
     * @param filePath 文件地址
     * @return String
     * @description 处理pdf
     * <AUTHOR>
     * @date 2020-4-26
     */
    public static String getPDFStr(String filePath) {
        String result = null;
        try (PDDocument document = PDDocument.load(new File(filePath))) {
            document.getClass();
            if (!document.isEncrypted()) {
                PDFTextStripperByArea stripper = new PDFTextStripperByArea();
                stripper.setSortByPosition(true);
                PDFTextStripper tStripper = new PDFTextStripper();
                result = tStripper.getText(document);
            }
        } catch (InvalidPasswordException e) {
            log.error("读取" + filePath + "文件出现异常！", e);
//            e.printStackTrace();
        } catch (IOException e) {
            log.error("读取" + filePath + "文件出现异常！", e);
//            e.printStackTrace();
        }
        return result;
    }


    public static void main(String[] args) {
        String wordStr = getPDFStr("F:\\检查记录.pdf");
        Console.log("PDF文档内容为: ");
        Console.log(wordStr);

        String[] split = wordStr.split("。|，|\\s+|\r|\\r\\n|,|；|:|：|;|\n|\t|=|'|\"|’|‘|“|”|（|）|\\{|}|[|]|\\(|\\)|！|#|￥|……|&|\\*|!|\\$|\\^");

        List<String> list = new ArrayList<>();
        for (String s : split) {
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(s)) {
                list.add(s.trim());
            }
        }
        Console.log("list集合为: {}", list);
        for (String s : list) {
            Console.log("经处理后的字段为: {} ", s);
        }
    }

}
