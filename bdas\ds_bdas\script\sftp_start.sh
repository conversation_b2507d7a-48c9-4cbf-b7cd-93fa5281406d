#!/bin/bash

# ################################## 变量定义 ################################################
dateTimeNow=$(date +%Y%m%d)  # 当前日期 年月日
dateTimePrevDay=$(date -d '1 day ago' +%Y%m%d)  # 前一天的日期 年月日
dateTimePrevHour=$(date -d '1 hour ago' +%Y_%m_%d_%H)  # 前一个小时的路径
dateTimePrevHourDir=$(date -d '1 hour ago' +%Y%m%d)/$dateTimePrevHour  # 前一个小时的流量文件夹路径

bakSrcDir=/flowdata   # 需要备份的文件路径
bakDescDir=/flowdata/backup  # 备份文件到该路径
FTP_SERVER=150.223.33.230
FTP_USER=root
FTP_PASS=Sjb\$2023\#789@
FTP_DIR=/data/netflow/

# ################################## 压缩前一个小时流量文件 ################################################

# 确定前一个小时的流量文件夹
hourDir=$bakSrcDir/$dateTimePrevHourDir

if [ -d "$hourDir" ]; then
    # 进入备份目录
    cd $bakDescDir || exit 1

    # 创建压缩文件名
    tarFile=$(printf "%s_%s_%s.tar.gz" $dateTimeNow $(date +%s%N | md5sum | head -c 10) $(date -d '1 hour ago' +%H))

    # 压缩前一个小时的流量文件
    tar -zcvPf $tarFile $hourDir

    echo "压缩完成: $tarFile"
else
    echo "路径 $hourDir 不存在,没有要压缩的文件."
    exit 1
fi

# ################################## 上传文件至SFTP ################################################

# 使用 lftp 进行上传
echo "$(date "+%Y-%m-%d %H:%M:%S")  开始上传文件"

lftp -u $FTP_USER,$FTP_PASS sftp://$FTP_SERVER:22 <<EOF
cd $FTP_DIR
lcd $bakDescDir
put $tarFile
bye
EOF

echo "$(date "+%Y-%m-%d %H:%M:%S")  上传到SFTP成功"

# 上传后删除本地压缩文件
rm -f $tarFile

# ################################## 清理 ################################################

# 删除本地备份文件夹中前一天的文件夹
if [ -d "$bakSrcDir/$dateTimePrevDay" ]; then
    rm -rf "$bakSrcDir/$dateTimePrevDay"
    echo "已删除本地备份文件夹: $bakSrcDir/$dateTimePrevDay"
fi

# 删除本地备份文件夹中前一天的压缩文件
if [ -n "$(find $bakDescDir -maxdepth 1 -type f -name "${dateTimePrevDay}*.tar.gz")" ]; then
    rm -f $bakDescDir/${dateTimePrevDay}*.tar.gz
    echo "已删除本地备份文件: $bakDescDir/${dateTimePrevDay}*.tar.gz"
fi
