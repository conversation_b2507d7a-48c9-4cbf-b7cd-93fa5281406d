package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcFilesensitiveresultdetail;
import com.wzsec.modules.ic.service.dto.IcFilesensitiveresultdetailDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:30+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class IcFilesensitiveresultdetailMapperImpl implements IcFilesensitiveresultdetailMapper {

    @Override
    public IcFilesensitiveresultdetail toEntity(IcFilesensitiveresultdetailDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcFilesensitiveresultdetail icFilesensitiveresultdetail = new IcFilesensitiveresultdetail();

        icFilesensitiveresultdetail.setId( dto.getId() );
        icFilesensitiveresultdetail.setApicode( dto.getApicode() );
        icFilesensitiveresultdetail.setApiname( dto.getApiname() );
        icFilesensitiveresultdetail.setUrl( dto.getUrl() );
        icFilesensitiveresultdetail.setClientip( dto.getClientip() );
        icFilesensitiveresultdetail.setServerip( dto.getServerip() );
        icFilesensitiveresultdetail.setServerport( dto.getServerport() );
        icFilesensitiveresultdetail.setFilename( dto.getFilename() );
        icFilesensitiveresultdetail.setFilesize( dto.getFilesize() );
        icFilesensitiveresultdetail.setBehaviortype( dto.getBehaviortype() );
        icFilesensitiveresultdetail.setTotalcount( dto.getTotalcount() );
        icFilesensitiveresultdetail.setResulttype( dto.getResulttype() );
        icFilesensitiveresultdetail.setSensitivenum( dto.getSensitivenum() );
        icFilesensitiveresultdetail.setRatio( dto.getRatio() );
        icFilesensitiveresultdetail.setExample( dto.getExample() );
        icFilesensitiveresultdetail.setChecktime( dto.getChecktime() );
        icFilesensitiveresultdetail.setRisk( dto.getRisk() );
        icFilesensitiveresultdetail.setTaskname( dto.getTaskname() );
        icFilesensitiveresultdetail.setSparefield1( dto.getSparefield1() );
        icFilesensitiveresultdetail.setSparefield2( dto.getSparefield2() );
        icFilesensitiveresultdetail.setSparefield3( dto.getSparefield3() );
        icFilesensitiveresultdetail.setSparefield4( dto.getSparefield4() );
        icFilesensitiveresultdetail.setSparefield5( dto.getSparefield5() );

        return icFilesensitiveresultdetail;
    }

    @Override
    public IcFilesensitiveresultdetailDto toDto(IcFilesensitiveresultdetail entity) {
        if ( entity == null ) {
            return null;
        }

        IcFilesensitiveresultdetailDto icFilesensitiveresultdetailDto = new IcFilesensitiveresultdetailDto();

        icFilesensitiveresultdetailDto.setId( entity.getId() );
        icFilesensitiveresultdetailDto.setApicode( entity.getApicode() );
        icFilesensitiveresultdetailDto.setApiname( entity.getApiname() );
        icFilesensitiveresultdetailDto.setUrl( entity.getUrl() );
        icFilesensitiveresultdetailDto.setClientip( entity.getClientip() );
        icFilesensitiveresultdetailDto.setServerip( entity.getServerip() );
        icFilesensitiveresultdetailDto.setServerport( entity.getServerport() );
        icFilesensitiveresultdetailDto.setFilename( entity.getFilename() );
        icFilesensitiveresultdetailDto.setFilesize( entity.getFilesize() );
        icFilesensitiveresultdetailDto.setBehaviortype( entity.getBehaviortype() );
        icFilesensitiveresultdetailDto.setTotalcount( entity.getTotalcount() );
        icFilesensitiveresultdetailDto.setResulttype( entity.getResulttype() );
        icFilesensitiveresultdetailDto.setSensitivenum( entity.getSensitivenum() );
        icFilesensitiveresultdetailDto.setRatio( entity.getRatio() );
        icFilesensitiveresultdetailDto.setExample( entity.getExample() );
        icFilesensitiveresultdetailDto.setChecktime( entity.getChecktime() );
        icFilesensitiveresultdetailDto.setRisk( entity.getRisk() );
        icFilesensitiveresultdetailDto.setTaskname( entity.getTaskname() );
        icFilesensitiveresultdetailDto.setSparefield1( entity.getSparefield1() );
        icFilesensitiveresultdetailDto.setSparefield2( entity.getSparefield2() );
        icFilesensitiveresultdetailDto.setSparefield3( entity.getSparefield3() );
        icFilesensitiveresultdetailDto.setSparefield4( entity.getSparefield4() );
        icFilesensitiveresultdetailDto.setSparefield5( entity.getSparefield5() );

        return icFilesensitiveresultdetailDto;
    }

    @Override
    public List<IcFilesensitiveresultdetail> toEntity(List<IcFilesensitiveresultdetailDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcFilesensitiveresultdetail> list = new ArrayList<IcFilesensitiveresultdetail>( dtoList.size() );
        for ( IcFilesensitiveresultdetailDto icFilesensitiveresultdetailDto : dtoList ) {
            list.add( toEntity( icFilesensitiveresultdetailDto ) );
        }

        return list;
    }

    @Override
    public List<IcFilesensitiveresultdetailDto> toDto(List<IcFilesensitiveresultdetail> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcFilesensitiveresultdetailDto> list = new ArrayList<IcFilesensitiveresultdetailDto>( entityList.size() );
        for ( IcFilesensitiveresultdetail icFilesensitiveresultdetail : entityList ) {
            list.add( toDto( icFilesensitiveresultdetail ) );
        }

        return list;
    }
}
