package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcFilesensitiveresultdetail;
import com.wzsec.modules.ic.service.dto.IcFilesensitiveresultdetailDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:26:45+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class IcFilesensitiveresultdetailMapperImpl implements IcFilesensitiveresultdetailMapper {

    @Override
    public IcFilesensitiveresultdetailDto toDto(IcFilesensitiveresultdetail entity) {
        if ( entity == null ) {
            return null;
        }

        IcFilesensitiveresultdetailDto icFilesensitiveresultdetailDto = new IcFilesensitiveresultdetailDto();

        icFilesensitiveresultdetailDto.setApicode( entity.getApicode() );
        icFilesensitiveresultdetailDto.setApiname( entity.getApiname() );
        icFilesensitiveresultdetailDto.setBehaviortype( entity.getBehaviortype() );
        icFilesensitiveresultdetailDto.setChecktime( entity.getChecktime() );
        icFilesensitiveresultdetailDto.setClientip( entity.getClientip() );
        icFilesensitiveresultdetailDto.setExample( entity.getExample() );
        icFilesensitiveresultdetailDto.setFilename( entity.getFilename() );
        icFilesensitiveresultdetailDto.setFilesize( entity.getFilesize() );
        icFilesensitiveresultdetailDto.setId( entity.getId() );
        icFilesensitiveresultdetailDto.setRatio( entity.getRatio() );
        icFilesensitiveresultdetailDto.setResulttype( entity.getResulttype() );
        icFilesensitiveresultdetailDto.setRisk( entity.getRisk() );
        icFilesensitiveresultdetailDto.setSensitivenum( entity.getSensitivenum() );
        icFilesensitiveresultdetailDto.setServerip( entity.getServerip() );
        icFilesensitiveresultdetailDto.setServerport( entity.getServerport() );
        icFilesensitiveresultdetailDto.setSparefield1( entity.getSparefield1() );
        icFilesensitiveresultdetailDto.setSparefield2( entity.getSparefield2() );
        icFilesensitiveresultdetailDto.setSparefield3( entity.getSparefield3() );
        icFilesensitiveresultdetailDto.setSparefield4( entity.getSparefield4() );
        icFilesensitiveresultdetailDto.setSparefield5( entity.getSparefield5() );
        icFilesensitiveresultdetailDto.setTaskname( entity.getTaskname() );
        icFilesensitiveresultdetailDto.setTotalcount( entity.getTotalcount() );
        icFilesensitiveresultdetailDto.setUrl( entity.getUrl() );

        return icFilesensitiveresultdetailDto;
    }

    @Override
    public List<IcFilesensitiveresultdetailDto> toDto(List<IcFilesensitiveresultdetail> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcFilesensitiveresultdetailDto> list = new ArrayList<IcFilesensitiveresultdetailDto>( entityList.size() );
        for ( IcFilesensitiveresultdetail icFilesensitiveresultdetail : entityList ) {
            list.add( toDto( icFilesensitiveresultdetail ) );
        }

        return list;
    }

    @Override
    public IcFilesensitiveresultdetail toEntity(IcFilesensitiveresultdetailDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcFilesensitiveresultdetail icFilesensitiveresultdetail = new IcFilesensitiveresultdetail();

        icFilesensitiveresultdetail.setApicode( dto.getApicode() );
        icFilesensitiveresultdetail.setApiname( dto.getApiname() );
        icFilesensitiveresultdetail.setBehaviortype( dto.getBehaviortype() );
        icFilesensitiveresultdetail.setChecktime( dto.getChecktime() );
        icFilesensitiveresultdetail.setClientip( dto.getClientip() );
        icFilesensitiveresultdetail.setExample( dto.getExample() );
        icFilesensitiveresultdetail.setFilename( dto.getFilename() );
        icFilesensitiveresultdetail.setFilesize( dto.getFilesize() );
        icFilesensitiveresultdetail.setId( dto.getId() );
        icFilesensitiveresultdetail.setRatio( dto.getRatio() );
        icFilesensitiveresultdetail.setResulttype( dto.getResulttype() );
        icFilesensitiveresultdetail.setRisk( dto.getRisk() );
        icFilesensitiveresultdetail.setSensitivenum( dto.getSensitivenum() );
        icFilesensitiveresultdetail.setServerip( dto.getServerip() );
        icFilesensitiveresultdetail.setServerport( dto.getServerport() );
        icFilesensitiveresultdetail.setSparefield1( dto.getSparefield1() );
        icFilesensitiveresultdetail.setSparefield2( dto.getSparefield2() );
        icFilesensitiveresultdetail.setSparefield3( dto.getSparefield3() );
        icFilesensitiveresultdetail.setSparefield4( dto.getSparefield4() );
        icFilesensitiveresultdetail.setSparefield5( dto.getSparefield5() );
        icFilesensitiveresultdetail.setTaskname( dto.getTaskname() );
        icFilesensitiveresultdetail.setTotalcount( dto.getTotalcount() );
        icFilesensitiveresultdetail.setUrl( dto.getUrl() );

        return icFilesensitiveresultdetail;
    }

    @Override
    public List<IcFilesensitiveresultdetail> toEntity(List<IcFilesensitiveresultdetailDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcFilesensitiveresultdetail> list = new ArrayList<IcFilesensitiveresultdetail>( dtoList.size() );
        for ( IcFilesensitiveresultdetailDto icFilesensitiveresultdetailDto : dtoList ) {
            list.add( toEntity( icFilesensitiveresultdetailDto ) );
        }

        return list;
    }
}
