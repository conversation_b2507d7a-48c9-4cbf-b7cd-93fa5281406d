package com.wzsec.modules.apiproxy.service.mapper;

import com.wzsec.modules.apiproxy.domain.ApiRule;
import com.wzsec.modules.apiproxy.service.dto.ApiRuleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:32+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ApiRuleMapperImpl implements ApiRuleMapper {

    @Override
    public ApiRuleDto toDto(ApiRule entity) {
        if ( entity == null ) {
            return null;
        }

        ApiRuleDto apiRuleDto = new ApiRuleDto();

        apiRuleDto.setAlgorithm( entity.getAlgorithm() );
        apiRuleDto.setCreatetime( entity.getCreatetime() );
        apiRuleDto.setCreateuser( entity.getCreateuser() );
        apiRuleDto.setId( entity.getId() );
        apiRuleDto.setNote( entity.getNote() );
        apiRuleDto.setParam( entity.getParam() );
        apiRuleDto.setRulename( entity.getRulename() );
        apiRuleDto.setSname( entity.getSname() );
        apiRuleDto.setSparefield1( entity.getSparefield1() );
        apiRuleDto.setSparefield2( entity.getSparefield2() );
        apiRuleDto.setSparefield3( entity.getSparefield3() );
        apiRuleDto.setSparefield4( entity.getSparefield4() );
        apiRuleDto.setSparefield5( entity.getSparefield5() );
        apiRuleDto.setStatus( entity.getStatus() );
        apiRuleDto.setUpdatetime( entity.getUpdatetime() );
        apiRuleDto.setUpdateuser( entity.getUpdateuser() );

        return apiRuleDto;
    }

    @Override
    public List<ApiRuleDto> toDto(List<ApiRule> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiRuleDto> list = new ArrayList<ApiRuleDto>( entityList.size() );
        for ( ApiRule apiRule : entityList ) {
            list.add( toDto( apiRule ) );
        }

        return list;
    }

    @Override
    public ApiRule toEntity(ApiRuleDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiRule apiRule = new ApiRule();

        apiRule.setAlgorithm( dto.getAlgorithm() );
        apiRule.setCreatetime( dto.getCreatetime() );
        apiRule.setCreateuser( dto.getCreateuser() );
        apiRule.setId( dto.getId() );
        apiRule.setNote( dto.getNote() );
        apiRule.setParam( dto.getParam() );
        apiRule.setRulename( dto.getRulename() );
        apiRule.setSname( dto.getSname() );
        apiRule.setSparefield1( dto.getSparefield1() );
        apiRule.setSparefield2( dto.getSparefield2() );
        apiRule.setSparefield3( dto.getSparefield3() );
        apiRule.setSparefield4( dto.getSparefield4() );
        apiRule.setSparefield5( dto.getSparefield5() );
        apiRule.setStatus( dto.getStatus() );
        apiRule.setUpdatetime( dto.getUpdatetime() );
        apiRule.setUpdateuser( dto.getUpdateuser() );

        return apiRule;
    }

    @Override
    public List<ApiRule> toEntity(List<ApiRuleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiRule> list = new ArrayList<ApiRule>( dtoList.size() );
        for ( ApiRuleDto apiRuleDto : dtoList ) {
            list.add( toEntity( apiRuleDto ) );
        }

        return list;
    }
}
