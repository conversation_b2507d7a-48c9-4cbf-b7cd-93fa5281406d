package com.wzsec.modules.ic.rest;

import cn.hutool.core.lang.Console;
import com.wzsec.aop.log.Log;
import com.wzsec.modules.ic.domain.ApiErrorcheckresult;
import com.wzsec.modules.ic.service.ApiErrorcheckresultService;
import com.wzsec.modules.ic.service.dto.ApiErrorcheckresultQueryCriteria;
import com.wzsec.utils.DomainUtil;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-05-20
 */
// @Api(tags = "API调用错误管理")
@RestController
@RequestMapping("/api/apiErrorcheckresult")
public class ApiErrorcheckresultController {

    private final ApiErrorcheckresultService apiErrorcheckresultService;

    public ApiErrorcheckresultController(ApiErrorcheckresultService apiErrorcheckresultService) {
        this.apiErrorcheckresultService = apiErrorcheckresultService;
    }

    @Log("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, ApiErrorcheckresultQueryCriteria criteria) throws IOException {
        try {
            Timestamp dateStart = criteria.getDateStart();
            Timestamp dateEnd = criteria.getDateEnd();
            List<String> checkTime = new ArrayList<>();
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateStart));
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateEnd));
            criteria.setChecktime(checkTime);
        } catch (Exception e) {
            Console.log("未填写查询时间");
        }

        // 根据申请部门名称分域
        List<String> domainAkList = DomainUtil.queryDomainAk();
        criteria.setAk(domainAkList);

        apiErrorcheckresultService.download(apiErrorcheckresultService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询API调用错误")
    public ResponseEntity<Object> getApiErrorcheckresults(ApiErrorcheckresultQueryCriteria criteria, Pageable pageable) {

        try {
            Timestamp dateStart = criteria.getDateStart();
            Timestamp dateEnd = criteria.getDateEnd();
            List<String> checkTime = new ArrayList<>();
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateStart));
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateEnd));
            criteria.setChecktime(checkTime);
        } catch (Exception e) {
            Console.log("未填写查询时间");
        }

        // 根据申请部门名称分域
        List<String> domainAkList = DomainUtil.queryDomainAk();
        criteria.setAk(domainAkList);

        return new ResponseEntity<>(apiErrorcheckresultService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增API调用错误")
    public ResponseEntity<Object> create(@Validated @RequestBody ApiErrorcheckresult resources) {
        return new ResponseEntity<>(apiErrorcheckresultService.create(resources), HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改API调用错误")
    public ResponseEntity<Object> update(@Validated @RequestBody ApiErrorcheckresult resources) {
        apiErrorcheckresultService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除API调用错误")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        apiErrorcheckresultService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
