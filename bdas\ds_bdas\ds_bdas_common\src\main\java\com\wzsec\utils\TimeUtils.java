/*********************************************************************
 *
 * CHINA TELECOM CORPORATION CONFIDENTIAL
 * ______________________________________________________________
 *
 *  [2015] - [2020] China Telecom Corporation Limited,
 *  All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of China Telecom Corporation and its suppliers,
 * if any. The intellectual and technical concepts contained
 * herein are proprietary to China Telecom Corporation and its
 * suppliers and may be covered by China and Foreign Patents,
 * patents in process, and are protected by trade secret  or
 * copyright law. Dissemination of this information or
 * reproduction of this material is strictly forbidden unless prior
 * written permission is obtained from China Telecom Corporation.
 **********************************************************************/
/**
 *@Title: TimeUtils.java
 *@Description: TODO
 *<AUTHOR>
 *@date 2018年11月23日
 */

package com.wzsec.utils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 *@ClassName: TimeUtils
 *@Description: TODO
 *<AUTHOR>
 *@date 2018年11月23日
 */
public class TimeUtils {

	/**
	 * 将时间转换为字符串(yyyy-MM-dd HH:mm)
	 * @return String
	 */
	public static String DateToStr(Date date) {
	   SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
	   String str = format.format(date);
	   return str;
	}

    /**
     * 将时间转换为字符串
     * @param date
     * @return String
     */
    public static String DateToStr2(Date date) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String str = format.format(date);
        return str;
    }

	/**
	 * 将时间转换为字符串(yyyyMMddHHmm)
	 * @return String
	 */
	public static String DateToStr1(Date date) {
	   SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmm");
	   String str = format.format(date);
	   return str;
	}

    /**
     * 获取当前时间
     */
	public static String getNowTime() {
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		return df.format(new Date());
	}

    /**
     * 根据时间戳获取格式化时间
     * @param Timestamp
     */
	public static String TimestampToDateStr(long Timestamp) {
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		return df.format(new Date(Timestamp));
	}

    /**
     * 通过日历类的Calendar.add方法第二个参数-1达到前一天的月份的效果
     * @return
     */
    public static String getYesterdayByCalendar(String dateformat,int Intervalday){
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE,-Intervalday);
        Date time = calendar.getTime();
        String yesterday = new SimpleDateFormat(dateformat).format(time);
        return yesterday;
    }

    /**
     *用当天的日期的long型字符数串减去昨天日期long型字符数串
     * 其中：
     *  86400000L，它的意思是说1天的时间=24小时 x 60分钟 x 60秒 x 1000毫秒 单位是L。
     * @return
     */
    public static String getYesterdayByDate(){
       //实例化当天的日期
        Date today = new Date();
        //用当天的日期减去昨天的日期
        Date yesterdayDate = new Date(today.getTime()-86400000L);
        String yesterday = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss").format(yesterdayDate);
        return yesterday;
    }

    /**
	 * @Description:将当前时间转换为固定格式
	 * <AUTHOR> by xiongpf
	 * @date 2018-01-04
	 */
	public static String getNowTimeStrByFormat(String dateFormat) {
		Date now = new Date();
		SimpleDateFormat dateFormatVal = new SimpleDateFormat(dateFormat);
		String nowtime = dateFormatVal.format(now);
		return nowtime;
	}

	/**
	 * @Description:获取当前时间，时间格式为yyyy-MM-dd HH:mm:ss
	 * <AUTHOR> by xiongpf
	 * @date 2018-02-27
	 */
	public static String getNowTimeStr() {
		Date now = new Date();
		SimpleDateFormat dateFormatVal = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String nowtime = dateFormatVal.format(now);
		return nowtime;
	}

	 /**
     * 两个时间相差距离多少秒
     * @param str1 时间参数 1 格式：1990-01-01 12:00:00
     * @param str2 时间参数 2 格式：2009-01-01 12:00:00
     * @return long 返回值为：{秒}
     */
    public static long getDistanceTimes(String str1, String str2) {
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date one;
        Date two;
        long sec = 0;
        try {
            one = df.parse(str1);
            two = df.parse(str2);
            long time1 = one.getTime();
            long time2 = two.getTime();
            long diff ;
            if(time1<time2) {
                diff = time2 - time1;
            } else {
                diff = time1 - time2;
            }
            sec = diff/1000;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return sec;
    }

    /**
     * 时间日期转换
     * @param strDate 字符串yyyyMMddHHmmss
     * @return 字符串yyyy-MM-dd HH:mm:ss
     */
    public static String strToDateLong(String strDate) {
        Date date = new Date();
        try {
            date = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss").parse(strDate);//先按照原格式转换为时间
        } catch (ParseException e) {
            e.printStackTrace();
        }
        String str = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss").format(date);//再将时间转换为对应格式字符串
        return str;
    }

    /**
     * 时间字符串转为格式时间字符串
     * @param sourceFormat 原格式
     * @param descFormat 目的格式
     * @param strDate 时间字符串
     * @return
     */
    public static String strToFormatStr(String sourceFormat, String descFormat, String strDate) {
        Date date = new Date();
        try {
            date = new SimpleDateFormat(sourceFormat).parse(strDate);//先按照原格式转换为时间
        } catch (ParseException e) {
            e.printStackTrace();
        }
        String str = new SimpleDateFormat(descFormat).format(date);//再将时间转换为对应格式字符串
        return str;
    }

    /**
     * 判断传入时间是否大于当前时间
     * @param strDate 字符串字符串yyyy-MM-dd HH:mm:ss
     * @return flag
     */
    public static boolean DateStrThanCurrentTime(String strDate) {
        boolean flag = false;
        try {
            long strDatetime = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss").parse(strDate).getTime();//先按照原格式转换为时间
            long nowDatetime = System.currentTimeMillis();
            if(strDatetime>nowDatetime){
                flag = true;
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return flag;
    }

    public static void main(String[] args) {
        //System.out.println(DateStrThanCurrentTime("2020-09-14 15:33:39"));
        System.out.println(getNowTime());
    }


}
