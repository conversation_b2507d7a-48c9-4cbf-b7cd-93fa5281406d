package com.wzsec.modules.ic.repository;

import com.wzsec.modules.ic.domain.IcApprovallog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-04-07
 */
public interface IcApprovallogRepository extends JpaRepository<IcApprovallog, Integer>, JpaSpecificationExecutor<IcApprovallog> {

    /**
     * 累计数据
     *
     * @return int
     */
    @Query(value = " SELECT COUNT(*) FROM sdd_ic_approvallog ", nativeQuery = true)
    int findCumulativeDate();

    /**
     * 根据申请部门名称查询接口编码（根据 deptIds 精准匹配 applyorgname）
     *
     * @param deptIds 部门ID列表
     * @return 接口编码（keyid）集合，去重后
     */
    @Query(value = "SELECT DISTINCT al.keyid " +
            "FROM sdd_ic_approvallog al " +
            "JOIN ic_deptinfo d ON al.applyorgname = d.deptname " +
            "WHERE d.deptid IN (:deptIds)", nativeQuery = true)
    List<String> queryApiCodeByApplyorgName(@Param("deptIds") List<String> deptIds);


    /**
     * 根据申请部门名称查询AK编码（根据 deptIds 精准匹配 ak）
     *
     * @param deptIds 部门ID列表
     * @return 接口编码（keyid）集合，去重后
     */
    @Query(value = "SELECT DISTINCT al.ak " +
            "FROM sdd_ic_approvallog al " +
            "JOIN ic_deptinfo d ON al.applyorgname = d.deptname " +
            "WHERE d.deptid IN (:deptIds)", nativeQuery = true)
    List<String> queryAkByApplyorgName(@Param("deptIds") List<String> deptIds);

}