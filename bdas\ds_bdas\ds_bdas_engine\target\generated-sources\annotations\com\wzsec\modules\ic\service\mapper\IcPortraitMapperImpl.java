package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.APIPortrait;
import com.wzsec.modules.ic.service.dto.IcPortraitDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:27:07+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class IcPortraitMapperImpl implements IcPortraitMapper {

    @Override
    public IcPortraitDto toDto(APIPortrait entity) {
        if ( entity == null ) {
            return null;
        }

        IcPortraitDto icPortraitDto = new IcPortraitDto();

        icPortraitDto.setActualinput( entity.getActualinput() );
        icPortraitDto.setActualoutput( entity.getActualoutput() );
        icPortraitDto.setApicode( entity.getApicode() );
        icPortraitDto.setApiname( entity.getApiname() );
        icPortraitDto.setAttackdetection( entity.getAttackdetection() );
        icPortraitDto.setAuthmethod( entity.getAuthmethod() );
        icPortraitDto.setAuthreqmethod( entity.getAuthreqmethod() );
        icPortraitDto.setAuthrisk( entity.getAuthrisk() );
        icPortraitDto.setAuthserviceip( entity.getAuthserviceip() );
        icPortraitDto.setAuthserviceport( entity.getAuthserviceport() );
        icPortraitDto.setBusinessid( entity.getBusinessid() );
        icPortraitDto.setBusinessname( entity.getBusinessname() );
        icPortraitDto.setCallbegintime( entity.getCallbegintime() );
        icPortraitDto.setCallendtime( entity.getCallendtime() );
        icPortraitDto.setComplianceauditrisk( entity.getComplianceauditrisk() );
        icPortraitDto.setCreatetime( entity.getCreatetime() );
        icPortraitDto.setDataformat( entity.getDataformat() );
        icPortraitDto.setErrorchecksum( entity.getErrorchecksum() );
        icPortraitDto.setErrorchecktype( entity.getErrorchecktype() );
        icPortraitDto.setId( entity.getId() );
        icPortraitDto.setInfrequentlycalldecide( entity.getInfrequentlycalldecide() );
        icPortraitDto.setInputparams( entity.getInputparams() );
        icPortraitDto.setInvokeType( entity.getInvokeType() );
        icPortraitDto.setInvokingrecordmax( entity.getInvokingrecordmax() );
        icPortraitDto.setInvokingrecordmin( entity.getInvokingrecordmin() );
        icPortraitDto.setInvokingrecordsum( entity.getInvokingrecordsum() );
        icPortraitDto.setInvokingrecordweekavg( entity.getInvokingrecordweekavg() );
        icPortraitDto.setNote( entity.getNote() );
        icPortraitDto.setOrganization( entity.getOrganization() );
        icPortraitDto.setOutdatacallcount( entity.getOutdatacallcount() );
        icPortraitDto.setOutdatacallrisk( entity.getOutdatacallrisk() );
        icPortraitDto.setOutputparams( entity.getOutputparams() );
        icPortraitDto.setRegisterUnit( entity.getRegisterUnit() );
        icPortraitDto.setSafetyindex( entity.getSafetyindex() );
        icPortraitDto.setSensitiverisk( entity.getSensitiverisk() );
        icPortraitDto.setSensitivetype( entity.getSensitivetype() );
        icPortraitDto.setSilentaccount( entity.getSilentaccount() );
        icPortraitDto.setSilentaccountrisk( entity.getSilentaccountrisk() );
        icPortraitDto.setSparefield1( entity.getSparefield1() );
        icPortraitDto.setSparefield10( entity.getSparefield10() );
        icPortraitDto.setSparefield2( entity.getSparefield2() );
        icPortraitDto.setSparefield3( entity.getSparefield3() );
        icPortraitDto.setSparefield4( entity.getSparefield4() );
        icPortraitDto.setSparefield5( entity.getSparefield5() );
        icPortraitDto.setSparefield6( entity.getSparefield6() );
        icPortraitDto.setSparefield7( entity.getSparefield7() );
        icPortraitDto.setSparefield8( entity.getSparefield8() );
        icPortraitDto.setSparefield9( entity.getSparefield9() );
        icPortraitDto.setUnexpectedfieldname( entity.getUnexpectedfieldname() );
        icPortraitDto.setUnexpectedfieldvalue( entity.getUnexpectedfieldvalue() );
        icPortraitDto.setUnexpectedmethod( entity.getUnexpectedmethod() );
        icPortraitDto.setUnexpectedrisk( entity.getUnexpectedrisk() );
        icPortraitDto.setUnusedcheckdecide( entity.getUnusedcheckdecide() );
        icPortraitDto.setUpdatetime( entity.getUpdatetime() );
        icPortraitDto.setUrl( entity.getUrl() );
        icPortraitDto.setUsercallclientip( entity.getUsercallclientip() );
        icPortraitDto.setUsercallcount( entity.getUsercallcount() );
        icPortraitDto.setUsercallrisk( entity.getUsercallrisk() );

        return icPortraitDto;
    }

    @Override
    public List<IcPortraitDto> toDto(List<APIPortrait> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcPortraitDto> list = new ArrayList<IcPortraitDto>( entityList.size() );
        for ( APIPortrait aPIPortrait : entityList ) {
            list.add( toDto( aPIPortrait ) );
        }

        return list;
    }

    @Override
    public APIPortrait toEntity(IcPortraitDto dto) {
        if ( dto == null ) {
            return null;
        }

        APIPortrait aPIPortrait = new APIPortrait();

        aPIPortrait.setActualinput( dto.getActualinput() );
        aPIPortrait.setActualoutput( dto.getActualoutput() );
        aPIPortrait.setApicode( dto.getApicode() );
        aPIPortrait.setApiname( dto.getApiname() );
        aPIPortrait.setAttackdetection( dto.getAttackdetection() );
        aPIPortrait.setAuthmethod( dto.getAuthmethod() );
        aPIPortrait.setAuthreqmethod( dto.getAuthreqmethod() );
        aPIPortrait.setAuthrisk( dto.getAuthrisk() );
        aPIPortrait.setAuthserviceip( dto.getAuthserviceip() );
        aPIPortrait.setAuthserviceport( dto.getAuthserviceport() );
        aPIPortrait.setBusinessid( dto.getBusinessid() );
        aPIPortrait.setBusinessname( dto.getBusinessname() );
        aPIPortrait.setCallbegintime( dto.getCallbegintime() );
        aPIPortrait.setCallendtime( dto.getCallendtime() );
        aPIPortrait.setComplianceauditrisk( dto.getComplianceauditrisk() );
        aPIPortrait.setCreatetime( dto.getCreatetime() );
        aPIPortrait.setDataformat( dto.getDataformat() );
        aPIPortrait.setErrorchecksum( dto.getErrorchecksum() );
        aPIPortrait.setErrorchecktype( dto.getErrorchecktype() );
        aPIPortrait.setId( dto.getId() );
        aPIPortrait.setInfrequentlycalldecide( dto.getInfrequentlycalldecide() );
        aPIPortrait.setInputparams( dto.getInputparams() );
        aPIPortrait.setInvokeType( dto.getInvokeType() );
        aPIPortrait.setInvokingrecordmax( dto.getInvokingrecordmax() );
        aPIPortrait.setInvokingrecordmin( dto.getInvokingrecordmin() );
        aPIPortrait.setInvokingrecordsum( dto.getInvokingrecordsum() );
        aPIPortrait.setInvokingrecordweekavg( dto.getInvokingrecordweekavg() );
        aPIPortrait.setNote( dto.getNote() );
        aPIPortrait.setOrganization( dto.getOrganization() );
        aPIPortrait.setOutdatacallcount( dto.getOutdatacallcount() );
        aPIPortrait.setOutdatacallrisk( dto.getOutdatacallrisk() );
        aPIPortrait.setOutputparams( dto.getOutputparams() );
        aPIPortrait.setRegisterUnit( dto.getRegisterUnit() );
        aPIPortrait.setSafetyindex( dto.getSafetyindex() );
        aPIPortrait.setSensitiverisk( dto.getSensitiverisk() );
        aPIPortrait.setSensitivetype( dto.getSensitivetype() );
        aPIPortrait.setSilentaccount( dto.getSilentaccount() );
        aPIPortrait.setSilentaccountrisk( dto.getSilentaccountrisk() );
        aPIPortrait.setSparefield1( dto.getSparefield1() );
        aPIPortrait.setSparefield10( dto.getSparefield10() );
        aPIPortrait.setSparefield2( dto.getSparefield2() );
        aPIPortrait.setSparefield3( dto.getSparefield3() );
        aPIPortrait.setSparefield4( dto.getSparefield4() );
        aPIPortrait.setSparefield5( dto.getSparefield5() );
        aPIPortrait.setSparefield6( dto.getSparefield6() );
        aPIPortrait.setSparefield7( dto.getSparefield7() );
        aPIPortrait.setSparefield8( dto.getSparefield8() );
        aPIPortrait.setSparefield9( dto.getSparefield9() );
        aPIPortrait.setUnexpectedfieldname( dto.getUnexpectedfieldname() );
        aPIPortrait.setUnexpectedfieldvalue( dto.getUnexpectedfieldvalue() );
        aPIPortrait.setUnexpectedmethod( dto.getUnexpectedmethod() );
        aPIPortrait.setUnexpectedrisk( dto.getUnexpectedrisk() );
        aPIPortrait.setUnusedcheckdecide( dto.getUnusedcheckdecide() );
        aPIPortrait.setUpdatetime( dto.getUpdatetime() );
        aPIPortrait.setUrl( dto.getUrl() );
        aPIPortrait.setUsercallclientip( dto.getUsercallclientip() );
        aPIPortrait.setUsercallcount( dto.getUsercallcount() );
        aPIPortrait.setUsercallrisk( dto.getUsercallrisk() );

        return aPIPortrait;
    }

    @Override
    public List<APIPortrait> toEntity(List<IcPortraitDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<APIPortrait> list = new ArrayList<APIPortrait>( dtoList.size() );
        for ( IcPortraitDto icPortraitDto : dtoList ) {
            list.add( toEntity( icPortraitDto ) );
        }

        return list;
    }
}
