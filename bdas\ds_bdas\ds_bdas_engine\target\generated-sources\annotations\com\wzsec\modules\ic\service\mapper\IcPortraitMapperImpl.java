package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.APIPortrait;
import com.wzsec.modules.ic.service.dto.IcPortraitDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:01+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class IcPortraitMapperImpl implements IcPortraitMapper {

    @Override
    public APIPortrait toEntity(IcPortraitDto dto) {
        if ( dto == null ) {
            return null;
        }

        APIPortrait aPIPortrait = new APIPortrait();

        aPIPortrait.setId( dto.getId() );
        aPIPortrait.setApicode( dto.getApicode() );
        aPIPortrait.setApiname( dto.getApiname() );
        aPIPortrait.setUrl( dto.getUrl() );
        aPIPortrait.setInvokeType( dto.getInvokeType() );
        aPIPortrait.setDataformat( dto.getDataformat() );
        aPIPortrait.setInputparams( dto.getInputparams() );
        aPIPortrait.setOutputparams( dto.getOutputparams() );
        aPIPortrait.setOrganization( dto.getOrganization() );
        aPIPortrait.setRegisterUnit( dto.getRegisterUnit() );
        aPIPortrait.setBusinessname( dto.getBusinessname() );
        aPIPortrait.setBusinessid( dto.getBusinessid() );
        aPIPortrait.setSafetyindex( dto.getSafetyindex() );
        aPIPortrait.setSensitivetype( dto.getSensitivetype() );
        aPIPortrait.setSensitiverisk( dto.getSensitiverisk() );
        aPIPortrait.setUnexpectedmethod( dto.getUnexpectedmethod() );
        aPIPortrait.setUnexpectedfieldname( dto.getUnexpectedfieldname() );
        aPIPortrait.setUnexpectedfieldvalue( dto.getUnexpectedfieldvalue() );
        aPIPortrait.setUnexpectedrisk( dto.getUnexpectedrisk() );
        aPIPortrait.setOutdatacallcount( dto.getOutdatacallcount() );
        aPIPortrait.setOutdatacallrisk( dto.getOutdatacallrisk() );
        aPIPortrait.setAuthreqmethod( dto.getAuthreqmethod() );
        aPIPortrait.setAuthmethod( dto.getAuthmethod() );
        aPIPortrait.setAuthserviceip( dto.getAuthserviceip() );
        aPIPortrait.setAuthserviceport( dto.getAuthserviceport() );
        aPIPortrait.setAuthrisk( dto.getAuthrisk() );
        aPIPortrait.setActualinput( dto.getActualinput() );
        aPIPortrait.setActualoutput( dto.getActualoutput() );
        aPIPortrait.setComplianceauditrisk( dto.getComplianceauditrisk() );
        aPIPortrait.setSilentaccount( dto.getSilentaccount() );
        aPIPortrait.setSilentaccountrisk( dto.getSilentaccountrisk() );
        aPIPortrait.setUsercallclientip( dto.getUsercallclientip() );
        aPIPortrait.setUsercallcount( dto.getUsercallcount() );
        aPIPortrait.setUsercallrisk( dto.getUsercallrisk() );
        aPIPortrait.setInvokingrecordsum( dto.getInvokingrecordsum() );
        aPIPortrait.setInvokingrecordweekavg( dto.getInvokingrecordweekavg() );
        aPIPortrait.setInvokingrecordmax( dto.getInvokingrecordmax() );
        aPIPortrait.setInvokingrecordmin( dto.getInvokingrecordmin() );
        aPIPortrait.setInfrequentlycalldecide( dto.getInfrequentlycalldecide() );
        aPIPortrait.setCallbegintime( dto.getCallbegintime() );
        aPIPortrait.setCallendtime( dto.getCallendtime() );
        aPIPortrait.setUnusedcheckdecide( dto.getUnusedcheckdecide() );
        aPIPortrait.setErrorchecktype( dto.getErrorchecktype() );
        aPIPortrait.setErrorchecksum( dto.getErrorchecksum() );
        aPIPortrait.setAttackdetection( dto.getAttackdetection() );
        aPIPortrait.setCreatetime( dto.getCreatetime() );
        aPIPortrait.setUpdatetime( dto.getUpdatetime() );
        aPIPortrait.setNote( dto.getNote() );
        aPIPortrait.setSparefield1( dto.getSparefield1() );
        aPIPortrait.setSparefield2( dto.getSparefield2() );
        aPIPortrait.setSparefield3( dto.getSparefield3() );
        aPIPortrait.setSparefield4( dto.getSparefield4() );
        aPIPortrait.setSparefield5( dto.getSparefield5() );
        aPIPortrait.setSparefield6( dto.getSparefield6() );
        aPIPortrait.setSparefield7( dto.getSparefield7() );
        aPIPortrait.setSparefield8( dto.getSparefield8() );
        aPIPortrait.setSparefield9( dto.getSparefield9() );
        aPIPortrait.setSparefield10( dto.getSparefield10() );

        return aPIPortrait;
    }

    @Override
    public IcPortraitDto toDto(APIPortrait entity) {
        if ( entity == null ) {
            return null;
        }

        IcPortraitDto icPortraitDto = new IcPortraitDto();

        icPortraitDto.setId( entity.getId() );
        icPortraitDto.setApicode( entity.getApicode() );
        icPortraitDto.setApiname( entity.getApiname() );
        icPortraitDto.setUrl( entity.getUrl() );
        icPortraitDto.setInvokeType( entity.getInvokeType() );
        icPortraitDto.setDataformat( entity.getDataformat() );
        icPortraitDto.setInputparams( entity.getInputparams() );
        icPortraitDto.setOutputparams( entity.getOutputparams() );
        icPortraitDto.setOrganization( entity.getOrganization() );
        icPortraitDto.setRegisterUnit( entity.getRegisterUnit() );
        icPortraitDto.setBusinessname( entity.getBusinessname() );
        icPortraitDto.setBusinessid( entity.getBusinessid() );
        icPortraitDto.setSafetyindex( entity.getSafetyindex() );
        icPortraitDto.setSensitivetype( entity.getSensitivetype() );
        icPortraitDto.setSensitiverisk( entity.getSensitiverisk() );
        icPortraitDto.setUnexpectedmethod( entity.getUnexpectedmethod() );
        icPortraitDto.setUnexpectedfieldname( entity.getUnexpectedfieldname() );
        icPortraitDto.setUnexpectedfieldvalue( entity.getUnexpectedfieldvalue() );
        icPortraitDto.setUnexpectedrisk( entity.getUnexpectedrisk() );
        icPortraitDto.setOutdatacallcount( entity.getOutdatacallcount() );
        icPortraitDto.setOutdatacallrisk( entity.getOutdatacallrisk() );
        icPortraitDto.setAuthreqmethod( entity.getAuthreqmethod() );
        icPortraitDto.setAuthmethod( entity.getAuthmethod() );
        icPortraitDto.setAuthserviceip( entity.getAuthserviceip() );
        icPortraitDto.setAuthserviceport( entity.getAuthserviceport() );
        icPortraitDto.setAuthrisk( entity.getAuthrisk() );
        icPortraitDto.setActualinput( entity.getActualinput() );
        icPortraitDto.setActualoutput( entity.getActualoutput() );
        icPortraitDto.setComplianceauditrisk( entity.getComplianceauditrisk() );
        icPortraitDto.setSilentaccount( entity.getSilentaccount() );
        icPortraitDto.setSilentaccountrisk( entity.getSilentaccountrisk() );
        icPortraitDto.setUsercallclientip( entity.getUsercallclientip() );
        icPortraitDto.setUsercallcount( entity.getUsercallcount() );
        icPortraitDto.setUsercallrisk( entity.getUsercallrisk() );
        icPortraitDto.setInvokingrecordsum( entity.getInvokingrecordsum() );
        icPortraitDto.setInvokingrecordweekavg( entity.getInvokingrecordweekavg() );
        icPortraitDto.setInvokingrecordmax( entity.getInvokingrecordmax() );
        icPortraitDto.setInvokingrecordmin( entity.getInvokingrecordmin() );
        icPortraitDto.setInfrequentlycalldecide( entity.getInfrequentlycalldecide() );
        icPortraitDto.setCallbegintime( entity.getCallbegintime() );
        icPortraitDto.setCallendtime( entity.getCallendtime() );
        icPortraitDto.setUnusedcheckdecide( entity.getUnusedcheckdecide() );
        icPortraitDto.setErrorchecktype( entity.getErrorchecktype() );
        icPortraitDto.setErrorchecksum( entity.getErrorchecksum() );
        icPortraitDto.setAttackdetection( entity.getAttackdetection() );
        icPortraitDto.setCreatetime( entity.getCreatetime() );
        icPortraitDto.setUpdatetime( entity.getUpdatetime() );
        icPortraitDto.setNote( entity.getNote() );
        icPortraitDto.setSparefield1( entity.getSparefield1() );
        icPortraitDto.setSparefield2( entity.getSparefield2() );
        icPortraitDto.setSparefield3( entity.getSparefield3() );
        icPortraitDto.setSparefield4( entity.getSparefield4() );
        icPortraitDto.setSparefield5( entity.getSparefield5() );
        icPortraitDto.setSparefield6( entity.getSparefield6() );
        icPortraitDto.setSparefield7( entity.getSparefield7() );
        icPortraitDto.setSparefield8( entity.getSparefield8() );
        icPortraitDto.setSparefield9( entity.getSparefield9() );
        icPortraitDto.setSparefield10( entity.getSparefield10() );

        return icPortraitDto;
    }

    @Override
    public List<APIPortrait> toEntity(List<IcPortraitDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<APIPortrait> list = new ArrayList<APIPortrait>( dtoList.size() );
        for ( IcPortraitDto icPortraitDto : dtoList ) {
            list.add( toEntity( icPortraitDto ) );
        }

        return list;
    }

    @Override
    public List<IcPortraitDto> toDto(List<APIPortrait> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcPortraitDto> list = new ArrayList<IcPortraitDto>( entityList.size() );
        for ( APIPortrait aPIPortrait : entityList ) {
            list.add( toDto( aPIPortrait ) );
        }

        return list;
    }
}
