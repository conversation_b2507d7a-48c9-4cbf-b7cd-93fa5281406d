package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcCheckmodel;
import com.wzsec.modules.ic.service.dto.IcCheckmodelDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:29+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class IcCheckmodelMapperImpl implements IcCheckmodelMapper {

    @Override
    public IcCheckmodelDto toDto(IcCheckmodel entity) {
        if ( entity == null ) {
            return null;
        }

        IcCheckmodelDto icCheckmodelDto = new IcCheckmodelDto();

        icCheckmodelDto.setBehaviourcatagory( entity.getBehaviourcatagory() );
        icCheckmodelDto.setBusinesscatagory( entity.getBusinesscatagory() );
        icCheckmodelDto.setEventid( entity.getEventid() );
        icCheckmodelDto.setEventlevel( entity.getEventlevel() );
        icCheckmodelDto.setEventname( entity.getEventname() );
        icCheckmodelDto.setId( entity.getId() );
        icCheckmodelDto.setModelconfig( entity.getModelconfig() );
        icCheckmodelDto.setModelparameter( entity.getModelparameter() );
        icCheckmodelDto.setModeltask( entity.getModeltask() );
        icCheckmodelDto.setRuledescription( entity.getRuledescription() );
        icCheckmodelDto.setSource( entity.getSource() );
        icCheckmodelDto.setSparefield1( entity.getSparefield1() );
        icCheckmodelDto.setSparefield2( entity.getSparefield2() );
        icCheckmodelDto.setSparefield3( entity.getSparefield3() );
        icCheckmodelDto.setSparefield4( entity.getSparefield4() );
        icCheckmodelDto.setStatus( entity.getStatus() );

        return icCheckmodelDto;
    }

    @Override
    public List<IcCheckmodelDto> toDto(List<IcCheckmodel> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcCheckmodelDto> list = new ArrayList<IcCheckmodelDto>( entityList.size() );
        for ( IcCheckmodel icCheckmodel : entityList ) {
            list.add( toDto( icCheckmodel ) );
        }

        return list;
    }

    @Override
    public IcCheckmodel toEntity(IcCheckmodelDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcCheckmodel icCheckmodel = new IcCheckmodel();

        icCheckmodel.setBehaviourcatagory( dto.getBehaviourcatagory() );
        icCheckmodel.setBusinesscatagory( dto.getBusinesscatagory() );
        icCheckmodel.setEventid( dto.getEventid() );
        icCheckmodel.setEventlevel( dto.getEventlevel() );
        icCheckmodel.setEventname( dto.getEventname() );
        icCheckmodel.setId( dto.getId() );
        icCheckmodel.setModelconfig( dto.getModelconfig() );
        icCheckmodel.setModelparameter( dto.getModelparameter() );
        icCheckmodel.setModeltask( dto.getModeltask() );
        icCheckmodel.setRuledescription( dto.getRuledescription() );
        icCheckmodel.setSource( dto.getSource() );
        icCheckmodel.setSparefield1( dto.getSparefield1() );
        icCheckmodel.setSparefield2( dto.getSparefield2() );
        icCheckmodel.setSparefield3( dto.getSparefield3() );
        icCheckmodel.setSparefield4( dto.getSparefield4() );
        icCheckmodel.setStatus( dto.getStatus() );

        return icCheckmodel;
    }

    @Override
    public List<IcCheckmodel> toEntity(List<IcCheckmodelDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcCheckmodel> list = new ArrayList<IcCheckmodel>( dtoList.size() );
        for ( IcCheckmodelDto icCheckmodelDto : dtoList ) {
            list.add( toEntity( icCheckmodelDto ) );
        }

        return list;
    }
}
