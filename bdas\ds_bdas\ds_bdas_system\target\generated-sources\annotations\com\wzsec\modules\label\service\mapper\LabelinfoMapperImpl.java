package com.wzsec.modules.label.service.mapper;

import com.wzsec.modules.label.domain.Labelinfo;
import com.wzsec.modules.label.service.dto.LabelinfoDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:26:46+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class LabelinfoMapperImpl implements LabelinfoMapper {

    @Override
    public LabelinfoDto toDto(Labelinfo entity) {
        if ( entity == null ) {
            return null;
        }

        LabelinfoDto labelinfoDto = new LabelinfoDto();

        labelinfoDto.setCreatetime( entity.getCreatetime() );
        labelinfoDto.setCreateuser( entity.getCreateuser() );
        labelinfoDto.setId( entity.getId() );
        labelinfoDto.setLabelname( entity.getLabelname() );
        labelinfoDto.setLabeltype( entity.getLabeltype() );
        labelinfoDto.setLabelvalue( entity.getLabelvalue() );
        labelinfoDto.setNote( entity.getNote() );
        labelinfoDto.setScope( entity.getScope() );
        labelinfoDto.setSparefield1( entity.getSparefield1() );
        labelinfoDto.setSparefield2( entity.getSparefield2() );
        labelinfoDto.setSparefield3( entity.getSparefield3() );
        labelinfoDto.setUpdatetime( entity.getUpdatetime() );
        labelinfoDto.setUpdateuser( entity.getUpdateuser() );

        return labelinfoDto;
    }

    @Override
    public List<LabelinfoDto> toDto(List<Labelinfo> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<LabelinfoDto> list = new ArrayList<LabelinfoDto>( entityList.size() );
        for ( Labelinfo labelinfo : entityList ) {
            list.add( toDto( labelinfo ) );
        }

        return list;
    }

    @Override
    public Labelinfo toEntity(LabelinfoDto dto) {
        if ( dto == null ) {
            return null;
        }

        Labelinfo labelinfo = new Labelinfo();

        labelinfo.setCreatetime( dto.getCreatetime() );
        labelinfo.setCreateuser( dto.getCreateuser() );
        labelinfo.setId( dto.getId() );
        labelinfo.setLabelname( dto.getLabelname() );
        labelinfo.setLabeltype( dto.getLabeltype() );
        labelinfo.setLabelvalue( dto.getLabelvalue() );
        labelinfo.setNote( dto.getNote() );
        labelinfo.setScope( dto.getScope() );
        labelinfo.setSparefield1( dto.getSparefield1() );
        labelinfo.setSparefield2( dto.getSparefield2() );
        labelinfo.setSparefield3( dto.getSparefield3() );
        labelinfo.setUpdatetime( dto.getUpdatetime() );
        labelinfo.setUpdateuser( dto.getUpdateuser() );

        return labelinfo;
    }

    @Override
    public List<Labelinfo> toEntity(List<LabelinfoDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Labelinfo> list = new ArrayList<Labelinfo>( dtoList.size() );
        for ( LabelinfoDto labelinfoDto : dtoList ) {
            list.add( toEntity( labelinfoDto ) );
        }

        return list;
    }
}
