package com.wzsec.modules.label.service.mapper;

import com.wzsec.modules.label.domain.Labelinfo;
import com.wzsec.modules.label.service.dto.LabelinfoDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:31+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class LabelinfoMapperImpl implements LabelinfoMapper {

    @Override
    public Labelinfo toEntity(LabelinfoDto dto) {
        if ( dto == null ) {
            return null;
        }

        Labelinfo labelinfo = new Labelinfo();

        labelinfo.setId( dto.getId() );
        labelinfo.setLabeltype( dto.getLabeltype() );
        labelinfo.setLabelname( dto.getLabelname() );
        labelinfo.setLabelvalue( dto.getLabelvalue() );
        labelinfo.setScope( dto.getScope() );
        labelinfo.setNote( dto.getNote() );
        labelinfo.setCreateuser( dto.getCreateuser() );
        labelinfo.setCreatetime( dto.getCreatetime() );
        labelinfo.setUpdateuser( dto.getUpdateuser() );
        labelinfo.setUpdatetime( dto.getUpdatetime() );
        labelinfo.setSparefield1( dto.getSparefield1() );
        labelinfo.setSparefield2( dto.getSparefield2() );
        labelinfo.setSparefield3( dto.getSparefield3() );

        return labelinfo;
    }

    @Override
    public LabelinfoDto toDto(Labelinfo entity) {
        if ( entity == null ) {
            return null;
        }

        LabelinfoDto labelinfoDto = new LabelinfoDto();

        labelinfoDto.setId( entity.getId() );
        labelinfoDto.setLabeltype( entity.getLabeltype() );
        labelinfoDto.setLabelname( entity.getLabelname() );
        labelinfoDto.setLabelvalue( entity.getLabelvalue() );
        labelinfoDto.setScope( entity.getScope() );
        labelinfoDto.setNote( entity.getNote() );
        labelinfoDto.setCreateuser( entity.getCreateuser() );
        labelinfoDto.setCreatetime( entity.getCreatetime() );
        labelinfoDto.setUpdateuser( entity.getUpdateuser() );
        labelinfoDto.setUpdatetime( entity.getUpdatetime() );
        labelinfoDto.setSparefield1( entity.getSparefield1() );
        labelinfoDto.setSparefield2( entity.getSparefield2() );
        labelinfoDto.setSparefield3( entity.getSparefield3() );

        return labelinfoDto;
    }

    @Override
    public List<Labelinfo> toEntity(List<LabelinfoDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Labelinfo> list = new ArrayList<Labelinfo>( dtoList.size() );
        for ( LabelinfoDto labelinfoDto : dtoList ) {
            list.add( toEntity( labelinfoDto ) );
        }

        return list;
    }

    @Override
    public List<LabelinfoDto> toDto(List<Labelinfo> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<LabelinfoDto> list = new ArrayList<LabelinfoDto>( entityList.size() );
        for ( Labelinfo labelinfo : entityList ) {
            list.add( toDto( labelinfo ) );
        }

        return list;
    }
}
