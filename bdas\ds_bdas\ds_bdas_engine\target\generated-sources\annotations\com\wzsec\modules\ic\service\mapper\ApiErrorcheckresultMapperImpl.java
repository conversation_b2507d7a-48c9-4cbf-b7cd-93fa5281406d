package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.ApiErrorcheckresult;
import com.wzsec.modules.ic.service.dto.ApiErrorcheckresultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:01+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ApiErrorcheckresultMapperImpl implements ApiErrorcheckresultMapper {

    @Override
    public ApiErrorcheckresult toEntity(ApiErrorcheckresultDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiErrorcheckresult apiErrorcheckresult = new ApiErrorcheckresult();

        apiErrorcheckresult.setId( dto.getId() );
        apiErrorcheckresult.setApicode( dto.getApicode() );
        apiErrorcheckresult.setErrortype( dto.getErrortype() );
        apiErrorcheckresult.setErrorcount( dto.getErrorcount() );
        apiErrorcheckresult.setChecktime( dto.getChecktime() );
        apiErrorcheckresult.setSparefield1( dto.getSparefield1() );
        apiErrorcheckresult.setSparefield2( dto.getSparefield2() );
        apiErrorcheckresult.setSparefield3( dto.getSparefield3() );
        apiErrorcheckresult.setSparefield4( dto.getSparefield4() );
        apiErrorcheckresult.setRisk( dto.getRisk() );
        apiErrorcheckresult.setAk( dto.getAk() );
        apiErrorcheckresult.setApplyorgname( dto.getApplyorgname() );
        apiErrorcheckresult.setApiname( dto.getApiname() );
        apiErrorcheckresult.setApiurl( dto.getApiurl() );
        apiErrorcheckresult.setAkapicode( dto.getAkapicode() );
        apiErrorcheckresult.setSystemname( dto.getSystemname() );
        apiErrorcheckresult.setReqip( dto.getReqip() );

        return apiErrorcheckresult;
    }

    @Override
    public ApiErrorcheckresultDto toDto(ApiErrorcheckresult entity) {
        if ( entity == null ) {
            return null;
        }

        ApiErrorcheckresultDto apiErrorcheckresultDto = new ApiErrorcheckresultDto();

        apiErrorcheckresultDto.setId( entity.getId() );
        apiErrorcheckresultDto.setApicode( entity.getApicode() );
        apiErrorcheckresultDto.setErrortype( entity.getErrortype() );
        apiErrorcheckresultDto.setErrorcount( entity.getErrorcount() );
        apiErrorcheckresultDto.setChecktime( entity.getChecktime() );
        apiErrorcheckresultDto.setSparefield1( entity.getSparefield1() );
        apiErrorcheckresultDto.setSparefield2( entity.getSparefield2() );
        apiErrorcheckresultDto.setSparefield3( entity.getSparefield3() );
        apiErrorcheckresultDto.setSparefield4( entity.getSparefield4() );
        apiErrorcheckresultDto.setRisk( entity.getRisk() );
        apiErrorcheckresultDto.setAk( entity.getAk() );
        apiErrorcheckresultDto.setApplyorgname( entity.getApplyorgname() );
        apiErrorcheckresultDto.setApiname( entity.getApiname() );
        apiErrorcheckresultDto.setApiurl( entity.getApiurl() );
        apiErrorcheckresultDto.setAkapicode( entity.getAkapicode() );
        apiErrorcheckresultDto.setSystemname( entity.getSystemname() );
        apiErrorcheckresultDto.setReqip( entity.getReqip() );

        return apiErrorcheckresultDto;
    }

    @Override
    public List<ApiErrorcheckresult> toEntity(List<ApiErrorcheckresultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiErrorcheckresult> list = new ArrayList<ApiErrorcheckresult>( dtoList.size() );
        for ( ApiErrorcheckresultDto apiErrorcheckresultDto : dtoList ) {
            list.add( toEntity( apiErrorcheckresultDto ) );
        }

        return list;
    }

    @Override
    public List<ApiErrorcheckresultDto> toDto(List<ApiErrorcheckresult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiErrorcheckresultDto> list = new ArrayList<ApiErrorcheckresultDto>( entityList.size() );
        for ( ApiErrorcheckresult apiErrorcheckresult : entityList ) {
            list.add( toDto( apiErrorcheckresult ) );
        }

        return list;
    }
}
