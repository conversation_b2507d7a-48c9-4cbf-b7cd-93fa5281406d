package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.ApiErrorcheckresult;
import com.wzsec.modules.ic.service.dto.ApiErrorcheckresultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:27:08+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ApiErrorcheckresultMapperImpl implements ApiErrorcheckresultMapper {

    @Override
    public ApiErrorcheckresultDto toDto(ApiErrorcheckresult entity) {
        if ( entity == null ) {
            return null;
        }

        ApiErrorcheckresultDto apiErrorcheckresultDto = new ApiErrorcheckresultDto();

        apiErrorcheckresultDto.setAk( entity.getAk() );
        apiErrorcheckresultDto.setAkapicode( entity.getAkapicode() );
        apiErrorcheckresultDto.setApicode( entity.getApicode() );
        apiErrorcheckresultDto.setApiname( entity.getApiname() );
        apiErrorcheckresultDto.setApiurl( entity.getApiurl() );
        apiErrorcheckresultDto.setApplyorgname( entity.getApplyorgname() );
        apiErrorcheckresultDto.setChecktime( entity.getChecktime() );
        apiErrorcheckresultDto.setErrorcount( entity.getErrorcount() );
        apiErrorcheckresultDto.setErrortype( entity.getErrortype() );
        apiErrorcheckresultDto.setId( entity.getId() );
        apiErrorcheckresultDto.setReqip( entity.getReqip() );
        apiErrorcheckresultDto.setRisk( entity.getRisk() );
        apiErrorcheckresultDto.setSparefield1( entity.getSparefield1() );
        apiErrorcheckresultDto.setSparefield2( entity.getSparefield2() );
        apiErrorcheckresultDto.setSparefield3( entity.getSparefield3() );
        apiErrorcheckresultDto.setSparefield4( entity.getSparefield4() );
        apiErrorcheckresultDto.setSystemname( entity.getSystemname() );

        return apiErrorcheckresultDto;
    }

    @Override
    public List<ApiErrorcheckresultDto> toDto(List<ApiErrorcheckresult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiErrorcheckresultDto> list = new ArrayList<ApiErrorcheckresultDto>( entityList.size() );
        for ( ApiErrorcheckresult apiErrorcheckresult : entityList ) {
            list.add( toDto( apiErrorcheckresult ) );
        }

        return list;
    }

    @Override
    public ApiErrorcheckresult toEntity(ApiErrorcheckresultDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiErrorcheckresult apiErrorcheckresult = new ApiErrorcheckresult();

        apiErrorcheckresult.setAk( dto.getAk() );
        apiErrorcheckresult.setAkapicode( dto.getAkapicode() );
        apiErrorcheckresult.setApicode( dto.getApicode() );
        apiErrorcheckresult.setApiname( dto.getApiname() );
        apiErrorcheckresult.setApiurl( dto.getApiurl() );
        apiErrorcheckresult.setApplyorgname( dto.getApplyorgname() );
        apiErrorcheckresult.setChecktime( dto.getChecktime() );
        apiErrorcheckresult.setErrorcount( dto.getErrorcount() );
        apiErrorcheckresult.setErrortype( dto.getErrortype() );
        apiErrorcheckresult.setId( dto.getId() );
        apiErrorcheckresult.setReqip( dto.getReqip() );
        apiErrorcheckresult.setRisk( dto.getRisk() );
        apiErrorcheckresult.setSparefield1( dto.getSparefield1() );
        apiErrorcheckresult.setSparefield2( dto.getSparefield2() );
        apiErrorcheckresult.setSparefield3( dto.getSparefield3() );
        apiErrorcheckresult.setSparefield4( dto.getSparefield4() );
        apiErrorcheckresult.setSystemname( dto.getSystemname() );

        return apiErrorcheckresult;
    }

    @Override
    public List<ApiErrorcheckresult> toEntity(List<ApiErrorcheckresultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiErrorcheckresult> list = new ArrayList<ApiErrorcheckresult>( dtoList.size() );
        for ( ApiErrorcheckresultDto apiErrorcheckresultDto : dtoList ) {
            list.add( toEntity( apiErrorcheckresultDto ) );
        }

        return list;
    }
}
