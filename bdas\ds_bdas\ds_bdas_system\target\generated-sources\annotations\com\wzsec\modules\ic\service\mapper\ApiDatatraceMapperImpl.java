package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.ApiDatatrace;
import com.wzsec.modules.ic.service.dto.ApiDatatraceDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:30+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ApiDatatraceMapperImpl implements ApiDatatraceMapper {

    @Override
    public ApiDatatrace toEntity(ApiDatatraceDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiDatatrace apiDatatrace = new ApiDatatrace();

        apiDatatrace.setId( dto.getId() );
        apiDatatrace.setTraceablecontent( dto.getTraceablecontent() );
        apiDatatrace.setDatadescription( dto.getDatadescription() );
        apiDatatrace.setLocatdiscovery( dto.getLocatdiscovery() );
        apiDatatrace.setLocattime( dto.getLocattime() );
        apiDatatrace.setFilename( dto.getFilename() );
        apiDatatrace.setFilesize( dto.getFilesize() );
        apiDatatrace.setFilesuffix( dto.getFilesuffix() );
        apiDatatrace.setDataseparator( dto.getDataseparator() );
        apiDatatrace.setIssensitive( dto.getIssensitive() );
        apiDatatrace.setIsleakage( dto.getIsleakage() );
        apiDatatrace.setDataprovider( dto.getDataprovider() );
        apiDatatrace.setDatause( dto.getDatause() );
        apiDatatrace.setState( dto.getState() );
        apiDatatrace.setOperationuser( dto.getOperationuser() );
        apiDatatrace.setOperationtime( dto.getOperationtime() );
        apiDatatrace.setSparefield1( dto.getSparefield1() );
        apiDatatrace.setSparefield2( dto.getSparefield2() );
        apiDatatrace.setSparefield3( dto.getSparefield3() );
        apiDatatrace.setSparefield4( dto.getSparefield4() );
        apiDatatrace.setResourceId( dto.getResourceId() );
        apiDatatrace.setWatermark( dto.getWatermark() );

        return apiDatatrace;
    }

    @Override
    public ApiDatatraceDto toDto(ApiDatatrace entity) {
        if ( entity == null ) {
            return null;
        }

        ApiDatatraceDto apiDatatraceDto = new ApiDatatraceDto();

        apiDatatraceDto.setId( entity.getId() );
        apiDatatraceDto.setTraceablecontent( entity.getTraceablecontent() );
        apiDatatraceDto.setDatadescription( entity.getDatadescription() );
        apiDatatraceDto.setLocatdiscovery( entity.getLocatdiscovery() );
        apiDatatraceDto.setLocattime( entity.getLocattime() );
        apiDatatraceDto.setFilename( entity.getFilename() );
        apiDatatraceDto.setFilesize( entity.getFilesize() );
        apiDatatraceDto.setFilesuffix( entity.getFilesuffix() );
        apiDatatraceDto.setDataseparator( entity.getDataseparator() );
        apiDatatraceDto.setIssensitive( entity.getIssensitive() );
        apiDatatraceDto.setIsleakage( entity.getIsleakage() );
        apiDatatraceDto.setDataprovider( entity.getDataprovider() );
        apiDatatraceDto.setDatause( entity.getDatause() );
        apiDatatraceDto.setState( entity.getState() );
        apiDatatraceDto.setOperationuser( entity.getOperationuser() );
        apiDatatraceDto.setOperationtime( entity.getOperationtime() );
        apiDatatraceDto.setSparefield1( entity.getSparefield1() );
        apiDatatraceDto.setSparefield2( entity.getSparefield2() );
        apiDatatraceDto.setSparefield3( entity.getSparefield3() );
        apiDatatraceDto.setSparefield4( entity.getSparefield4() );
        apiDatatraceDto.setResourceId( entity.getResourceId() );
        apiDatatraceDto.setWatermark( entity.getWatermark() );

        return apiDatatraceDto;
    }

    @Override
    public List<ApiDatatrace> toEntity(List<ApiDatatraceDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiDatatrace> list = new ArrayList<ApiDatatrace>( dtoList.size() );
        for ( ApiDatatraceDto apiDatatraceDto : dtoList ) {
            list.add( toEntity( apiDatatraceDto ) );
        }

        return list;
    }

    @Override
    public List<ApiDatatraceDto> toDto(List<ApiDatatrace> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiDatatraceDto> list = new ArrayList<ApiDatatraceDto>( entityList.size() );
        for ( ApiDatatrace apiDatatrace : entityList ) {
            list.add( toDto( apiDatatrace ) );
        }

        return list;
    }
}
