package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.ApiDatatrace;
import com.wzsec.modules.ic.service.dto.ApiDatatraceDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:31+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ApiDatatraceMapperImpl implements ApiDatatraceMapper {

    @Override
    public ApiDatatraceDto toDto(ApiDatatrace entity) {
        if ( entity == null ) {
            return null;
        }

        ApiDatatraceDto apiDatatraceDto = new ApiDatatraceDto();

        apiDatatraceDto.setDatadescription( entity.getDatadescription() );
        apiDatatraceDto.setDataprovider( entity.getDataprovider() );
        apiDatatraceDto.setDataseparator( entity.getDataseparator() );
        apiDatatraceDto.setDatause( entity.getDatause() );
        apiDatatraceDto.setFilename( entity.getFilename() );
        apiDatatraceDto.setFilesize( entity.getFilesize() );
        apiDatatraceDto.setFilesuffix( entity.getFilesuffix() );
        apiDatatraceDto.setId( entity.getId() );
        apiDatatraceDto.setIsleakage( entity.getIsleakage() );
        apiDatatraceDto.setIssensitive( entity.getIssensitive() );
        apiDatatraceDto.setLocatdiscovery( entity.getLocatdiscovery() );
        apiDatatraceDto.setLocattime( entity.getLocattime() );
        apiDatatraceDto.setOperationtime( entity.getOperationtime() );
        apiDatatraceDto.setOperationuser( entity.getOperationuser() );
        apiDatatraceDto.setResourceId( entity.getResourceId() );
        apiDatatraceDto.setSparefield1( entity.getSparefield1() );
        apiDatatraceDto.setSparefield2( entity.getSparefield2() );
        apiDatatraceDto.setSparefield3( entity.getSparefield3() );
        apiDatatraceDto.setSparefield4( entity.getSparefield4() );
        apiDatatraceDto.setState( entity.getState() );
        apiDatatraceDto.setTraceablecontent( entity.getTraceablecontent() );
        apiDatatraceDto.setWatermark( entity.getWatermark() );

        return apiDatatraceDto;
    }

    @Override
    public List<ApiDatatraceDto> toDto(List<ApiDatatrace> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiDatatraceDto> list = new ArrayList<ApiDatatraceDto>( entityList.size() );
        for ( ApiDatatrace apiDatatrace : entityList ) {
            list.add( toDto( apiDatatrace ) );
        }

        return list;
    }

    @Override
    public ApiDatatrace toEntity(ApiDatatraceDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiDatatrace apiDatatrace = new ApiDatatrace();

        apiDatatrace.setDatadescription( dto.getDatadescription() );
        apiDatatrace.setDataprovider( dto.getDataprovider() );
        apiDatatrace.setDataseparator( dto.getDataseparator() );
        apiDatatrace.setDatause( dto.getDatause() );
        apiDatatrace.setFilename( dto.getFilename() );
        apiDatatrace.setFilesize( dto.getFilesize() );
        apiDatatrace.setFilesuffix( dto.getFilesuffix() );
        apiDatatrace.setId( dto.getId() );
        apiDatatrace.setIsleakage( dto.getIsleakage() );
        apiDatatrace.setIssensitive( dto.getIssensitive() );
        apiDatatrace.setLocatdiscovery( dto.getLocatdiscovery() );
        apiDatatrace.setLocattime( dto.getLocattime() );
        apiDatatrace.setOperationtime( dto.getOperationtime() );
        apiDatatrace.setOperationuser( dto.getOperationuser() );
        apiDatatrace.setResourceId( dto.getResourceId() );
        apiDatatrace.setSparefield1( dto.getSparefield1() );
        apiDatatrace.setSparefield2( dto.getSparefield2() );
        apiDatatrace.setSparefield3( dto.getSparefield3() );
        apiDatatrace.setSparefield4( dto.getSparefield4() );
        apiDatatrace.setState( dto.getState() );
        apiDatatrace.setTraceablecontent( dto.getTraceablecontent() );
        apiDatatrace.setWatermark( dto.getWatermark() );

        return apiDatatrace;
    }

    @Override
    public List<ApiDatatrace> toEntity(List<ApiDatatraceDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiDatatrace> list = new ArrayList<ApiDatatrace>( dtoList.size() );
        for ( ApiDatatraceDto apiDatatraceDto : dtoList ) {
            list.add( toEntity( apiDatatraceDto ) );
        }

        return list;
    }
}
