package com.wzsec.modules.blackwhitelist.service.mapper;

import com.wzsec.modules.blackwhitelist.domain.Blackwhitelist;
import com.wzsec.modules.blackwhitelist.service.dto.BlackwhitelistDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:31+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class BlackwhitelistMapperImpl implements BlackwhitelistMapper {

    @Override
    public Blackwhitelist toEntity(BlackwhitelistDto dto) {
        if ( dto == null ) {
            return null;
        }

        Blackwhitelist blackwhitelist = new Blackwhitelist();

        blackwhitelist.setId( dto.getId() );
        blackwhitelist.setContent( dto.getContent() );
        blackwhitelist.setType( dto.getType() );
        blackwhitelist.setPurpose( dto.getPurpose() );
        blackwhitelist.setApply( dto.getApply() );
        blackwhitelist.setCreateuser( dto.getCreateuser() );
        blackwhitelist.setCreateTime( dto.getCreateTime() );
        blackwhitelist.setUpdateuser( dto.getUpdateuser() );
        blackwhitelist.setUpdateTime( dto.getUpdateTime() );
        blackwhitelist.setSparefield1( dto.getSparefield1() );
        blackwhitelist.setSparefield2( dto.getSparefield2() );
        blackwhitelist.setSparefield3( dto.getSparefield3() );
        blackwhitelist.setSparefield4( dto.getSparefield4() );
        blackwhitelist.setSparefield5( dto.getSparefield5() );
        blackwhitelist.setState( dto.getState() );
        blackwhitelist.setNote( dto.getNote() );

        return blackwhitelist;
    }

    @Override
    public BlackwhitelistDto toDto(Blackwhitelist entity) {
        if ( entity == null ) {
            return null;
        }

        BlackwhitelistDto blackwhitelistDto = new BlackwhitelistDto();

        blackwhitelistDto.setId( entity.getId() );
        blackwhitelistDto.setContent( entity.getContent() );
        blackwhitelistDto.setType( entity.getType() );
        blackwhitelistDto.setPurpose( entity.getPurpose() );
        blackwhitelistDto.setApply( entity.getApply() );
        blackwhitelistDto.setCreateuser( entity.getCreateuser() );
        blackwhitelistDto.setCreateTime( entity.getCreateTime() );
        blackwhitelistDto.setUpdateuser( entity.getUpdateuser() );
        blackwhitelistDto.setUpdateTime( entity.getUpdateTime() );
        blackwhitelistDto.setSparefield1( entity.getSparefield1() );
        blackwhitelistDto.setSparefield2( entity.getSparefield2() );
        blackwhitelistDto.setSparefield3( entity.getSparefield3() );
        blackwhitelistDto.setSparefield4( entity.getSparefield4() );
        blackwhitelistDto.setSparefield5( entity.getSparefield5() );
        blackwhitelistDto.setState( entity.getState() );
        blackwhitelistDto.setNote( entity.getNote() );

        return blackwhitelistDto;
    }

    @Override
    public List<Blackwhitelist> toEntity(List<BlackwhitelistDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Blackwhitelist> list = new ArrayList<Blackwhitelist>( dtoList.size() );
        for ( BlackwhitelistDto blackwhitelistDto : dtoList ) {
            list.add( toEntity( blackwhitelistDto ) );
        }

        return list;
    }

    @Override
    public List<BlackwhitelistDto> toDto(List<Blackwhitelist> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<BlackwhitelistDto> list = new ArrayList<BlackwhitelistDto>( entityList.size() );
        for ( Blackwhitelist blackwhitelist : entityList ) {
            list.add( toDto( blackwhitelist ) );
        }

        return list;
    }
}
