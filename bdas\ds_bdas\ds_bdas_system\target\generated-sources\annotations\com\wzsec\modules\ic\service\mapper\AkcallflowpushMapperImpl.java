package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.Akcallflowpush;
import com.wzsec.modules.ic.service.dto.AkcallflowpushDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:28+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AkcallflowpushMapperImpl implements AkcallflowpushMapper {

    @Override
    public AkcallflowpushDto toDto(Akcallflowpush entity) {
        if ( entity == null ) {
            return null;
        }

        AkcallflowpushDto akcallflowpushDto = new AkcallflowpushDto();

        akcallflowpushDto.setApplyorgname( entity.getApplyorgname() );
        akcallflowpushDto.setCreatetime( entity.getCreatetime() );
        akcallflowpushDto.setId( entity.getId() );
        akcallflowpushDto.setL1count( entity.getL1count() );
        akcallflowpushDto.setL2count( entity.getL2count() );
        akcallflowpushDto.setL3count( entity.getL3count() );
        akcallflowpushDto.setL4count( entity.getL4count() );
        akcallflowpushDto.setSparefield1( entity.getSparefield1() );
        akcallflowpushDto.setSparefield2( entity.getSparefield2() );
        akcallflowpushDto.setSparefield3( entity.getSparefield3() );
        akcallflowpushDto.setSparefield4( entity.getSparefield4() );
        akcallflowpushDto.setSparefield5( entity.getSparefield5() );

        return akcallflowpushDto;
    }

    @Override
    public List<AkcallflowpushDto> toDto(List<Akcallflowpush> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<AkcallflowpushDto> list = new ArrayList<AkcallflowpushDto>( entityList.size() );
        for ( Akcallflowpush akcallflowpush : entityList ) {
            list.add( toDto( akcallflowpush ) );
        }

        return list;
    }

    @Override
    public Akcallflowpush toEntity(AkcallflowpushDto dto) {
        if ( dto == null ) {
            return null;
        }

        Akcallflowpush akcallflowpush = new Akcallflowpush();

        akcallflowpush.setApplyorgname( dto.getApplyorgname() );
        akcallflowpush.setCreatetime( dto.getCreatetime() );
        akcallflowpush.setId( dto.getId() );
        akcallflowpush.setL1count( dto.getL1count() );
        akcallflowpush.setL2count( dto.getL2count() );
        akcallflowpush.setL3count( dto.getL3count() );
        akcallflowpush.setL4count( dto.getL4count() );
        akcallflowpush.setSparefield1( dto.getSparefield1() );
        akcallflowpush.setSparefield2( dto.getSparefield2() );
        akcallflowpush.setSparefield3( dto.getSparefield3() );
        akcallflowpush.setSparefield4( dto.getSparefield4() );
        akcallflowpush.setSparefield5( dto.getSparefield5() );

        return akcallflowpush;
    }

    @Override
    public List<Akcallflowpush> toEntity(List<AkcallflowpushDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Akcallflowpush> list = new ArrayList<Akcallflowpush>( dtoList.size() );
        for ( AkcallflowpushDto akcallflowpushDto : dtoList ) {
            list.add( toEntity( akcallflowpushDto ) );
        }

        return list;
    }
}
