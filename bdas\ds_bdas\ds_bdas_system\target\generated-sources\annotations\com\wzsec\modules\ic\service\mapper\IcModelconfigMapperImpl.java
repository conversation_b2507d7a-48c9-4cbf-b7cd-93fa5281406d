package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcModelconfig;
import com.wzsec.modules.ic.service.dto.IcModelconfigDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:26:45+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class IcModelconfigMapperImpl implements IcModelconfigMapper {

    @Override
    public IcModelconfigDto toDto(IcModelconfig entity) {
        if ( entity == null ) {
            return null;
        }

        IcModelconfigDto icModelconfigDto = new IcModelconfigDto();

        icModelconfigDto.setApicode( entity.getApicode() );
        icModelconfigDto.setBegindate( entity.getBegindate() );
        icModelconfigDto.setCreatetime( entity.getCreatetime() );
        icModelconfigDto.setCreateuser( entity.getCreateuser() );
        icModelconfigDto.setEnddate( entity.getEnddate() );
        icModelconfigDto.setForecastfieldname( entity.getForecastfieldname() );
        icModelconfigDto.setForecastfieldvalues( entity.getForecastfieldvalues() );
        icModelconfigDto.setForecastmethod( entity.getForecastmethod() );
        icModelconfigDto.setId( entity.getId() );
        icModelconfigDto.setMaximumoutput( entity.getMaximumoutput() );
        icModelconfigDto.setSparefield1( entity.getSparefield1() );
        icModelconfigDto.setSparefield2( entity.getSparefield2() );
        icModelconfigDto.setSparefield3( entity.getSparefield3() );
        icModelconfigDto.setSparefield4( entity.getSparefield4() );
        icModelconfigDto.setSparefield5( entity.getSparefield5() );
        icModelconfigDto.setSparefield6( entity.getSparefield6() );
        icModelconfigDto.setSparefield7( entity.getSparefield7() );
        icModelconfigDto.setSparefield8( entity.getSparefield8() );
        icModelconfigDto.setStatus( entity.getStatus() );
        icModelconfigDto.setSuggestbegindate( entity.getSuggestbegindate() );
        icModelconfigDto.setSuggestenddate( entity.getSuggestenddate() );
        icModelconfigDto.setUpdatetime( entity.getUpdatetime() );
        icModelconfigDto.setUpdateuser( entity.getUpdateuser() );
        icModelconfigDto.setWorktime( entity.getWorktime() );

        return icModelconfigDto;
    }

    @Override
    public List<IcModelconfigDto> toDto(List<IcModelconfig> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcModelconfigDto> list = new ArrayList<IcModelconfigDto>( entityList.size() );
        for ( IcModelconfig icModelconfig : entityList ) {
            list.add( toDto( icModelconfig ) );
        }

        return list;
    }

    @Override
    public IcModelconfig toEntity(IcModelconfigDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcModelconfig icModelconfig = new IcModelconfig();

        icModelconfig.setApicode( dto.getApicode() );
        icModelconfig.setBegindate( dto.getBegindate() );
        icModelconfig.setCreatetime( dto.getCreatetime() );
        icModelconfig.setCreateuser( dto.getCreateuser() );
        icModelconfig.setEnddate( dto.getEnddate() );
        icModelconfig.setForecastfieldname( dto.getForecastfieldname() );
        icModelconfig.setForecastfieldvalues( dto.getForecastfieldvalues() );
        icModelconfig.setForecastmethod( dto.getForecastmethod() );
        icModelconfig.setId( dto.getId() );
        icModelconfig.setMaximumoutput( dto.getMaximumoutput() );
        icModelconfig.setSparefield1( dto.getSparefield1() );
        icModelconfig.setSparefield2( dto.getSparefield2() );
        icModelconfig.setSparefield3( dto.getSparefield3() );
        icModelconfig.setSparefield4( dto.getSparefield4() );
        icModelconfig.setSparefield5( dto.getSparefield5() );
        icModelconfig.setSparefield6( dto.getSparefield6() );
        icModelconfig.setSparefield7( dto.getSparefield7() );
        icModelconfig.setSparefield8( dto.getSparefield8() );
        icModelconfig.setStatus( dto.getStatus() );
        icModelconfig.setSuggestbegindate( dto.getSuggestbegindate() );
        icModelconfig.setSuggestenddate( dto.getSuggestenddate() );
        icModelconfig.setUpdatetime( dto.getUpdatetime() );
        icModelconfig.setUpdateuser( dto.getUpdateuser() );
        icModelconfig.setWorktime( dto.getWorktime() );

        return icModelconfig;
    }

    @Override
    public List<IcModelconfig> toEntity(List<IcModelconfigDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcModelconfig> list = new ArrayList<IcModelconfig>( dtoList.size() );
        for ( IcModelconfigDto icModelconfigDto : dtoList ) {
            list.add( toEntity( icModelconfigDto ) );
        }

        return list;
    }
}
