package com.wzsec.modules.apiproxy.service.mapper;

import com.wzsec.modules.apiproxy.domain.ApiproxyRegulatory;
import com.wzsec.modules.apiproxy.service.dto.ApiproxyRegulatoryDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:30+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ApiproxyRegulatoryMapperImpl implements ApiproxyRegulatoryMapper {

    @Override
    public ApiproxyRegulatory toEntity(ApiproxyRegulatoryDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiproxyRegulatory apiproxyRegulatory = new ApiproxyRegulatory();

        apiproxyRegulatory.setId( dto.getId() );
        apiproxyRegulatory.setRegistername( dto.getRegistername() );
        apiproxyRegulatory.setServerhost( dto.getServerhost() );
        apiproxyRegulatory.setServerport( dto.getServerport() );
        apiproxyRegulatory.setPredicates( dto.getPredicates() );
        apiproxyRegulatory.setHandle( dto.getHandle() );
        apiproxyRegulatory.setStatus( dto.getStatus() );
        apiproxyRegulatory.setExecutionnum( dto.getExecutionnum() );
        apiproxyRegulatory.setNote( dto.getNote() );
        apiproxyRegulatory.setCreateuser( dto.getCreateuser() );
        apiproxyRegulatory.setCreatetime( dto.getCreatetime() );
        apiproxyRegulatory.setUpdateuser( dto.getUpdateuser() );
        apiproxyRegulatory.setUpdatetime( dto.getUpdatetime() );
        apiproxyRegulatory.setSparefield1( dto.getSparefield1() );
        apiproxyRegulatory.setSparefield2( dto.getSparefield2() );
        apiproxyRegulatory.setSparefield3( dto.getSparefield3() );
        apiproxyRegulatory.setSparefield4( dto.getSparefield4() );
        apiproxyRegulatory.setSparefield5( dto.getSparefield5() );

        return apiproxyRegulatory;
    }

    @Override
    public ApiproxyRegulatoryDto toDto(ApiproxyRegulatory entity) {
        if ( entity == null ) {
            return null;
        }

        ApiproxyRegulatoryDto apiproxyRegulatoryDto = new ApiproxyRegulatoryDto();

        apiproxyRegulatoryDto.setId( entity.getId() );
        apiproxyRegulatoryDto.setRegistername( entity.getRegistername() );
        apiproxyRegulatoryDto.setServerhost( entity.getServerhost() );
        apiproxyRegulatoryDto.setServerport( entity.getServerport() );
        apiproxyRegulatoryDto.setPredicates( entity.getPredicates() );
        apiproxyRegulatoryDto.setHandle( entity.getHandle() );
        apiproxyRegulatoryDto.setStatus( entity.getStatus() );
        apiproxyRegulatoryDto.setExecutionnum( entity.getExecutionnum() );
        apiproxyRegulatoryDto.setNote( entity.getNote() );
        apiproxyRegulatoryDto.setCreateuser( entity.getCreateuser() );
        apiproxyRegulatoryDto.setCreatetime( entity.getCreatetime() );
        apiproxyRegulatoryDto.setUpdateuser( entity.getUpdateuser() );
        apiproxyRegulatoryDto.setUpdatetime( entity.getUpdatetime() );
        apiproxyRegulatoryDto.setSparefield1( entity.getSparefield1() );
        apiproxyRegulatoryDto.setSparefield2( entity.getSparefield2() );
        apiproxyRegulatoryDto.setSparefield3( entity.getSparefield3() );
        apiproxyRegulatoryDto.setSparefield4( entity.getSparefield4() );
        apiproxyRegulatoryDto.setSparefield5( entity.getSparefield5() );

        return apiproxyRegulatoryDto;
    }

    @Override
    public List<ApiproxyRegulatory> toEntity(List<ApiproxyRegulatoryDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiproxyRegulatory> list = new ArrayList<ApiproxyRegulatory>( dtoList.size() );
        for ( ApiproxyRegulatoryDto apiproxyRegulatoryDto : dtoList ) {
            list.add( toEntity( apiproxyRegulatoryDto ) );
        }

        return list;
    }

    @Override
    public List<ApiproxyRegulatoryDto> toDto(List<ApiproxyRegulatory> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiproxyRegulatoryDto> list = new ArrayList<ApiproxyRegulatoryDto>( entityList.size() );
        for ( ApiproxyRegulatory apiproxyRegulatory : entityList ) {
            list.add( toDto( apiproxyRegulatory ) );
        }

        return list;
    }
}
