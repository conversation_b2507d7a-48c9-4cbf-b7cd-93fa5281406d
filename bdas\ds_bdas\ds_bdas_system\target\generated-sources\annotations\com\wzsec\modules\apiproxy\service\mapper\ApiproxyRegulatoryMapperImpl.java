package com.wzsec.modules.apiproxy.service.mapper;

import com.wzsec.modules.apiproxy.domain.ApiproxyRegulatory;
import com.wzsec.modules.apiproxy.service.dto.ApiproxyRegulatoryDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:26:46+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ApiproxyRegulatoryMapperImpl implements ApiproxyRegulatoryMapper {

    @Override
    public ApiproxyRegulatoryDto toDto(ApiproxyRegulatory entity) {
        if ( entity == null ) {
            return null;
        }

        ApiproxyRegulatoryDto apiproxyRegulatoryDto = new ApiproxyRegulatoryDto();

        apiproxyRegulatoryDto.setCreatetime( entity.getCreatetime() );
        apiproxyRegulatoryDto.setCreateuser( entity.getCreateuser() );
        apiproxyRegulatoryDto.setExecutionnum( entity.getExecutionnum() );
        apiproxyRegulatoryDto.setHandle( entity.getHandle() );
        apiproxyRegulatoryDto.setId( entity.getId() );
        apiproxyRegulatoryDto.setNote( entity.getNote() );
        apiproxyRegulatoryDto.setPredicates( entity.getPredicates() );
        apiproxyRegulatoryDto.setRegistername( entity.getRegistername() );
        apiproxyRegulatoryDto.setServerhost( entity.getServerhost() );
        apiproxyRegulatoryDto.setServerport( entity.getServerport() );
        apiproxyRegulatoryDto.setSparefield1( entity.getSparefield1() );
        apiproxyRegulatoryDto.setSparefield2( entity.getSparefield2() );
        apiproxyRegulatoryDto.setSparefield3( entity.getSparefield3() );
        apiproxyRegulatoryDto.setSparefield4( entity.getSparefield4() );
        apiproxyRegulatoryDto.setSparefield5( entity.getSparefield5() );
        apiproxyRegulatoryDto.setStatus( entity.getStatus() );
        apiproxyRegulatoryDto.setUpdatetime( entity.getUpdatetime() );
        apiproxyRegulatoryDto.setUpdateuser( entity.getUpdateuser() );

        return apiproxyRegulatoryDto;
    }

    @Override
    public List<ApiproxyRegulatoryDto> toDto(List<ApiproxyRegulatory> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiproxyRegulatoryDto> list = new ArrayList<ApiproxyRegulatoryDto>( entityList.size() );
        for ( ApiproxyRegulatory apiproxyRegulatory : entityList ) {
            list.add( toDto( apiproxyRegulatory ) );
        }

        return list;
    }

    @Override
    public ApiproxyRegulatory toEntity(ApiproxyRegulatoryDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiproxyRegulatory apiproxyRegulatory = new ApiproxyRegulatory();

        apiproxyRegulatory.setCreatetime( dto.getCreatetime() );
        apiproxyRegulatory.setCreateuser( dto.getCreateuser() );
        apiproxyRegulatory.setExecutionnum( dto.getExecutionnum() );
        apiproxyRegulatory.setHandle( dto.getHandle() );
        apiproxyRegulatory.setId( dto.getId() );
        apiproxyRegulatory.setNote( dto.getNote() );
        apiproxyRegulatory.setPredicates( dto.getPredicates() );
        apiproxyRegulatory.setRegistername( dto.getRegistername() );
        apiproxyRegulatory.setServerhost( dto.getServerhost() );
        apiproxyRegulatory.setServerport( dto.getServerport() );
        apiproxyRegulatory.setSparefield1( dto.getSparefield1() );
        apiproxyRegulatory.setSparefield2( dto.getSparefield2() );
        apiproxyRegulatory.setSparefield3( dto.getSparefield3() );
        apiproxyRegulatory.setSparefield4( dto.getSparefield4() );
        apiproxyRegulatory.setSparefield5( dto.getSparefield5() );
        apiproxyRegulatory.setStatus( dto.getStatus() );
        apiproxyRegulatory.setUpdatetime( dto.getUpdatetime() );
        apiproxyRegulatory.setUpdateuser( dto.getUpdateuser() );

        return apiproxyRegulatory;
    }

    @Override
    public List<ApiproxyRegulatory> toEntity(List<ApiproxyRegulatoryDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiproxyRegulatory> list = new ArrayList<ApiproxyRegulatory>( dtoList.size() );
        for ( ApiproxyRegulatoryDto apiproxyRegulatoryDto : dtoList ) {
            list.add( toEntity( apiproxyRegulatoryDto ) );
        }

        return list;
    }
}
