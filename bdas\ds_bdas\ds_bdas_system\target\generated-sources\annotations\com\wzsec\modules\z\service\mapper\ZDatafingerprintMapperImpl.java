package com.wzsec.modules.z.service.mapper;

import com.wzsec.modules.z.domain.ZDatafingerprint;
import com.wzsec.modules.z.service.dto.ZDatafingerprintDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:26:47+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ZDatafingerprintMapperImpl implements ZDatafingerprintMapper {

    @Override
    public ZDatafingerprintDto toDto(ZDatafingerprint entity) {
        if ( entity == null ) {
            return null;
        }

        ZDatafingerprintDto zDatafingerprintDto = new ZDatafingerprintDto();

        zDatafingerprintDto.setDatafingerprint( entity.getDatafingerprint() );
        zDatafingerprintDto.setDatatype( entity.getDatatype() );
        zDatafingerprintDto.setId( entity.getId() );

        return zDatafingerprintDto;
    }

    @Override
    public List<ZDatafingerprintDto> toDto(List<ZDatafingerprint> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ZDatafingerprintDto> list = new ArrayList<ZDatafingerprintDto>( entityList.size() );
        for ( ZDatafingerprint zDatafingerprint : entityList ) {
            list.add( toDto( zDatafingerprint ) );
        }

        return list;
    }

    @Override
    public ZDatafingerprint toEntity(ZDatafingerprintDto dto) {
        if ( dto == null ) {
            return null;
        }

        ZDatafingerprint zDatafingerprint = new ZDatafingerprint();

        zDatafingerprint.setDatafingerprint( dto.getDatafingerprint() );
        zDatafingerprint.setDatatype( dto.getDatatype() );
        zDatafingerprint.setId( dto.getId() );

        return zDatafingerprint;
    }

    @Override
    public List<ZDatafingerprint> toEntity(List<ZDatafingerprintDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ZDatafingerprint> list = new ArrayList<ZDatafingerprint>( dtoList.size() );
        for ( ZDatafingerprintDto zDatafingerprintDto : dtoList ) {
            list.add( toEntity( zDatafingerprintDto ) );
        }

        return list;
    }
}
