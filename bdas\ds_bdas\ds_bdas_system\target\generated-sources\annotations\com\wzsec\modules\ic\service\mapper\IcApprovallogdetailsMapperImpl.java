package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcApprovallogdetails;
import com.wzsec.modules.ic.service.dto.IcApprovallogdetailsDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:31+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class IcApprovallogdetailsMapperImpl implements IcApprovallogdetailsMapper {

    @Override
    public IcApprovallogdetails toEntity(IcApprovallogdetailsDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcApprovallogdetails icApprovallogdetails = new IcApprovallogdetails();

        icApprovallogdetails.setId( dto.getId() );
        icApprovallogdetails.setApprovallogid( dto.getApprovallogid() );
        icApprovallogdetails.setAk( dto.getAk() );
        icApprovallogdetails.setShareapplyid( dto.getShareapplyid() );
        icApprovallogdetails.setNamecn( dto.getNamecn() );
        icApprovallogdetails.setNameen( dto.getNameen() );
        icApprovallogdetails.setInfoname( dto.getInfoname() );
        icApprovallogdetails.setInfoid( dto.getInfoid() );
        icApprovallogdetails.setSharetype( dto.getSharetype() );
        icApprovallogdetails.setOpen( dto.getOpen() );
        icApprovallogdetails.setClassification( dto.getClassification() );
        icApprovallogdetails.setCreatetime( dto.getCreatetime() );
        icApprovallogdetails.setSparefield1( dto.getSparefield1() );
        icApprovallogdetails.setSparefield2( dto.getSparefield2() );
        icApprovallogdetails.setSparefield3( dto.getSparefield3() );
        icApprovallogdetails.setSparefield4( dto.getSparefield4() );
        icApprovallogdetails.setSparefield5( dto.getSparefield5() );

        return icApprovallogdetails;
    }

    @Override
    public IcApprovallogdetailsDto toDto(IcApprovallogdetails entity) {
        if ( entity == null ) {
            return null;
        }

        IcApprovallogdetailsDto icApprovallogdetailsDto = new IcApprovallogdetailsDto();

        icApprovallogdetailsDto.setId( entity.getId() );
        icApprovallogdetailsDto.setApprovallogid( entity.getApprovallogid() );
        icApprovallogdetailsDto.setAk( entity.getAk() );
        icApprovallogdetailsDto.setShareapplyid( entity.getShareapplyid() );
        icApprovallogdetailsDto.setNamecn( entity.getNamecn() );
        icApprovallogdetailsDto.setNameen( entity.getNameen() );
        icApprovallogdetailsDto.setInfoname( entity.getInfoname() );
        icApprovallogdetailsDto.setInfoid( entity.getInfoid() );
        icApprovallogdetailsDto.setSharetype( entity.getSharetype() );
        icApprovallogdetailsDto.setOpen( entity.getOpen() );
        icApprovallogdetailsDto.setClassification( entity.getClassification() );
        icApprovallogdetailsDto.setCreatetime( entity.getCreatetime() );
        icApprovallogdetailsDto.setSparefield1( entity.getSparefield1() );
        icApprovallogdetailsDto.setSparefield2( entity.getSparefield2() );
        icApprovallogdetailsDto.setSparefield3( entity.getSparefield3() );
        icApprovallogdetailsDto.setSparefield4( entity.getSparefield4() );
        icApprovallogdetailsDto.setSparefield5( entity.getSparefield5() );

        return icApprovallogdetailsDto;
    }

    @Override
    public List<IcApprovallogdetails> toEntity(List<IcApprovallogdetailsDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcApprovallogdetails> list = new ArrayList<IcApprovallogdetails>( dtoList.size() );
        for ( IcApprovallogdetailsDto icApprovallogdetailsDto : dtoList ) {
            list.add( toEntity( icApprovallogdetailsDto ) );
        }

        return list;
    }

    @Override
    public List<IcApprovallogdetailsDto> toDto(List<IcApprovallogdetails> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcApprovallogdetailsDto> list = new ArrayList<IcApprovallogdetailsDto>( entityList.size() );
        for ( IcApprovallogdetails icApprovallogdetails : entityList ) {
            list.add( toDto( icApprovallogdetails ) );
        }

        return list;
    }
}
