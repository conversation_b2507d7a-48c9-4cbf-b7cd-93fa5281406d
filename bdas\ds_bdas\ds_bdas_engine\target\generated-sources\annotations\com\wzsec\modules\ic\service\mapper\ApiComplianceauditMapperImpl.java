package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.ApiComplianceaudit;
import com.wzsec.modules.ic.service.dto.ApiComplianceauditDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:27:08+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ApiComplianceauditMapperImpl implements ApiComplianceauditMapper {

    @Override
    public ApiComplianceauditDto toDto(ApiComplianceaudit entity) {
        if ( entity == null ) {
            return null;
        }

        ApiComplianceauditDto apiComplianceauditDto = new ApiComplianceauditDto();

        apiComplianceauditDto.setActualinput( entity.getActualinput() );
        apiComplianceauditDto.setActualoutput( entity.getActualoutput() );
        apiComplianceauditDto.setApicode( entity.getApicode() );
        apiComplianceauditDto.setApiname( entity.getApiname() );
        apiComplianceauditDto.setCompliance( entity.getCompliance() );
        apiComplianceauditDto.setId( entity.getId() );
        apiComplianceauditDto.setInputparams( entity.getInputparams() );
        apiComplianceauditDto.setOutputparams( entity.getOutputparams() );
        apiComplianceauditDto.setProtocol( entity.getProtocol() );
        apiComplianceauditDto.setRisk( entity.getRisk() );
        apiComplianceauditDto.setSparefield1( entity.getSparefield1() );
        apiComplianceauditDto.setSparefield2( entity.getSparefield2() );
        apiComplianceauditDto.setSparefield3( entity.getSparefield3() );
        apiComplianceauditDto.setSparefield4( entity.getSparefield4() );
        apiComplianceauditDto.setStatus( entity.getStatus() );
        apiComplianceauditDto.setTime( entity.getTime() );

        return apiComplianceauditDto;
    }

    @Override
    public List<ApiComplianceauditDto> toDto(List<ApiComplianceaudit> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiComplianceauditDto> list = new ArrayList<ApiComplianceauditDto>( entityList.size() );
        for ( ApiComplianceaudit apiComplianceaudit : entityList ) {
            list.add( toDto( apiComplianceaudit ) );
        }

        return list;
    }

    @Override
    public ApiComplianceaudit toEntity(ApiComplianceauditDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiComplianceaudit apiComplianceaudit = new ApiComplianceaudit();

        apiComplianceaudit.setActualinput( dto.getActualinput() );
        apiComplianceaudit.setActualoutput( dto.getActualoutput() );
        apiComplianceaudit.setApicode( dto.getApicode() );
        apiComplianceaudit.setApiname( dto.getApiname() );
        apiComplianceaudit.setCompliance( dto.getCompliance() );
        apiComplianceaudit.setId( dto.getId() );
        apiComplianceaudit.setInputparams( dto.getInputparams() );
        apiComplianceaudit.setOutputparams( dto.getOutputparams() );
        apiComplianceaudit.setProtocol( dto.getProtocol() );
        apiComplianceaudit.setRisk( dto.getRisk() );
        apiComplianceaudit.setSparefield1( dto.getSparefield1() );
        apiComplianceaudit.setSparefield2( dto.getSparefield2() );
        apiComplianceaudit.setSparefield3( dto.getSparefield3() );
        apiComplianceaudit.setSparefield4( dto.getSparefield4() );
        apiComplianceaudit.setStatus( dto.getStatus() );
        apiComplianceaudit.setTime( dto.getTime() );

        return apiComplianceaudit;
    }

    @Override
    public List<ApiComplianceaudit> toEntity(List<ApiComplianceauditDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiComplianceaudit> list = new ArrayList<ApiComplianceaudit>( dtoList.size() );
        for ( ApiComplianceauditDto apiComplianceauditDto : dtoList ) {
            list.add( toEntity( apiComplianceauditDto ) );
        }

        return list;
    }
}
