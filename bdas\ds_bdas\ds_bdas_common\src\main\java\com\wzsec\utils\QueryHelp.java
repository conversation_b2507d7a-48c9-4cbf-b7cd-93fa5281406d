package com.wzsec.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import com.wzsec.annotation.Query;
import javax.persistence.criteria.*;
import java.lang.reflect.Field;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2019-6-4 14:59:48
 */
@Slf4j
@SuppressWarnings({"unchecked","all"})
public class QueryHelp {

    public static <R, Q> Predicate getPredicate(Root<R> root, Q query, CriteriaBuilder cb) {
        List<Predicate> list = new ArrayList<>();

        if(query == null){
            return cb.and(list.toArray(new Predicate[0]));
        }
        try {
            List<Field> fields = getAllFields(query.getClass(), new ArrayList<>());
            for (Field field : fields) {
                boolean accessible = field.isAccessible();
                field.setAccessible(true);
                Query q = field.getAnnotation(Query.class);
                if (q != null) {
                    String propName = q.propName();
                    String joinName = q.joinName();
                    String blurry = q.blurry();
                    String attributeName = isBlank(propName) ? field.getName() : propName;
                    Class<?> fieldType = field.getType();
                    Object val = field.get(query);
                    if (ObjectUtil.isNull(val) || "".equals(val)) {
                        continue;
                    }
                    Join join = null;
                    // 模糊多字段
                    if (ObjectUtil.isNotEmpty(blurry)) {
                        String[] blurrys = blurry.split(",");
                        List<Predicate> orPredicate = new ArrayList<>();
                        for (String s : blurrys) {
                            orPredicate.add(cb.like(root.get(s)
                                    .as(String.class), "%" + val.toString() + "%"));
                        }
                        Predicate[] p = new Predicate[orPredicate.size()];
                        list.add(cb.or(orPredicate.toArray(p)));
                        continue;
                    }
                    if (ObjectUtil.isNotEmpty(joinName)) {
                        String[] joinNames = joinName.split(">");
                        for (String name : joinNames) {
                            switch (q.join()) {
                                case LEFT:
                                    if(ObjectUtil.isNotNull(join)){
                                        join = join.join(name, JoinType.LEFT);
                                    } else {
                                        join = root.join(name, JoinType.LEFT);
                                    }
                                    break;
                                case RIGHT:
                                    if(ObjectUtil.isNotNull(join)){
                                        join = join.join(name, JoinType.RIGHT);
                                    } else {
                                        join = root.join(name, JoinType.RIGHT);
                                    }
                                    break;
                                default: break;
                            }
                        }
                    }
                    switch (q.type()) {
                        case EQUAL:
                            list.add(cb.equal(getExpression(attributeName,join,root)
                                    .as((Class<? extends Comparable>) fieldType),val));
                            break;
                        case GREATER_THAN:
                            list.add(cb.greaterThanOrEqualTo(getExpression(attributeName,join,root)
                                    .as((Class<? extends Comparable>) fieldType), (Comparable) val));
                            break;
                        case LESS_THAN:
                            list.add(cb.lessThanOrEqualTo(getExpression(attributeName,join,root)
                                    .as((Class<? extends Comparable>) fieldType), (Comparable) val));
                            break;
                        case LESS_THAN_NQ:
                            list.add(cb.lessThan(getExpression(attributeName,join,root)
                                    .as((Class<? extends Comparable>) fieldType), (Comparable) val));
                            break;
                        case INNER_LIKE:
                            list.add(cb.like(getExpression(attributeName,join,root)
                                    .as(String.class), "%" + val.toString() + "%"));
                            break;
                        case LEFT_LIKE:
                            list.add(cb.like(getExpression(attributeName,join,root)
                                    .as(String.class), "%" + val.toString()));
                            break;
                        case RIGHT_LIKE:
                            list.add(cb.like(getExpression(attributeName,join,root)
                                    .as(String.class), val.toString() + "%"));
                            break;
                        case IN:
                            if (val instanceof String) {
                                // 处理String类型，支持逗号分隔的多个值
                                String strVal = ((String) val).trim();
                                if (strVal.isEmpty()) {
                                    // 空字符串，跳过
                                    break;
                                }
                                if (strVal.contains(",")) {
                                    // 多个值，按逗号分隔
                                    String[] values = strVal.split(",");
                                    List<String> valueList = new ArrayList<>();
                                    for (String value : values) {
                                        String trimmed = value.trim();
                                        if (!trimmed.isEmpty()) {
                                            valueList.add(trimmed);
                                        }
                                    }
                                    if (!valueList.isEmpty()) {
                                        list.add(getExpression(attributeName,join,root).in(valueList));
                                    }
                                } else {
                                    // 单个值，直接使用EQUAL
                                    list.add(cb.equal(getExpression(attributeName,join,root), strVal));
                                }
                            } else if (val instanceof Collection) {
                                // 处理Collection类型（List、Set等）- 保持原有兼容性
                                Collection<?> collection = (Collection<?>) val;
                                if (CollUtil.isNotEmpty(collection)) {
                                    // 过滤掉空值和空字符串
                                    List<Object> filteredList = new ArrayList<>();
                                    for (Object item : collection) {
                                        if (item != null && !item.toString().trim().isEmpty()) {
                                            filteredList.add(item);
                                        }
                                    }
                                    if (!filteredList.isEmpty()) {
                                        list.add(getExpression(attributeName,join,root).in(filteredList));
                                    }
                                }
                            } else {
                                // 其他类型，使用EQUAL
                                list.add(cb.equal(getExpression(attributeName,join,root), val));
                            }
                            break;
                        case NOT_EQUAL:
                            list.add(cb.notEqual(getExpression(attributeName,join,root), val));
                            break;
                        case NOT_NULL:
                            list.add(cb.isNotNull(getExpression(attributeName,join,root)));
                            break;
                        case BETWEEN:
                            List<Object> between = new ArrayList<>((List<Object>)val);
                            list.add(cb.between(getExpression(attributeName, join, root).as((Class<? extends Comparable>) between.get(0).getClass()),
                                    (Comparable) between.get(0), (Comparable) between.get(1)));
                            break;
                        case NOT_EQUAL_INNER_LIKE:
                            if(Const.WPRESULT_TASK_TABLE_RESULTTYPE_NOTSUPPORT_LABLE.equals(val.toString())){
                                list.add(cb.notEqual(getExpression(attributeName,join,root), val));
                            }else{
                                list.add(cb.notEqual(getExpression(attributeName,join,root), Const.WPRESULT_TASK_TABLE_RESULTTYPE_NOTSUPPORT_LABLE));
                                list.add(cb.like(getExpression(attributeName,join,root)
                                        .as(String.class), "%" + val.toString() + "%"));
                            }
                            break;
                        case CONDITION_INNER_LIKE:
                            if(Const.SENSITIVEDATA_QUERY_CONDITION_true.equals(val.toString())){
                                list.add(cb.isNotNull(getExpression(attributeName,join,root)));
                            }else if (Const.SENSITIVEDATA_QUERY_CONDITION_false.equals(val.toString())){
                                list.add(cb.isNull(getExpression(attributeName,join,root)));
                            }
                            break;
                        default: break;
                    }
                }
                field.setAccessible(accessible);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        int size = list.size();
        return cb.and(list.toArray(new Predicate[size]));
    }

    @SuppressWarnings("unchecked")
    private static <T, R> Expression<T> getExpression(String attributeName, Join join, Root<R> root) {
        if (ObjectUtil.isNotEmpty(join)) {
            return join.get(attributeName);
        } else {
            return root.get(attributeName);
        }
    }

    private static boolean isBlank(final CharSequence cs) {
        int strLen;
        if (cs == null || (strLen = cs.length()) == 0) {
            return true;
        }
        for (int i = 0; i < strLen; i++) {
            if (!Character.isWhitespace(cs.charAt(i))) {
                return false;
            }
        }
        return true;
    }

    private static List<Field> getAllFields(Class clazz, List<Field> fields) {
        if (clazz != null) {
            fields.addAll(Arrays.asList(clazz.getDeclaredFields()));
            getAllFields(clazz.getSuperclass(), fields);
        }
        return fields;
    }
}
