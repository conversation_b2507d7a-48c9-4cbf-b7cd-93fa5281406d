package com.wzsec.modules.weakpwd.service.mapper;

import com.wzsec.modules.weakpwd.domain.ResultDatabase;
import com.wzsec.modules.weakpwd.service.dto.ResultDatabaseDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:30+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ResultDatabaseMapperImpl implements ResultDatabaseMapper {

    @Override
    public ResultDatabase toEntity(ResultDatabaseDto dto) {
        if ( dto == null ) {
            return null;
        }

        ResultDatabase resultDatabase = new ResultDatabase();

        resultDatabase.setId( dto.getId() );
        resultDatabase.setTaskno( dto.getTaskno() );
        resultDatabase.setSubtaskno( dto.getSubtaskno() );
        resultDatabase.setLocation( dto.getLocation() );
        resultDatabase.setType( dto.getType() );
        resultDatabase.setDbname( dto.getDbname() );
        resultDatabase.setWpcount( dto.getWpcount() );
        resultDatabase.setStarttime( dto.getStarttime() );
        resultDatabase.setEndtime( dto.getEndtime() );
        resultDatabase.setUsetime( dto.getUsetime() );
        resultDatabase.setCreatetime( dto.getCreatetime() );
        resultDatabase.setSparefield1( dto.getSparefield1() );
        resultDatabase.setSparefield2( dto.getSparefield2() );
        resultDatabase.setSparefield3( dto.getSparefield3() );
        resultDatabase.setSparefield4( dto.getSparefield4() );
        resultDatabase.setSparefield5( dto.getSparefield5() );

        return resultDatabase;
    }

    @Override
    public ResultDatabaseDto toDto(ResultDatabase entity) {
        if ( entity == null ) {
            return null;
        }

        ResultDatabaseDto resultDatabaseDto = new ResultDatabaseDto();

        resultDatabaseDto.setId( entity.getId() );
        resultDatabaseDto.setTaskno( entity.getTaskno() );
        resultDatabaseDto.setSubtaskno( entity.getSubtaskno() );
        resultDatabaseDto.setLocation( entity.getLocation() );
        resultDatabaseDto.setType( entity.getType() );
        resultDatabaseDto.setDbname( entity.getDbname() );
        resultDatabaseDto.setWpcount( entity.getWpcount() );
        resultDatabaseDto.setStarttime( entity.getStarttime() );
        resultDatabaseDto.setEndtime( entity.getEndtime() );
        resultDatabaseDto.setUsetime( entity.getUsetime() );
        resultDatabaseDto.setCreatetime( entity.getCreatetime() );
        resultDatabaseDto.setSparefield1( entity.getSparefield1() );
        resultDatabaseDto.setSparefield2( entity.getSparefield2() );
        resultDatabaseDto.setSparefield3( entity.getSparefield3() );
        resultDatabaseDto.setSparefield4( entity.getSparefield4() );
        resultDatabaseDto.setSparefield5( entity.getSparefield5() );

        return resultDatabaseDto;
    }

    @Override
    public List<ResultDatabase> toEntity(List<ResultDatabaseDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ResultDatabase> list = new ArrayList<ResultDatabase>( dtoList.size() );
        for ( ResultDatabaseDto resultDatabaseDto : dtoList ) {
            list.add( toEntity( resultDatabaseDto ) );
        }

        return list;
    }

    @Override
    public List<ResultDatabaseDto> toDto(List<ResultDatabase> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ResultDatabaseDto> list = new ArrayList<ResultDatabaseDto>( entityList.size() );
        for ( ResultDatabase resultDatabase : entityList ) {
            list.add( toDto( resultDatabase ) );
        }

        return list;
    }
}
