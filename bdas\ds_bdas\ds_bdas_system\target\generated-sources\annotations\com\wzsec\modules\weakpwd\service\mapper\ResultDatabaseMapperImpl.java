package com.wzsec.modules.weakpwd.service.mapper;

import com.wzsec.modules.weakpwd.domain.ResultDatabase;
import com.wzsec.modules.weakpwd.service.dto.ResultDatabaseDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:26:48+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ResultDatabaseMapperImpl implements ResultDatabaseMapper {

    @Override
    public ResultDatabaseDto toDto(ResultDatabase entity) {
        if ( entity == null ) {
            return null;
        }

        ResultDatabaseDto resultDatabaseDto = new ResultDatabaseDto();

        resultDatabaseDto.setCreatetime( entity.getCreatetime() );
        resultDatabaseDto.setDbname( entity.getDbname() );
        resultDatabaseDto.setEndtime( entity.getEndtime() );
        resultDatabaseDto.setId( entity.getId() );
        resultDatabaseDto.setLocation( entity.getLocation() );
        resultDatabaseDto.setSparefield1( entity.getSparefield1() );
        resultDatabaseDto.setSparefield2( entity.getSparefield2() );
        resultDatabaseDto.setSparefield3( entity.getSparefield3() );
        resultDatabaseDto.setSparefield4( entity.getSparefield4() );
        resultDatabaseDto.setSparefield5( entity.getSparefield5() );
        resultDatabaseDto.setStarttime( entity.getStarttime() );
        resultDatabaseDto.setSubtaskno( entity.getSubtaskno() );
        resultDatabaseDto.setTaskno( entity.getTaskno() );
        resultDatabaseDto.setType( entity.getType() );
        resultDatabaseDto.setUsetime( entity.getUsetime() );
        resultDatabaseDto.setWpcount( entity.getWpcount() );

        return resultDatabaseDto;
    }

    @Override
    public List<ResultDatabaseDto> toDto(List<ResultDatabase> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ResultDatabaseDto> list = new ArrayList<ResultDatabaseDto>( entityList.size() );
        for ( ResultDatabase resultDatabase : entityList ) {
            list.add( toDto( resultDatabase ) );
        }

        return list;
    }

    @Override
    public ResultDatabase toEntity(ResultDatabaseDto dto) {
        if ( dto == null ) {
            return null;
        }

        ResultDatabase resultDatabase = new ResultDatabase();

        resultDatabase.setCreatetime( dto.getCreatetime() );
        resultDatabase.setDbname( dto.getDbname() );
        resultDatabase.setEndtime( dto.getEndtime() );
        resultDatabase.setId( dto.getId() );
        resultDatabase.setLocation( dto.getLocation() );
        resultDatabase.setSparefield1( dto.getSparefield1() );
        resultDatabase.setSparefield2( dto.getSparefield2() );
        resultDatabase.setSparefield3( dto.getSparefield3() );
        resultDatabase.setSparefield4( dto.getSparefield4() );
        resultDatabase.setSparefield5( dto.getSparefield5() );
        resultDatabase.setStarttime( dto.getStarttime() );
        resultDatabase.setSubtaskno( dto.getSubtaskno() );
        resultDatabase.setTaskno( dto.getTaskno() );
        resultDatabase.setType( dto.getType() );
        resultDatabase.setUsetime( dto.getUsetime() );
        resultDatabase.setWpcount( dto.getWpcount() );

        return resultDatabase;
    }

    @Override
    public List<ResultDatabase> toEntity(List<ResultDatabaseDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ResultDatabase> list = new ArrayList<ResultDatabase>( dtoList.size() );
        for ( ResultDatabaseDto resultDatabaseDto : dtoList ) {
            list.add( toEntity( resultDatabaseDto ) );
        }

        return list;
    }
}
