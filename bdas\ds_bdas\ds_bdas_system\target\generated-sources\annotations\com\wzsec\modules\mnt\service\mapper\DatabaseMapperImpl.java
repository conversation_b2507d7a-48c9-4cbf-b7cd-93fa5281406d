package com.wzsec.modules.mnt.service.mapper;

import com.wzsec.modules.mnt.domain.Database;
import com.wzsec.modules.mnt.service.dto.DatabaseDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:26:46+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class DatabaseMapperImpl implements DatabaseMapper {

    @Override
    public DatabaseDto toDto(Database entity) {
        if ( entity == null ) {
            return null;
        }

        DatabaseDto databaseDto = new DatabaseDto();

        databaseDto.setCreateTime( entity.getCreateTime() );
        databaseDto.setId( entity.getId() );
        databaseDto.setJdbcUrl( entity.getJdbcUrl() );
        databaseDto.setName( entity.getName() );
        databaseDto.setPwd( entity.getPwd() );
        databaseDto.setUserName( entity.getUserName() );

        return databaseDto;
    }

    @Override
    public List<DatabaseDto> toDto(List<Database> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<DatabaseDto> list = new ArrayList<DatabaseDto>( entityList.size() );
        for ( Database database : entityList ) {
            list.add( toDto( database ) );
        }

        return list;
    }

    @Override
    public Database toEntity(DatabaseDto dto) {
        if ( dto == null ) {
            return null;
        }

        Database database = new Database();

        database.setCreateTime( dto.getCreateTime() );
        database.setId( dto.getId() );
        database.setJdbcUrl( dto.getJdbcUrl() );
        database.setName( dto.getName() );
        database.setPwd( dto.getPwd() );
        database.setUserName( dto.getUserName() );

        return database;
    }

    @Override
    public List<Database> toEntity(List<DatabaseDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Database> list = new ArrayList<Database>( dtoList.size() );
        for ( DatabaseDto databaseDto : dtoList ) {
            list.add( toEntity( databaseDto ) );
        }

        return list;
    }
}
