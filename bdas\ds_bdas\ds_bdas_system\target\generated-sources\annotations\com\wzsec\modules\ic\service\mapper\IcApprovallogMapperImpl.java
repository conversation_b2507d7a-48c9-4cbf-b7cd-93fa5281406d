package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcApprovallog;
import com.wzsec.modules.ic.service.dto.IcApprovallogDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:33+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class IcApprovallogMapperImpl implements IcApprovallogMapper {

    @Override
    public IcApprovallogDto toDto(IcApprovallog entity) {
        if ( entity == null ) {
            return null;
        }

        IcApprovallogDto icApprovallogDto = new IcApprovallogDto();

        icApprovallogDto.setAk( entity.getAk() );
        icApprovallogDto.setApplyorgname( entity.getApplyorgname() );
        icApprovallogDto.setApplytime( entity.getApplytime() );
        icApprovallogDto.setAudittime( entity.getAudittime() );
        icApprovallogDto.setCreatetime( entity.getCreatetime() );
        icApprovallogDto.setId( entity.getId() );
        icApprovallogDto.setInfoid( entity.getInfoid() );
        icApprovallogDto.setKeyid( entity.getKeyid() );
        icApprovallogDto.setMartsupplyorgname( entity.getMartsupplyorgname() );
        icApprovallogDto.setName( entity.getName() );
        icApprovallogDto.setOpen( entity.getOpen() );
        icApprovallogDto.setResourceapplyid( entity.getResourceapplyid() );
        icApprovallogDto.setResourcename( entity.getResourcename() );
        icApprovallogDto.setResourcetype( entity.getResourcetype() );
        icApprovallogDto.setShareapplyid( entity.getShareapplyid() );
        icApprovallogDto.setSharetype( entity.getSharetype() );
        icApprovallogDto.setSk( entity.getSk() );
        icApprovallogDto.setSparefield1( entity.getSparefield1() );
        icApprovallogDto.setSparefield2( entity.getSparefield2() );
        icApprovallogDto.setSparefield3( entity.getSparefield3() );
        icApprovallogDto.setSparefield4( entity.getSparefield4() );
        icApprovallogDto.setSparefield5( entity.getSparefield5() );
        icApprovallogDto.setSysname( entity.getSysname() );

        return icApprovallogDto;
    }

    @Override
    public List<IcApprovallogDto> toDto(List<IcApprovallog> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcApprovallogDto> list = new ArrayList<IcApprovallogDto>( entityList.size() );
        for ( IcApprovallog icApprovallog : entityList ) {
            list.add( toDto( icApprovallog ) );
        }

        return list;
    }

    @Override
    public IcApprovallog toEntity(IcApprovallogDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcApprovallog icApprovallog = new IcApprovallog();

        icApprovallog.setAk( dto.getAk() );
        icApprovallog.setApplyorgname( dto.getApplyorgname() );
        icApprovallog.setApplytime( dto.getApplytime() );
        icApprovallog.setAudittime( dto.getAudittime() );
        icApprovallog.setCreatetime( dto.getCreatetime() );
        icApprovallog.setId( dto.getId() );
        icApprovallog.setInfoid( dto.getInfoid() );
        icApprovallog.setKeyid( dto.getKeyid() );
        icApprovallog.setMartsupplyorgname( dto.getMartsupplyorgname() );
        icApprovallog.setName( dto.getName() );
        icApprovallog.setOpen( dto.getOpen() );
        icApprovallog.setResourceapplyid( dto.getResourceapplyid() );
        icApprovallog.setResourcename( dto.getResourcename() );
        icApprovallog.setResourcetype( dto.getResourcetype() );
        icApprovallog.setShareapplyid( dto.getShareapplyid() );
        icApprovallog.setSharetype( dto.getSharetype() );
        icApprovallog.setSk( dto.getSk() );
        icApprovallog.setSparefield1( dto.getSparefield1() );
        icApprovallog.setSparefield2( dto.getSparefield2() );
        icApprovallog.setSparefield3( dto.getSparefield3() );
        icApprovallog.setSparefield4( dto.getSparefield4() );
        icApprovallog.setSparefield5( dto.getSparefield5() );
        icApprovallog.setSysname( dto.getSysname() );

        return icApprovallog;
    }

    @Override
    public List<IcApprovallog> toEntity(List<IcApprovallogDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcApprovallog> list = new ArrayList<IcApprovallog>( dtoList.size() );
        for ( IcApprovallogDto icApprovallogDto : dtoList ) {
            list.add( toEntity( icApprovallogDto ) );
        }

        return list;
    }
}
