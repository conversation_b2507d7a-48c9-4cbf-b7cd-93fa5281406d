package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.ApiUnusedak;
import com.wzsec.modules.ic.service.dto.ApiUnusedakDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:31+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ApiUnusedakMapperImpl implements ApiUnusedakMapper {

    @Override
    public ApiUnusedak toEntity(ApiUnusedakDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiUnusedak apiUnusedak = new ApiUnusedak();

        apiUnusedak.setId( dto.getId() );
        apiUnusedak.setAk( dto.getAk() );
        apiUnusedak.setApplyorgname( dto.getApplyorgname() );
        apiUnusedak.setApicode( dto.getApicode() );
        apiUnusedak.setApiname( dto.getApiname() );
        apiUnusedak.setApiurl( dto.getApiurl() );
        apiUnusedak.setRisk( dto.getRisk() );
        apiUnusedak.setChecktime( dto.getChecktime() );
        apiUnusedak.setSystemname( dto.getSystemname() );
        apiUnusedak.setSparefield1( dto.getSparefield1() );
        apiUnusedak.setSparefield2( dto.getSparefield2() );
        apiUnusedak.setSparefield3( dto.getSparefield3() );
        apiUnusedak.setSparefield4( dto.getSparefield4() );
        apiUnusedak.setReqip( dto.getReqip() );

        return apiUnusedak;
    }

    @Override
    public ApiUnusedakDto toDto(ApiUnusedak entity) {
        if ( entity == null ) {
            return null;
        }

        ApiUnusedakDto apiUnusedakDto = new ApiUnusedakDto();

        apiUnusedakDto.setId( entity.getId() );
        apiUnusedakDto.setAk( entity.getAk() );
        apiUnusedakDto.setApplyorgname( entity.getApplyorgname() );
        apiUnusedakDto.setApicode( entity.getApicode() );
        apiUnusedakDto.setApiname( entity.getApiname() );
        apiUnusedakDto.setApiurl( entity.getApiurl() );
        apiUnusedakDto.setRisk( entity.getRisk() );
        apiUnusedakDto.setChecktime( entity.getChecktime() );
        apiUnusedakDto.setSystemname( entity.getSystemname() );
        apiUnusedakDto.setSparefield1( entity.getSparefield1() );
        apiUnusedakDto.setSparefield2( entity.getSparefield2() );
        apiUnusedakDto.setSparefield3( entity.getSparefield3() );
        apiUnusedakDto.setSparefield4( entity.getSparefield4() );
        apiUnusedakDto.setReqip( entity.getReqip() );

        return apiUnusedakDto;
    }

    @Override
    public List<ApiUnusedak> toEntity(List<ApiUnusedakDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiUnusedak> list = new ArrayList<ApiUnusedak>( dtoList.size() );
        for ( ApiUnusedakDto apiUnusedakDto : dtoList ) {
            list.add( toEntity( apiUnusedakDto ) );
        }

        return list;
    }

    @Override
    public List<ApiUnusedakDto> toDto(List<ApiUnusedak> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiUnusedakDto> list = new ArrayList<ApiUnusedakDto>( entityList.size() );
        for ( ApiUnusedak apiUnusedak : entityList ) {
            list.add( toDto( apiUnusedak ) );
        }

        return list;
    }
}
