package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcTask;
import com.wzsec.modules.ic.service.dto.IcTaskDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:27:08+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class IcTaskMapperImpl implements IcTaskMapper {

    @Override
    public IcTaskDto toDto(IcTask entity) {
        if ( entity == null ) {
            return null;
        }

        IcTaskDto icTaskDto = new IcTaskDto();

        icTaskDto.setCreatetime( entity.getCreatetime() );
        icTaskDto.setCreateuser( entity.getCreateuser() );
        icTaskDto.setCron( entity.getCron() );
        icTaskDto.setExecutionstate( entity.getExecutionstate() );
        icTaskDto.setFilepath( entity.getFilepath() );
        icTaskDto.setId( entity.getId() );
        icTaskDto.setIsdependstrategy( entity.getIsdependstrategy() );
        icTaskDto.setLogsign( entity.getLogsign() );
        icTaskDto.setSensitivestrategy( entity.getSensitivestrategy() );
        icTaskDto.setSourceid( entity.getSourceid() );
        icTaskDto.setSparefield1( entity.getSparefield1() );
        icTaskDto.setSparefield2( entity.getSparefield2() );
        icTaskDto.setSparefield3( entity.getSparefield3() );
        icTaskDto.setSparefield4( entity.getSparefield4() );
        icTaskDto.setStatus( entity.getStatus() );
        icTaskDto.setStrategyconfig( entity.getStrategyconfig() );
        icTaskDto.setSubmitmethod( entity.getSubmitmethod() );
        icTaskDto.setSubmitname( entity.getSubmitname() );
        icTaskDto.setTaskname( entity.getTaskname() );
        icTaskDto.setTasktype( entity.getTasktype() );
        icTaskDto.setUpdatetime( entity.getUpdatetime() );
        icTaskDto.setUpdateuser( entity.getUpdateuser() );

        return icTaskDto;
    }

    @Override
    public List<IcTaskDto> toDto(List<IcTask> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcTaskDto> list = new ArrayList<IcTaskDto>( entityList.size() );
        for ( IcTask icTask : entityList ) {
            list.add( toDto( icTask ) );
        }

        return list;
    }

    @Override
    public IcTask toEntity(IcTaskDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcTask icTask = new IcTask();

        icTask.setCreatetime( dto.getCreatetime() );
        icTask.setCreateuser( dto.getCreateuser() );
        icTask.setCron( dto.getCron() );
        icTask.setExecutionstate( dto.getExecutionstate() );
        icTask.setFilepath( dto.getFilepath() );
        icTask.setId( dto.getId() );
        icTask.setIsdependstrategy( dto.getIsdependstrategy() );
        icTask.setLogsign( dto.getLogsign() );
        icTask.setSensitivestrategy( dto.getSensitivestrategy() );
        icTask.setSourceid( dto.getSourceid() );
        icTask.setSparefield1( dto.getSparefield1() );
        icTask.setSparefield2( dto.getSparefield2() );
        icTask.setSparefield3( dto.getSparefield3() );
        icTask.setSparefield4( dto.getSparefield4() );
        icTask.setStatus( dto.getStatus() );
        icTask.setStrategyconfig( dto.getStrategyconfig() );
        icTask.setSubmitmethod( dto.getSubmitmethod() );
        icTask.setSubmitname( dto.getSubmitname() );
        icTask.setTaskname( dto.getTaskname() );
        icTask.setTasktype( dto.getTasktype() );
        icTask.setUpdatetime( dto.getUpdatetime() );
        icTask.setUpdateuser( dto.getUpdateuser() );

        return icTask;
    }

    @Override
    public List<IcTask> toEntity(List<IcTaskDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcTask> list = new ArrayList<IcTask>( dtoList.size() );
        for ( IcTaskDto icTaskDto : dtoList ) {
            list.add( toEntity( icTaskDto ) );
        }

        return list;
    }
}
