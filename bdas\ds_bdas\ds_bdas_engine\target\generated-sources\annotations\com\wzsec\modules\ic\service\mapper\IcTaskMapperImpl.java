package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcTask;
import com.wzsec.modules.ic.service.dto.IcTaskDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:01+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class IcTaskMapperImpl implements IcTaskMapper {

    @Override
    public IcTask toEntity(IcTaskDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcTask icTask = new IcTask();

        icTask.setId( dto.getId() );
        icTask.setTaskname( dto.getTaskname() );
        icTask.setStrategyconfig( dto.getStrategyconfig() );
        icTask.setSensitivestrategy( dto.getSensitivestrategy() );
        icTask.setLogsign( dto.getLogsign() );
        icTask.setTasktype( dto.getTasktype() );
        icTask.setSubmitname( dto.getSubmitname() );
        icTask.setIsdependstrategy( dto.getIsdependstrategy() );
        icTask.setStatus( dto.getStatus() );
        icTask.setSubmitmethod( dto.getSubmitmethod() );
        icTask.setCron( dto.getCron() );
        icTask.setExecutionstate( dto.getExecutionstate() );
        icTask.setSourceid( dto.getSourceid() );
        icTask.setFilepath( dto.getFilepath() );
        icTask.setCreateuser( dto.getCreateuser() );
        icTask.setCreatetime( dto.getCreatetime() );
        icTask.setUpdatetime( dto.getUpdatetime() );
        icTask.setUpdateuser( dto.getUpdateuser() );
        icTask.setSparefield1( dto.getSparefield1() );
        icTask.setSparefield2( dto.getSparefield2() );
        icTask.setSparefield3( dto.getSparefield3() );
        icTask.setSparefield4( dto.getSparefield4() );

        return icTask;
    }

    @Override
    public IcTaskDto toDto(IcTask entity) {
        if ( entity == null ) {
            return null;
        }

        IcTaskDto icTaskDto = new IcTaskDto();

        icTaskDto.setId( entity.getId() );
        icTaskDto.setTaskname( entity.getTaskname() );
        icTaskDto.setStrategyconfig( entity.getStrategyconfig() );
        icTaskDto.setSensitivestrategy( entity.getSensitivestrategy() );
        icTaskDto.setLogsign( entity.getLogsign() );
        icTaskDto.setTasktype( entity.getTasktype() );
        icTaskDto.setSubmitname( entity.getSubmitname() );
        icTaskDto.setIsdependstrategy( entity.getIsdependstrategy() );
        icTaskDto.setStatus( entity.getStatus() );
        icTaskDto.setSubmitmethod( entity.getSubmitmethod() );
        icTaskDto.setCron( entity.getCron() );
        icTaskDto.setExecutionstate( entity.getExecutionstate() );
        icTaskDto.setSourceid( entity.getSourceid() );
        icTaskDto.setFilepath( entity.getFilepath() );
        icTaskDto.setCreateuser( entity.getCreateuser() );
        icTaskDto.setCreatetime( entity.getCreatetime() );
        icTaskDto.setUpdatetime( entity.getUpdatetime() );
        icTaskDto.setUpdateuser( entity.getUpdateuser() );
        icTaskDto.setSparefield1( entity.getSparefield1() );
        icTaskDto.setSparefield2( entity.getSparefield2() );
        icTaskDto.setSparefield3( entity.getSparefield3() );
        icTaskDto.setSparefield4( entity.getSparefield4() );

        return icTaskDto;
    }

    @Override
    public List<IcTask> toEntity(List<IcTaskDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcTask> list = new ArrayList<IcTask>( dtoList.size() );
        for ( IcTaskDto icTaskDto : dtoList ) {
            list.add( toEntity( icTaskDto ) );
        }

        return list;
    }

    @Override
    public List<IcTaskDto> toDto(List<IcTask> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcTaskDto> list = new ArrayList<IcTaskDto>( entityList.size() );
        for ( IcTask icTask : entityList ) {
            list.add( toDto( icTask ) );
        }

        return list;
    }
}
