package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcScenedetection;
import com.wzsec.modules.ic.service.dto.IcScenedetectionDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:31+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class IcScenedetectionMapperImpl implements IcScenedetectionMapper {

    @Override
    public IcScenedetection toEntity(IcScenedetectionDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcScenedetection icScenedetection = new IcScenedetection();

        icScenedetection.setId( dto.getId() );
        icScenedetection.setScene( dto.getScene() );
        icScenedetection.setEstimated( dto.getEstimated() );
        icScenedetection.setCreator( dto.getCreator() );
        icScenedetection.setCreationtime( dto.getCreationtime() );
        icScenedetection.setRegenerator( dto.getRegenerator() );
        icScenedetection.setTurnovertime( dto.getTurnovertime() );
        icScenedetection.setSparefield1( dto.getSparefield1() );
        icScenedetection.setSparefield2( dto.getSparefield2() );
        icScenedetection.setSparefield3( dto.getSparefield3() );
        icScenedetection.setSparefield4( dto.getSparefield4() );

        return icScenedetection;
    }

    @Override
    public IcScenedetectionDto toDto(IcScenedetection entity) {
        if ( entity == null ) {
            return null;
        }

        IcScenedetectionDto icScenedetectionDto = new IcScenedetectionDto();

        icScenedetectionDto.setId( entity.getId() );
        icScenedetectionDto.setScene( entity.getScene() );
        icScenedetectionDto.setEstimated( entity.getEstimated() );
        icScenedetectionDto.setCreator( entity.getCreator() );
        icScenedetectionDto.setCreationtime( entity.getCreationtime() );
        icScenedetectionDto.setRegenerator( entity.getRegenerator() );
        icScenedetectionDto.setTurnovertime( entity.getTurnovertime() );
        icScenedetectionDto.setSparefield1( entity.getSparefield1() );
        icScenedetectionDto.setSparefield2( entity.getSparefield2() );
        icScenedetectionDto.setSparefield3( entity.getSparefield3() );
        icScenedetectionDto.setSparefield4( entity.getSparefield4() );

        return icScenedetectionDto;
    }

    @Override
    public List<IcScenedetection> toEntity(List<IcScenedetectionDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcScenedetection> list = new ArrayList<IcScenedetection>( dtoList.size() );
        for ( IcScenedetectionDto icScenedetectionDto : dtoList ) {
            list.add( toEntity( icScenedetectionDto ) );
        }

        return list;
    }

    @Override
    public List<IcScenedetectionDto> toDto(List<IcScenedetection> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcScenedetectionDto> list = new ArrayList<IcScenedetectionDto>( entityList.size() );
        for ( IcScenedetection icScenedetection : entityList ) {
            list.add( toDto( icScenedetection ) );
        }

        return list;
    }
}
