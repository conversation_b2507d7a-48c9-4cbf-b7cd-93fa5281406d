package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcScenedetection;
import com.wzsec.modules.ic.service.dto.IcScenedetectionDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:29+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class IcScenedetectionMapperImpl implements IcScenedetectionMapper {

    @Override
    public IcScenedetectionDto toDto(IcScenedetection entity) {
        if ( entity == null ) {
            return null;
        }

        IcScenedetectionDto icScenedetectionDto = new IcScenedetectionDto();

        icScenedetectionDto.setCreationtime( entity.getCreationtime() );
        icScenedetectionDto.setCreator( entity.getCreator() );
        icScenedetectionDto.setEstimated( entity.getEstimated() );
        icScenedetectionDto.setId( entity.getId() );
        icScenedetectionDto.setRegenerator( entity.getRegenerator() );
        icScenedetectionDto.setScene( entity.getScene() );
        icScenedetectionDto.setSparefield1( entity.getSparefield1() );
        icScenedetectionDto.setSparefield2( entity.getSparefield2() );
        icScenedetectionDto.setSparefield3( entity.getSparefield3() );
        icScenedetectionDto.setSparefield4( entity.getSparefield4() );
        icScenedetectionDto.setTurnovertime( entity.getTurnovertime() );

        return icScenedetectionDto;
    }

    @Override
    public List<IcScenedetectionDto> toDto(List<IcScenedetection> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcScenedetectionDto> list = new ArrayList<IcScenedetectionDto>( entityList.size() );
        for ( IcScenedetection icScenedetection : entityList ) {
            list.add( toDto( icScenedetection ) );
        }

        return list;
    }

    @Override
    public IcScenedetection toEntity(IcScenedetectionDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcScenedetection icScenedetection = new IcScenedetection();

        icScenedetection.setCreationtime( dto.getCreationtime() );
        icScenedetection.setCreator( dto.getCreator() );
        icScenedetection.setEstimated( dto.getEstimated() );
        icScenedetection.setId( dto.getId() );
        icScenedetection.setRegenerator( dto.getRegenerator() );
        icScenedetection.setScene( dto.getScene() );
        icScenedetection.setSparefield1( dto.getSparefield1() );
        icScenedetection.setSparefield2( dto.getSparefield2() );
        icScenedetection.setSparefield3( dto.getSparefield3() );
        icScenedetection.setSparefield4( dto.getSparefield4() );
        icScenedetection.setTurnovertime( dto.getTurnovertime() );

        return icScenedetection;
    }

    @Override
    public List<IcScenedetection> toEntity(List<IcScenedetectionDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcScenedetection> list = new ArrayList<IcScenedetection>( dtoList.size() );
        for ( IcScenedetectionDto icScenedetectionDto : dtoList ) {
            list.add( toEntity( icScenedetectionDto ) );
        }

        return list;
    }
}
