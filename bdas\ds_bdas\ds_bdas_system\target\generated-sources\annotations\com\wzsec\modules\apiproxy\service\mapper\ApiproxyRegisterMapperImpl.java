package com.wzsec.modules.apiproxy.service.mapper;

import com.wzsec.modules.apiproxy.domain.ApiproxyRegister;
import com.wzsec.modules.apiproxy.service.dto.ApiproxyRegisterDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:30+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ApiproxyRegisterMapperImpl implements ApiproxyRegisterMapper {

    @Override
    public ApiproxyRegister toEntity(ApiproxyRegisterDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiproxyRegister apiproxyRegister = new ApiproxyRegister();

        apiproxyRegister.setId( dto.getId() );
        apiproxyRegister.setRegistername( dto.getRegistername() );
        apiproxyRegister.setServerhost( dto.getServerhost() );
        apiproxyRegister.setServerport( dto.getServerport() );
        apiproxyRegister.setExecutionengine( dto.getExecutionengine() );
        apiproxyRegister.setProxyport( dto.getProxyport() );
        apiproxyRegister.setIsvalid( dto.getIsvalid() );
        apiproxyRegister.setStatus( dto.getStatus() );
        apiproxyRegister.setNote( dto.getNote() );
        apiproxyRegister.setCreateuser( dto.getCreateuser() );
        apiproxyRegister.setCreatetime( dto.getCreatetime() );
        apiproxyRegister.setUpdateuser( dto.getUpdateuser() );
        apiproxyRegister.setUpdatetime( dto.getUpdatetime() );
        apiproxyRegister.setSparefield1( dto.getSparefield1() );
        apiproxyRegister.setSparefield2( dto.getSparefield2() );
        apiproxyRegister.setSparefield3( dto.getSparefield3() );
        apiproxyRegister.setSparefield4( dto.getSparefield4() );
        apiproxyRegister.setSparefield5( dto.getSparefield5() );

        return apiproxyRegister;
    }

    @Override
    public ApiproxyRegisterDto toDto(ApiproxyRegister entity) {
        if ( entity == null ) {
            return null;
        }

        ApiproxyRegisterDto apiproxyRegisterDto = new ApiproxyRegisterDto();

        apiproxyRegisterDto.setId( entity.getId() );
        apiproxyRegisterDto.setRegistername( entity.getRegistername() );
        apiproxyRegisterDto.setServerhost( entity.getServerhost() );
        apiproxyRegisterDto.setServerport( entity.getServerport() );
        apiproxyRegisterDto.setExecutionengine( entity.getExecutionengine() );
        apiproxyRegisterDto.setProxyport( entity.getProxyport() );
        apiproxyRegisterDto.setIsvalid( entity.getIsvalid() );
        apiproxyRegisterDto.setStatus( entity.getStatus() );
        apiproxyRegisterDto.setNote( entity.getNote() );
        apiproxyRegisterDto.setCreateuser( entity.getCreateuser() );
        apiproxyRegisterDto.setCreatetime( entity.getCreatetime() );
        apiproxyRegisterDto.setUpdateuser( entity.getUpdateuser() );
        apiproxyRegisterDto.setUpdatetime( entity.getUpdatetime() );
        apiproxyRegisterDto.setSparefield1( entity.getSparefield1() );
        apiproxyRegisterDto.setSparefield2( entity.getSparefield2() );
        apiproxyRegisterDto.setSparefield3( entity.getSparefield3() );
        apiproxyRegisterDto.setSparefield4( entity.getSparefield4() );
        apiproxyRegisterDto.setSparefield5( entity.getSparefield5() );

        return apiproxyRegisterDto;
    }

    @Override
    public List<ApiproxyRegister> toEntity(List<ApiproxyRegisterDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiproxyRegister> list = new ArrayList<ApiproxyRegister>( dtoList.size() );
        for ( ApiproxyRegisterDto apiproxyRegisterDto : dtoList ) {
            list.add( toEntity( apiproxyRegisterDto ) );
        }

        return list;
    }

    @Override
    public List<ApiproxyRegisterDto> toDto(List<ApiproxyRegister> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiproxyRegisterDto> list = new ArrayList<ApiproxyRegisterDto>( entityList.size() );
        for ( ApiproxyRegister apiproxyRegister : entityList ) {
            list.add( toDto( apiproxyRegister ) );
        }

        return list;
    }
}
