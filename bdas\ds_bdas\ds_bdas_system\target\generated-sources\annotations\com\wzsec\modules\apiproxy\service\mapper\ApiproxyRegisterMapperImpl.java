package com.wzsec.modules.apiproxy.service.mapper;

import com.wzsec.modules.apiproxy.domain.ApiproxyRegister;
import com.wzsec.modules.apiproxy.service.dto.ApiproxyRegisterDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:33+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ApiproxyRegisterMapperImpl implements ApiproxyRegisterMapper {

    @Override
    public ApiproxyRegisterDto toDto(ApiproxyRegister entity) {
        if ( entity == null ) {
            return null;
        }

        ApiproxyRegisterDto apiproxyRegisterDto = new ApiproxyRegisterDto();

        apiproxyRegisterDto.setCreatetime( entity.getCreatetime() );
        apiproxyRegisterDto.setCreateuser( entity.getCreateuser() );
        apiproxyRegisterDto.setExecutionengine( entity.getExecutionengine() );
        apiproxyRegisterDto.setId( entity.getId() );
        apiproxyRegisterDto.setIsvalid( entity.getIsvalid() );
        apiproxyRegisterDto.setNote( entity.getNote() );
        apiproxyRegisterDto.setProxyport( entity.getProxyport() );
        apiproxyRegisterDto.setRegistername( entity.getRegistername() );
        apiproxyRegisterDto.setServerhost( entity.getServerhost() );
        apiproxyRegisterDto.setServerport( entity.getServerport() );
        apiproxyRegisterDto.setSparefield1( entity.getSparefield1() );
        apiproxyRegisterDto.setSparefield2( entity.getSparefield2() );
        apiproxyRegisterDto.setSparefield3( entity.getSparefield3() );
        apiproxyRegisterDto.setSparefield4( entity.getSparefield4() );
        apiproxyRegisterDto.setSparefield5( entity.getSparefield5() );
        apiproxyRegisterDto.setStatus( entity.getStatus() );
        apiproxyRegisterDto.setUpdatetime( entity.getUpdatetime() );
        apiproxyRegisterDto.setUpdateuser( entity.getUpdateuser() );

        return apiproxyRegisterDto;
    }

    @Override
    public List<ApiproxyRegisterDto> toDto(List<ApiproxyRegister> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiproxyRegisterDto> list = new ArrayList<ApiproxyRegisterDto>( entityList.size() );
        for ( ApiproxyRegister apiproxyRegister : entityList ) {
            list.add( toDto( apiproxyRegister ) );
        }

        return list;
    }

    @Override
    public ApiproxyRegister toEntity(ApiproxyRegisterDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiproxyRegister apiproxyRegister = new ApiproxyRegister();

        apiproxyRegister.setCreatetime( dto.getCreatetime() );
        apiproxyRegister.setCreateuser( dto.getCreateuser() );
        apiproxyRegister.setExecutionengine( dto.getExecutionengine() );
        apiproxyRegister.setId( dto.getId() );
        apiproxyRegister.setIsvalid( dto.getIsvalid() );
        apiproxyRegister.setNote( dto.getNote() );
        apiproxyRegister.setProxyport( dto.getProxyport() );
        apiproxyRegister.setRegistername( dto.getRegistername() );
        apiproxyRegister.setServerhost( dto.getServerhost() );
        apiproxyRegister.setServerport( dto.getServerport() );
        apiproxyRegister.setSparefield1( dto.getSparefield1() );
        apiproxyRegister.setSparefield2( dto.getSparefield2() );
        apiproxyRegister.setSparefield3( dto.getSparefield3() );
        apiproxyRegister.setSparefield4( dto.getSparefield4() );
        apiproxyRegister.setSparefield5( dto.getSparefield5() );
        apiproxyRegister.setStatus( dto.getStatus() );
        apiproxyRegister.setUpdatetime( dto.getUpdatetime() );
        apiproxyRegister.setUpdateuser( dto.getUpdateuser() );

        return apiproxyRegister;
    }

    @Override
    public List<ApiproxyRegister> toEntity(List<ApiproxyRegisterDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiproxyRegister> list = new ArrayList<ApiproxyRegister>( dtoList.size() );
        for ( ApiproxyRegisterDto apiproxyRegisterDto : dtoList ) {
            list.add( toEntity( apiproxyRegisterDto ) );
        }

        return list;
    }
}
