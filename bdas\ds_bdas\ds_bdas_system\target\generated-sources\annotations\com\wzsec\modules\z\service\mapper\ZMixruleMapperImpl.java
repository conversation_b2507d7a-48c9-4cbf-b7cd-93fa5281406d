package com.wzsec.modules.z.service.mapper;

import com.wzsec.modules.z.domain.ZMixrule;
import com.wzsec.modules.z.service.dto.ZMixruleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:31+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ZMixruleMapperImpl implements ZMixruleMapper {

    @Override
    public ZMixrule toEntity(ZMixruleDto dto) {
        if ( dto == null ) {
            return null;
        }

        ZMixrule zMixrule = new ZMixrule();

        zMixrule.setId( dto.getId() );
        zMixrule.setName( dto.getName() );
        zMixrule.setEnname( dto.getEnname() );
        zMixrule.setLevel( dto.getLevel() );
        zMixrule.setDes( dto.getDes() );
        zMixrule.setSeparatorstr( dto.getSeparatorstr() );
        zMixrule.setCheckrule( dto.getCheckrule() );
        zMixrule.setNote( dto.getNote() );
        zMixrule.setReservefield1( dto.getReservefield1() );
        zMixrule.setReservefield2( dto.getReservefield2() );

        return zMixrule;
    }

    @Override
    public ZMixruleDto toDto(ZMixrule entity) {
        if ( entity == null ) {
            return null;
        }

        ZMixruleDto zMixruleDto = new ZMixruleDto();

        zMixruleDto.setId( entity.getId() );
        zMixruleDto.setName( entity.getName() );
        zMixruleDto.setEnname( entity.getEnname() );
        zMixruleDto.setLevel( entity.getLevel() );
        zMixruleDto.setDes( entity.getDes() );
        zMixruleDto.setSeparatorstr( entity.getSeparatorstr() );
        zMixruleDto.setCheckrule( entity.getCheckrule() );
        zMixruleDto.setNote( entity.getNote() );
        zMixruleDto.setReservefield1( entity.getReservefield1() );
        zMixruleDto.setReservefield2( entity.getReservefield2() );

        return zMixruleDto;
    }

    @Override
    public List<ZMixrule> toEntity(List<ZMixruleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ZMixrule> list = new ArrayList<ZMixrule>( dtoList.size() );
        for ( ZMixruleDto zMixruleDto : dtoList ) {
            list.add( toEntity( zMixruleDto ) );
        }

        return list;
    }

    @Override
    public List<ZMixruleDto> toDto(List<ZMixrule> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ZMixruleDto> list = new ArrayList<ZMixruleDto>( entityList.size() );
        for ( ZMixrule zMixrule : entityList ) {
            list.add( toDto( zMixrule ) );
        }

        return list;
    }
}
