package com.wzsec.modules.z.service.mapper;

import com.wzsec.modules.z.domain.ZMixrule;
import com.wzsec.modules.z.service.dto.ZMixruleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:33+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ZMixruleMapperImpl implements ZMixruleMapper {

    @Override
    public ZMixruleDto toDto(ZMixrule entity) {
        if ( entity == null ) {
            return null;
        }

        ZMixruleDto zMixruleDto = new ZMixruleDto();

        zMixruleDto.setCheckrule( entity.getCheckrule() );
        zMixruleDto.setDes( entity.getDes() );
        zMixruleDto.setEnname( entity.getEnname() );
        zMixruleDto.setId( entity.getId() );
        zMixruleDto.setLevel( entity.getLevel() );
        zMixruleDto.setName( entity.getName() );
        zMixruleDto.setNote( entity.getNote() );
        zMixruleDto.setReservefield1( entity.getReservefield1() );
        zMixruleDto.setReservefield2( entity.getReservefield2() );
        zMixruleDto.setSeparatorstr( entity.getSeparatorstr() );

        return zMixruleDto;
    }

    @Override
    public List<ZMixruleDto> toDto(List<ZMixrule> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ZMixruleDto> list = new ArrayList<ZMixruleDto>( entityList.size() );
        for ( ZMixrule zMixrule : entityList ) {
            list.add( toDto( zMixrule ) );
        }

        return list;
    }

    @Override
    public ZMixrule toEntity(ZMixruleDto dto) {
        if ( dto == null ) {
            return null;
        }

        ZMixrule zMixrule = new ZMixrule();

        zMixrule.setCheckrule( dto.getCheckrule() );
        zMixrule.setDes( dto.getDes() );
        zMixrule.setEnname( dto.getEnname() );
        zMixrule.setId( dto.getId() );
        zMixrule.setLevel( dto.getLevel() );
        zMixrule.setName( dto.getName() );
        zMixrule.setNote( dto.getNote() );
        zMixrule.setReservefield1( dto.getReservefield1() );
        zMixrule.setReservefield2( dto.getReservefield2() );
        zMixrule.setSeparatorstr( dto.getSeparatorstr() );

        return zMixrule;
    }

    @Override
    public List<ZMixrule> toEntity(List<ZMixruleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ZMixrule> list = new ArrayList<ZMixrule>( dtoList.size() );
        for ( ZMixruleDto zMixruleDto : dtoList ) {
            list.add( toEntity( zMixruleDto ) );
        }

        return list;
    }
}
