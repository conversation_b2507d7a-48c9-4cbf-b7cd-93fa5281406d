package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.Approvaldetection;
import com.wzsec.modules.ic.service.dto.ApprovaldetectionDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:27:08+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ApprovaldetectionMapperImpl implements ApprovaldetectionMapper {

    @Override
    public ApprovaldetectionDto toDto(Approvaldetection entity) {
        if ( entity == null ) {
            return null;
        }

        ApprovaldetectionDto approvaldetectionDto = new ApprovaldetectionDto();

        approvaldetectionDto.setActualoutput( entity.getActualoutput() );
        approvaldetectionDto.setAk( entity.getAk() );
        approvaldetectionDto.setApicode( entity.getApicode() );
        approvaldetectionDto.setApiname( entity.getApiname() );
        approvaldetectionDto.setApplyorgname( entity.getApplyorgname() );
        approvaldetectionDto.setApplyoutput( entity.getApplyoutput() );
        approvaldetectionDto.setApplytime( entity.getApplytime() );
        approvaldetectionDto.setAudittime( entity.getAudittime() );
        approvaldetectionDto.setCreatetime( entity.getCreatetime() );
        approvaldetectionDto.setId( entity.getId() );
        approvaldetectionDto.setMartsupplyorgname( entity.getMartsupplyorgname() );
        approvaldetectionDto.setName( entity.getName() );
        approvaldetectionDto.setResourcename( entity.getResourcename() );
        approvaldetectionDto.setRisk( entity.getRisk() );
        approvaldetectionDto.setShareapplyid( entity.getShareapplyid() );
        approvaldetectionDto.setSk( entity.getSk() );
        approvaldetectionDto.setSparefield1( entity.getSparefield1() );
        approvaldetectionDto.setSparefield2( entity.getSparefield2() );
        approvaldetectionDto.setSparefield3( entity.getSparefield3() );
        approvaldetectionDto.setSparefield4( entity.getSparefield4() );
        approvaldetectionDto.setSparefield5( entity.getSparefield5() );
        approvaldetectionDto.setSysname( entity.getSysname() );

        return approvaldetectionDto;
    }

    @Override
    public List<ApprovaldetectionDto> toDto(List<Approvaldetection> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApprovaldetectionDto> list = new ArrayList<ApprovaldetectionDto>( entityList.size() );
        for ( Approvaldetection approvaldetection : entityList ) {
            list.add( toDto( approvaldetection ) );
        }

        return list;
    }

    @Override
    public Approvaldetection toEntity(ApprovaldetectionDto dto) {
        if ( dto == null ) {
            return null;
        }

        Approvaldetection approvaldetection = new Approvaldetection();

        approvaldetection.setActualoutput( dto.getActualoutput() );
        approvaldetection.setAk( dto.getAk() );
        approvaldetection.setApicode( dto.getApicode() );
        approvaldetection.setApiname( dto.getApiname() );
        approvaldetection.setApplyorgname( dto.getApplyorgname() );
        approvaldetection.setApplyoutput( dto.getApplyoutput() );
        approvaldetection.setApplytime( dto.getApplytime() );
        approvaldetection.setAudittime( dto.getAudittime() );
        approvaldetection.setCreatetime( dto.getCreatetime() );
        approvaldetection.setId( dto.getId() );
        approvaldetection.setMartsupplyorgname( dto.getMartsupplyorgname() );
        approvaldetection.setName( dto.getName() );
        approvaldetection.setResourcename( dto.getResourcename() );
        approvaldetection.setRisk( dto.getRisk() );
        approvaldetection.setShareapplyid( dto.getShareapplyid() );
        approvaldetection.setSk( dto.getSk() );
        approvaldetection.setSparefield1( dto.getSparefield1() );
        approvaldetection.setSparefield2( dto.getSparefield2() );
        approvaldetection.setSparefield3( dto.getSparefield3() );
        approvaldetection.setSparefield4( dto.getSparefield4() );
        approvaldetection.setSparefield5( dto.getSparefield5() );
        approvaldetection.setSysname( dto.getSysname() );

        return approvaldetection;
    }

    @Override
    public List<Approvaldetection> toEntity(List<ApprovaldetectionDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Approvaldetection> list = new ArrayList<Approvaldetection>( dtoList.size() );
        for ( ApprovaldetectionDto approvaldetectionDto : dtoList ) {
            list.add( toEntity( approvaldetectionDto ) );
        }

        return list;
    }
}
