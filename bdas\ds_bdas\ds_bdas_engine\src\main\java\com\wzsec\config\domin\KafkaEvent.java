package com.wzsec.config.domin;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * kafka推送事件对象定义
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class KafkaEvent {

    private Event event;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Event {
        /** UUID,事件ID */
        private String id;
        /** yyyy-MM-dd HH:mm:ss 事件创建时间 */
        private String created;
        /** api 固定值 */
        private String category;
        /** 维祯API 固定值 */
        private String zone;
    }

    private Rule rule;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Rule {
        /** 告警规则id */
        private String ruleId;
        /** 告警规则名 */
        private String name;
        /** 告警规则描述 */
        private String description;
        /** 告警等级 低危，中危，高危 */
        private String severity;
        /** share,规则类型 固定值 */
        private String category;
        /** api,规则子类型 固定值 */
        private String subcategory;
        /** external 固定值 */
        private String type;
        /** 交换 固定值 */
        private String lifecycle;
        /** {"非法访问", "内容异常检测", "共享交换平台"} */
        private String tags;
    }

    /** 告警描述 */
    private String message;
    private Destination destination;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Destination {
        /** 目标IP */
        private String ip;
        /** 目标端口 */
        private String port;
    }

    private Source source;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Source {
        /** 源ip */
        private String ip;
        /** 源端口 */
        private String port;
    }

    private User user;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class User {
        /** 账号 */
        private String name;
    }

    /** 协议 */
    private String protocol;
    /** 访问url */
    private String url;

    /** 源部门名称 */
    @JSONField(name = "src_department_name")
    private String srcDepartmentName;
    /** 源业务系统名称 */
    @JSONField(name = "src_business_name")
    private String srcBusinessName;
    /** 目标部门名称 */
    @JSONField(name = "department_name")
    private String departmentName;
    /** 目标业务系统名称 */
    @JSONField(name = "business_name")
    private String businessName;

}
