package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.ApiNonperiodcall;
import com.wzsec.modules.ic.service.dto.ApiNonperiodcallDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:31+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ApiNonperiodcallMapperImpl implements ApiNonperiodcallMapper {

    @Override
    public ApiNonperiodcall toEntity(ApiNonperiodcallDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiNonperiodcall apiNonperiodcall = new ApiNonperiodcall();

        apiNonperiodcall.setId( dto.getId() );
        apiNonperiodcall.setApicode( dto.getApicode() );
        apiNonperiodcall.setNormalcallperiod( dto.getNormalcallperiod() );
        apiNonperiodcall.setAbnormalcallperiod( dto.getAbnormalcallperiod() );
        apiNonperiodcall.setRisk( dto.getRisk() );
        apiNonperiodcall.setChecktime( dto.getChecktime() );
        apiNonperiodcall.setSparefield1( dto.getSparefield1() );
        apiNonperiodcall.setSparefield2( dto.getSparefield2() );
        apiNonperiodcall.setSparefield3( dto.getSparefield3() );
        apiNonperiodcall.setSparefield4( dto.getSparefield4() );
        apiNonperiodcall.setAk( dto.getAk() );
        apiNonperiodcall.setApplyorgname( dto.getApplyorgname() );
        apiNonperiodcall.setApiname( dto.getApiname() );
        apiNonperiodcall.setApiurl( dto.getApiurl() );
        apiNonperiodcall.setSystemname( dto.getSystemname() );
        apiNonperiodcall.setCallnum( dto.getCallnum() );
        apiNonperiodcall.setReqip( dto.getReqip() );

        return apiNonperiodcall;
    }

    @Override
    public ApiNonperiodcallDto toDto(ApiNonperiodcall entity) {
        if ( entity == null ) {
            return null;
        }

        ApiNonperiodcallDto apiNonperiodcallDto = new ApiNonperiodcallDto();

        apiNonperiodcallDto.setId( entity.getId() );
        apiNonperiodcallDto.setApicode( entity.getApicode() );
        apiNonperiodcallDto.setNormalcallperiod( entity.getNormalcallperiod() );
        apiNonperiodcallDto.setAbnormalcallperiod( entity.getAbnormalcallperiod() );
        apiNonperiodcallDto.setRisk( entity.getRisk() );
        apiNonperiodcallDto.setChecktime( entity.getChecktime() );
        apiNonperiodcallDto.setSparefield1( entity.getSparefield1() );
        apiNonperiodcallDto.setSparefield2( entity.getSparefield2() );
        apiNonperiodcallDto.setSparefield3( entity.getSparefield3() );
        apiNonperiodcallDto.setSparefield4( entity.getSparefield4() );
        apiNonperiodcallDto.setAk( entity.getAk() );
        apiNonperiodcallDto.setApplyorgname( entity.getApplyorgname() );
        apiNonperiodcallDto.setApiname( entity.getApiname() );
        apiNonperiodcallDto.setApiurl( entity.getApiurl() );
        apiNonperiodcallDto.setSystemname( entity.getSystemname() );
        apiNonperiodcallDto.setCallnum( entity.getCallnum() );
        apiNonperiodcallDto.setReqip( entity.getReqip() );

        return apiNonperiodcallDto;
    }

    @Override
    public List<ApiNonperiodcall> toEntity(List<ApiNonperiodcallDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiNonperiodcall> list = new ArrayList<ApiNonperiodcall>( dtoList.size() );
        for ( ApiNonperiodcallDto apiNonperiodcallDto : dtoList ) {
            list.add( toEntity( apiNonperiodcallDto ) );
        }

        return list;
    }

    @Override
    public List<ApiNonperiodcallDto> toDto(List<ApiNonperiodcall> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiNonperiodcallDto> list = new ArrayList<ApiNonperiodcallDto>( entityList.size() );
        for ( ApiNonperiodcall apiNonperiodcall : entityList ) {
            list.add( toDto( apiNonperiodcall ) );
        }

        return list;
    }
}
