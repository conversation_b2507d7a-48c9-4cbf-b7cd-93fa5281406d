package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.ApiNonperiodcall;
import com.wzsec.modules.ic.service.dto.ApiNonperiodcallDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:29+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ApiNonperiodcallMapperImpl implements ApiNonperiodcallMapper {

    @Override
    public ApiNonperiodcallDto toDto(ApiNonperiodcall entity) {
        if ( entity == null ) {
            return null;
        }

        ApiNonperiodcallDto apiNonperiodcallDto = new ApiNonperiodcallDto();

        apiNonperiodcallDto.setAbnormalcallperiod( entity.getAbnormalcallperiod() );
        apiNonperiodcallDto.setAk( entity.getAk() );
        apiNonperiodcallDto.setApicode( entity.getApicode() );
        apiNonperiodcallDto.setApiname( entity.getApiname() );
        apiNonperiodcallDto.setApiurl( entity.getApiurl() );
        apiNonperiodcallDto.setApplyorgname( entity.getApplyorgname() );
        apiNonperiodcallDto.setCallnum( entity.getCallnum() );
        apiNonperiodcallDto.setChecktime( entity.getChecktime() );
        apiNonperiodcallDto.setId( entity.getId() );
        apiNonperiodcallDto.setNormalcallperiod( entity.getNormalcallperiod() );
        apiNonperiodcallDto.setReqip( entity.getReqip() );
        apiNonperiodcallDto.setRisk( entity.getRisk() );
        apiNonperiodcallDto.setSparefield1( entity.getSparefield1() );
        apiNonperiodcallDto.setSparefield2( entity.getSparefield2() );
        apiNonperiodcallDto.setSparefield3( entity.getSparefield3() );
        apiNonperiodcallDto.setSparefield4( entity.getSparefield4() );
        apiNonperiodcallDto.setSystemname( entity.getSystemname() );

        return apiNonperiodcallDto;
    }

    @Override
    public List<ApiNonperiodcallDto> toDto(List<ApiNonperiodcall> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiNonperiodcallDto> list = new ArrayList<ApiNonperiodcallDto>( entityList.size() );
        for ( ApiNonperiodcall apiNonperiodcall : entityList ) {
            list.add( toDto( apiNonperiodcall ) );
        }

        return list;
    }

    @Override
    public ApiNonperiodcall toEntity(ApiNonperiodcallDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiNonperiodcall apiNonperiodcall = new ApiNonperiodcall();

        apiNonperiodcall.setAbnormalcallperiod( dto.getAbnormalcallperiod() );
        apiNonperiodcall.setAk( dto.getAk() );
        apiNonperiodcall.setApicode( dto.getApicode() );
        apiNonperiodcall.setApiname( dto.getApiname() );
        apiNonperiodcall.setApiurl( dto.getApiurl() );
        apiNonperiodcall.setApplyorgname( dto.getApplyorgname() );
        apiNonperiodcall.setCallnum( dto.getCallnum() );
        apiNonperiodcall.setChecktime( dto.getChecktime() );
        apiNonperiodcall.setId( dto.getId() );
        apiNonperiodcall.setNormalcallperiod( dto.getNormalcallperiod() );
        apiNonperiodcall.setReqip( dto.getReqip() );
        apiNonperiodcall.setRisk( dto.getRisk() );
        apiNonperiodcall.setSparefield1( dto.getSparefield1() );
        apiNonperiodcall.setSparefield2( dto.getSparefield2() );
        apiNonperiodcall.setSparefield3( dto.getSparefield3() );
        apiNonperiodcall.setSparefield4( dto.getSparefield4() );
        apiNonperiodcall.setSystemname( dto.getSystemname() );

        return apiNonperiodcall;
    }

    @Override
    public List<ApiNonperiodcall> toEntity(List<ApiNonperiodcallDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiNonperiodcall> list = new ArrayList<ApiNonperiodcall>( dtoList.size() );
        for ( ApiNonperiodcallDto apiNonperiodcallDto : dtoList ) {
            list.add( toEntity( apiNonperiodcallDto ) );
        }

        return list;
    }
}
