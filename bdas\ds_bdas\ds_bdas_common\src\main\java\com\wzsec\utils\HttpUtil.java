package com.wzsec.utils;

import com.alibaba.fastjson.JSONObject;
import com.wzsec.exception.BadRequestException;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.http.HttpMethod;

import javax.net.ssl.SSLContext;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.security.cert.X509Certificate;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * http工具类，引用httpclient工具类，和常量类
 *
 * <AUTHOR> Kunxiang
 * @date 2020-7-13 18:49:47
 */
public class HttpUtil {

    /**
     * 发送请求到引擎(携带token)
     *
     * @param url        引擎地址
     * @param params     参数
     * @param httpMethod 请求方式
     * @param request    请求
     * <AUTHOR> Kunxiang
     * @date 2020-7-13 18:49:47
     */
    public static String sendToEngine(String url, Map<String, String> params, HttpMethod httpMethod, HttpServletRequest request) {
        HttpClientUtils.HttpClientResult httpClientResult = null;
        try {
            // System.out.println("url: " + url);
            Map<String, String> headers = new HashMap<>();
            if (null != request) {
                System.out.println(request.getHeader(Const.Authorization));
                headers.put(Const.Authorization, request.getHeader(Const.Authorization));//设置token
            }
            if (HttpMethod.GET == httpMethod) {
                httpClientResult = HttpClientUtils.doGet(url, headers, params);//发送GET请求
            } else if (HttpMethod.POST == httpMethod) {
                httpClientResult = HttpClientUtils.doPost(url, headers, params);//发送POST请求
            } else if (HttpMethod.PUT == httpMethod) {
                httpClientResult = HttpClientUtils.doPut(url, headers, params);//发送PUT请求
            } else if (HttpMethod.DELETE == httpMethod) {
                httpClientResult = HttpClientUtils.doDelete(url, headers, params);//发送DELETE请求
            } else {
                throw new Exception();
            }
        } catch (Exception e) {
            //e.printStackTrace();
            throw new BadRequestException("提交失败！连接引擎失败！");
        }
        return httpClientResult.getContent();
    }

    /**
     * 发送post请求(支持http和https)
     *
     * @param path   请求地址
     * @param params 参数map
     * @return 响应
     */
    public static String sendHttpsPostRequest(String path, Map<String, String> headers, Map<String, Object> params) {
        String result = "";
        CloseableHttpClient client = createSSLClientDefault();
        CloseableHttpResponse response = null;
        try {
            HttpPost post = new HttpPost(path);
            if (headers != null) {
                for (Map.Entry<String, String> param : headers.entrySet()) {
                    post.addHeader(param.getKey(), param.getValue());
                }
            }

            RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(200000)
                    .setSocketTimeout(200000).build();
            post.setConfig(requestConfig);
            StringEntity entity = new StringEntity(JSONObject.toJSONString(params), ContentType.create("application/json", "utf-8"));
            post.setEntity(entity);
            //post.setHeader("Content-Type","application/json");
            post.setHeader("Content-Type", "application/x-www-form-urlencoded");
            /* StringEntity entity = new StringEntity(JSON.toJSONString(params, SerializerFeature.WriteMapNullValue),
                    "UTF-8");
            entity.setContentEncoding("UTF-8");
            entity.setContentType("application/json");
            post.setEntity(entity);*/
            response = client.execute(post);
            System.out.println("响应状态码：" + response.getStatusLine().getStatusCode());
            HttpEntity httpEntity = response.getEntity();
            System.out.println("服务器端响应的实体：" + httpEntity.toString());
            result = EntityUtils.toString(httpEntity, "UTF-8");
            System.out.println("服务器端响应的数据：" + result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            client.close();
            response.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 发送get请求(支持http和https)
     *
     * @param path   请求地址
     * @param params 参数map
     * @return 响应
     */
    public static String sendHttpsGetRequest(String path, Map<String, String> headers, Map<String, Object> params) {
        String result = "";
        CloseableHttpClient client = createSSLClientDefault();
        CloseableHttpResponse response = null;
        try {
            URIBuilder uri = new URIBuilder(path);
            //get请求带参数
            List<NameValuePair> list = new LinkedList<>();
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                list.add(new BasicNameValuePair(entry.getKey(), entry.getValue().toString()));
            }
            uri.setParameters(list);
            HttpGet get = new HttpGet(uri.build());
            if (headers != null) {
                for (Map.Entry<String, String> param : headers.entrySet()) {
                    get.addHeader(param.getKey(), param.getValue());
                }
            }
            RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(200000)
                    .setSocketTimeout(200000).build();
            get.setConfig(requestConfig);
            response = client.execute(get);

            System.out.println("响应状态码：" + response.getStatusLine().getStatusCode());
            HttpEntity httpEntity = response.getEntity();
            System.out.println("服务器端响应的实体：" + httpEntity.toString());
            result = EntityUtils.toString(httpEntity, "UTF-8");
            System.out.println("服务器端响应的数据：" + result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            client.close();
            response.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result;
    }


    /**
     * @Description: TODO(获取忽略SSL证书的HttpClient)
     * <AUTHOR>
     * @date 2021年11月9日16:21:33
     */
    public static CloseableHttpClient createSSLClientDefault() {
        try {
            SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustStrategy() {
                // 信任所有证书
                public boolean isTrusted(X509Certificate[] chain, String authType) {
                    return true;
                }
            }).build();
            CloseableHttpClient client = HttpClients.custom().setSslcontext(sslContext).
                    setSSLHostnameVerifier(new NoopHostnameVerifier()).build();
            return client;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return HttpClients.createDefault();
    }

}
