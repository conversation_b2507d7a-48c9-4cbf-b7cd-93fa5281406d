package com.wzsec.modules.apiproxy.service.mapper;

import com.wzsec.modules.apiproxy.domain.ApiOutputparamsdetails;
import com.wzsec.modules.apiproxy.service.dto.ApiOutputparamsdetailsDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:30+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ApiOutputparamsdetailsMapperImpl implements ApiOutputparamsdetailsMapper {

    @Override
    public ApiOutputparamsdetails toEntity(ApiOutputparamsdetailsDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiOutputparamsdetails apiOutputparamsdetails = new ApiOutputparamsdetails();

        apiOutputparamsdetails.setId( dto.getId() );
        apiOutputparamsdetails.setInterfaceinfoid( dto.getInterfaceinfoid() );
        apiOutputparamsdetails.setOutputparams( dto.getOutputparams() );
        apiOutputparamsdetails.setOutparammean( dto.getOutparammean() );
        apiOutputparamsdetails.setApiruleid( dto.getApiruleid() );
        apiOutputparamsdetails.setStatus( dto.getStatus() );
        apiOutputparamsdetails.setCreateuser( dto.getCreateuser() );
        apiOutputparamsdetails.setCreatetime( dto.getCreatetime() );
        apiOutputparamsdetails.setUpdateuser( dto.getUpdateuser() );
        apiOutputparamsdetails.setUpdatetime( dto.getUpdatetime() );
        apiOutputparamsdetails.setNote( dto.getNote() );
        apiOutputparamsdetails.setSparefield1( dto.getSparefield1() );
        apiOutputparamsdetails.setSparefield2( dto.getSparefield2() );
        apiOutputparamsdetails.setSparefield3( dto.getSparefield3() );
        apiOutputparamsdetails.setSparefield4( dto.getSparefield4() );
        apiOutputparamsdetails.setSparefield5( dto.getSparefield5() );

        return apiOutputparamsdetails;
    }

    @Override
    public ApiOutputparamsdetailsDto toDto(ApiOutputparamsdetails entity) {
        if ( entity == null ) {
            return null;
        }

        ApiOutputparamsdetailsDto apiOutputparamsdetailsDto = new ApiOutputparamsdetailsDto();

        apiOutputparamsdetailsDto.setId( entity.getId() );
        apiOutputparamsdetailsDto.setInterfaceinfoid( entity.getInterfaceinfoid() );
        apiOutputparamsdetailsDto.setOutputparams( entity.getOutputparams() );
        apiOutputparamsdetailsDto.setOutparammean( entity.getOutparammean() );
        apiOutputparamsdetailsDto.setApiruleid( entity.getApiruleid() );
        apiOutputparamsdetailsDto.setStatus( entity.getStatus() );
        apiOutputparamsdetailsDto.setCreateuser( entity.getCreateuser() );
        apiOutputparamsdetailsDto.setCreatetime( entity.getCreatetime() );
        apiOutputparamsdetailsDto.setUpdateuser( entity.getUpdateuser() );
        apiOutputparamsdetailsDto.setUpdatetime( entity.getUpdatetime() );
        apiOutputparamsdetailsDto.setNote( entity.getNote() );
        apiOutputparamsdetailsDto.setSparefield1( entity.getSparefield1() );
        apiOutputparamsdetailsDto.setSparefield2( entity.getSparefield2() );
        apiOutputparamsdetailsDto.setSparefield3( entity.getSparefield3() );
        apiOutputparamsdetailsDto.setSparefield4( entity.getSparefield4() );
        apiOutputparamsdetailsDto.setSparefield5( entity.getSparefield5() );

        return apiOutputparamsdetailsDto;
    }

    @Override
    public List<ApiOutputparamsdetails> toEntity(List<ApiOutputparamsdetailsDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiOutputparamsdetails> list = new ArrayList<ApiOutputparamsdetails>( dtoList.size() );
        for ( ApiOutputparamsdetailsDto apiOutputparamsdetailsDto : dtoList ) {
            list.add( toEntity( apiOutputparamsdetailsDto ) );
        }

        return list;
    }

    @Override
    public List<ApiOutputparamsdetailsDto> toDto(List<ApiOutputparamsdetails> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiOutputparamsdetailsDto> list = new ArrayList<ApiOutputparamsdetailsDto>( entityList.size() );
        for ( ApiOutputparamsdetails apiOutputparamsdetails : entityList ) {
            list.add( toDto( apiOutputparamsdetails ) );
        }

        return list;
    }
}
