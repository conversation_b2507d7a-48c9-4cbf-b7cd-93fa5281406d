package com.wzsec.modules.apiproxy.service.mapper;

import com.wzsec.modules.apiproxy.domain.ApiOutputparamsdetails;
import com.wzsec.modules.apiproxy.service.dto.ApiOutputparamsdetailsDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:32+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ApiOutputparamsdetailsMapperImpl implements ApiOutputparamsdetailsMapper {

    @Override
    public ApiOutputparamsdetailsDto toDto(ApiOutputparamsdetails entity) {
        if ( entity == null ) {
            return null;
        }

        ApiOutputparamsdetailsDto apiOutputparamsdetailsDto = new ApiOutputparamsdetailsDto();

        apiOutputparamsdetailsDto.setApiruleid( entity.getApiruleid() );
        apiOutputparamsdetailsDto.setCreatetime( entity.getCreatetime() );
        apiOutputparamsdetailsDto.setCreateuser( entity.getCreateuser() );
        apiOutputparamsdetailsDto.setId( entity.getId() );
        apiOutputparamsdetailsDto.setInterfaceinfoid( entity.getInterfaceinfoid() );
        apiOutputparamsdetailsDto.setNote( entity.getNote() );
        apiOutputparamsdetailsDto.setOutparammean( entity.getOutparammean() );
        apiOutputparamsdetailsDto.setOutputparams( entity.getOutputparams() );
        apiOutputparamsdetailsDto.setSparefield1( entity.getSparefield1() );
        apiOutputparamsdetailsDto.setSparefield2( entity.getSparefield2() );
        apiOutputparamsdetailsDto.setSparefield3( entity.getSparefield3() );
        apiOutputparamsdetailsDto.setSparefield4( entity.getSparefield4() );
        apiOutputparamsdetailsDto.setSparefield5( entity.getSparefield5() );
        apiOutputparamsdetailsDto.setStatus( entity.getStatus() );
        apiOutputparamsdetailsDto.setUpdatetime( entity.getUpdatetime() );
        apiOutputparamsdetailsDto.setUpdateuser( entity.getUpdateuser() );

        return apiOutputparamsdetailsDto;
    }

    @Override
    public List<ApiOutputparamsdetailsDto> toDto(List<ApiOutputparamsdetails> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiOutputparamsdetailsDto> list = new ArrayList<ApiOutputparamsdetailsDto>( entityList.size() );
        for ( ApiOutputparamsdetails apiOutputparamsdetails : entityList ) {
            list.add( toDto( apiOutputparamsdetails ) );
        }

        return list;
    }

    @Override
    public ApiOutputparamsdetails toEntity(ApiOutputparamsdetailsDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiOutputparamsdetails apiOutputparamsdetails = new ApiOutputparamsdetails();

        apiOutputparamsdetails.setApiruleid( dto.getApiruleid() );
        apiOutputparamsdetails.setCreatetime( dto.getCreatetime() );
        apiOutputparamsdetails.setCreateuser( dto.getCreateuser() );
        apiOutputparamsdetails.setId( dto.getId() );
        apiOutputparamsdetails.setInterfaceinfoid( dto.getInterfaceinfoid() );
        apiOutputparamsdetails.setNote( dto.getNote() );
        apiOutputparamsdetails.setOutparammean( dto.getOutparammean() );
        apiOutputparamsdetails.setOutputparams( dto.getOutputparams() );
        apiOutputparamsdetails.setSparefield1( dto.getSparefield1() );
        apiOutputparamsdetails.setSparefield2( dto.getSparefield2() );
        apiOutputparamsdetails.setSparefield3( dto.getSparefield3() );
        apiOutputparamsdetails.setSparefield4( dto.getSparefield4() );
        apiOutputparamsdetails.setSparefield5( dto.getSparefield5() );
        apiOutputparamsdetails.setStatus( dto.getStatus() );
        apiOutputparamsdetails.setUpdatetime( dto.getUpdatetime() );
        apiOutputparamsdetails.setUpdateuser( dto.getUpdateuser() );

        return apiOutputparamsdetails;
    }

    @Override
    public List<ApiOutputparamsdetails> toEntity(List<ApiOutputparamsdetailsDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiOutputparamsdetails> list = new ArrayList<ApiOutputparamsdetails>( dtoList.size() );
        for ( ApiOutputparamsdetailsDto apiOutputparamsdetailsDto : dtoList ) {
            list.add( toEntity( apiOutputparamsdetailsDto ) );
        }

        return list;
    }
}
