package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcInterfaceInfo;
import com.wzsec.modules.ic.service.dto.IcInterfaceInfoDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:31+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class IcInterfaceInfoMapperImpl implements IcInterfaceInfoMapper {

    @Override
    public IcInterfaceInfo toEntity(IcInterfaceInfoDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcInterfaceInfo icInterfaceInfo = new IcInterfaceInfo();

        icInterfaceInfo.setId( dto.getId() );
        icInterfaceInfo.setApicode( dto.getApicode() );
        icInterfaceInfo.setApiname( dto.getApiname() );
        icInterfaceInfo.setApimethod( dto.getApimethod() );
        icInterfaceInfo.setApides( dto.getApides() );
        icInterfaceInfo.setUrl( dto.getUrl() );
        icInterfaceInfo.setInputparams( dto.getInputparams() );
        icInterfaceInfo.setInparamMean( dto.getInparamMean() );
        icInterfaceInfo.setReqExample( dto.getReqExample() );
        icInterfaceInfo.setOutputparams( dto.getOutputparams() );
        icInterfaceInfo.setOutparamMean( dto.getOutparamMean() );
        icInterfaceInfo.setReturnExample( dto.getReturnExample() );
        icInterfaceInfo.setFailreturn( dto.getFailreturn() );
        icInterfaceInfo.setDataFormat( dto.getDataFormat() );
        icInterfaceInfo.setImportance( dto.getImportance() );
        icInterfaceInfo.setInvokeType( dto.getInvokeType() );
        icInterfaceInfo.setInterfaceStatus( dto.getInterfaceStatus() );
        icInterfaceInfo.setDeleted( dto.getDeleted() );
        icInterfaceInfo.setArea( dto.getArea() );
        icInterfaceInfo.setApp( dto.getApp() );
        icInterfaceInfo.setResourceid( dto.getResourceid() );
        icInterfaceInfo.setDataSource( dto.getDataSource() );
        icInterfaceInfo.setDataSourceMoziOrgId( dto.getDataSourceMoziOrgId() );
        icInterfaceInfo.setRegisterUnit( dto.getRegisterUnit() );
        icInterfaceInfo.setRegisterUnitMoziOrgId( dto.getRegisterUnitMoziOrgId() );
        icInterfaceInfo.setLinkMan( dto.getLinkMan() );
        icInterfaceInfo.setLinkTel( dto.getLinkTel() );
        icInterfaceInfo.setCreateuser( dto.getCreateuser() );
        icInterfaceInfo.setCreatetime( dto.getCreatetime() );
        icInterfaceInfo.setUpdateuser( dto.getUpdateuser() );
        icInterfaceInfo.setUpdatetime( dto.getUpdatetime() );
        icInterfaceInfo.setNote( dto.getNote() );
        icInterfaceInfo.setSparefield1( dto.getSparefield1() );
        icInterfaceInfo.setSparefield2( dto.getSparefield2() );
        icInterfaceInfo.setSparefield3( dto.getSparefield3() );
        icInterfaceInfo.setSparefield4( dto.getSparefield4() );
        icInterfaceInfo.setApitype( dto.getApitype() );
        icInterfaceInfo.setApiuse( dto.getApiuse() );
        icInterfaceInfo.setApicomponenttype( dto.getApicomponenttype() );
        icInterfaceInfo.setApiip( dto.getApiip() );
        icInterfaceInfo.setApiport( dto.getApiport() );
        icInterfaceInfo.setLabels( dto.getLabels() );
        icInterfaceInfo.setSyncstate( dto.getSyncstate() );
        icInterfaceInfo.setLogsign( dto.getLogsign() );

        return icInterfaceInfo;
    }

    @Override
    public IcInterfaceInfoDto toDto(IcInterfaceInfo entity) {
        if ( entity == null ) {
            return null;
        }

        IcInterfaceInfoDto icInterfaceInfoDto = new IcInterfaceInfoDto();

        icInterfaceInfoDto.setId( entity.getId() );
        icInterfaceInfoDto.setApicode( entity.getApicode() );
        icInterfaceInfoDto.setApiname( entity.getApiname() );
        icInterfaceInfoDto.setApimethod( entity.getApimethod() );
        icInterfaceInfoDto.setApides( entity.getApides() );
        icInterfaceInfoDto.setUrl( entity.getUrl() );
        icInterfaceInfoDto.setInputparams( entity.getInputparams() );
        icInterfaceInfoDto.setInparamMean( entity.getInparamMean() );
        icInterfaceInfoDto.setReqExample( entity.getReqExample() );
        icInterfaceInfoDto.setOutputparams( entity.getOutputparams() );
        icInterfaceInfoDto.setOutparamMean( entity.getOutparamMean() );
        icInterfaceInfoDto.setReturnExample( entity.getReturnExample() );
        icInterfaceInfoDto.setFailreturn( entity.getFailreturn() );
        icInterfaceInfoDto.setDataFormat( entity.getDataFormat() );
        icInterfaceInfoDto.setImportance( entity.getImportance() );
        icInterfaceInfoDto.setInvokeType( entity.getInvokeType() );
        icInterfaceInfoDto.setInterfaceStatus( entity.getInterfaceStatus() );
        icInterfaceInfoDto.setDeleted( entity.getDeleted() );
        icInterfaceInfoDto.setArea( entity.getArea() );
        icInterfaceInfoDto.setApp( entity.getApp() );
        icInterfaceInfoDto.setResourceid( entity.getResourceid() );
        icInterfaceInfoDto.setDataSource( entity.getDataSource() );
        icInterfaceInfoDto.setDataSourceMoziOrgId( entity.getDataSourceMoziOrgId() );
        icInterfaceInfoDto.setRegisterUnit( entity.getRegisterUnit() );
        icInterfaceInfoDto.setRegisterUnitMoziOrgId( entity.getRegisterUnitMoziOrgId() );
        icInterfaceInfoDto.setLinkMan( entity.getLinkMan() );
        icInterfaceInfoDto.setLinkTel( entity.getLinkTel() );
        icInterfaceInfoDto.setCreateuser( entity.getCreateuser() );
        icInterfaceInfoDto.setCreatetime( entity.getCreatetime() );
        icInterfaceInfoDto.setUpdateuser( entity.getUpdateuser() );
        icInterfaceInfoDto.setUpdatetime( entity.getUpdatetime() );
        icInterfaceInfoDto.setNote( entity.getNote() );
        icInterfaceInfoDto.setSparefield1( entity.getSparefield1() );
        icInterfaceInfoDto.setSparefield2( entity.getSparefield2() );
        icInterfaceInfoDto.setSparefield3( entity.getSparefield3() );
        icInterfaceInfoDto.setSparefield4( entity.getSparefield4() );
        icInterfaceInfoDto.setApitype( entity.getApitype() );
        icInterfaceInfoDto.setApiuse( entity.getApiuse() );
        icInterfaceInfoDto.setApicomponenttype( entity.getApicomponenttype() );
        icInterfaceInfoDto.setApiip( entity.getApiip() );
        icInterfaceInfoDto.setApiport( entity.getApiport() );
        icInterfaceInfoDto.setLabels( entity.getLabels() );
        icInterfaceInfoDto.setSyncstate( entity.getSyncstate() );
        icInterfaceInfoDto.setLogsign( entity.getLogsign() );

        return icInterfaceInfoDto;
    }

    @Override
    public List<IcInterfaceInfo> toEntity(List<IcInterfaceInfoDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcInterfaceInfo> list = new ArrayList<IcInterfaceInfo>( dtoList.size() );
        for ( IcInterfaceInfoDto icInterfaceInfoDto : dtoList ) {
            list.add( toEntity( icInterfaceInfoDto ) );
        }

        return list;
    }

    @Override
    public List<IcInterfaceInfoDto> toDto(List<IcInterfaceInfo> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcInterfaceInfoDto> list = new ArrayList<IcInterfaceInfoDto>( entityList.size() );
        for ( IcInterfaceInfo icInterfaceInfo : entityList ) {
            list.add( toDto( icInterfaceInfo ) );
        }

        return list;
    }
}
