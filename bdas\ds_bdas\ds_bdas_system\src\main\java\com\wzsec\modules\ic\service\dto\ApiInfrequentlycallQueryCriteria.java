package com.wzsec.modules.ic.service.dto;

import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

import com.wzsec.annotation.Query;

/**
 * <AUTHOR>
 * @date 2022-05-19
 */
@Data
public class ApiInfrequentlycallQueryCriteria {

    /** 支持精确查询和IN查询：单个值时精确查询，逗号分隔字符串或List时IN查询 */
    @Query(type = Query.Type.IN)
    private Object ak;

    private Timestamp dateStart;

    private Timestamp dateEnd;

    @Query(type = Query.Type.IN)
    private List<String> sparefield4;

    @Query
    private String apicode;

    @Query
    private String risk;

    @Query(type = Query.Type.BETWEEN)
    private List<String> checktime;

    /**
     * 模糊
     */
    @Query(type = Query.Type.INNER_LIKE)
    private String sparefield3;

    @Query(blurry = "apicode,risk,sparefield3")
    private String blurry;
}
