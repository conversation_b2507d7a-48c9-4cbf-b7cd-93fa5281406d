package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcTaskrecords;
import com.wzsec.modules.ic.service.dto.IcTaskrecordsDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:27:08+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class IcTaskrecordsMapperImpl implements IcTaskrecordsMapper {

    @Override
    public IcTaskrecordsDto toDto(IcTaskrecords entity) {
        if ( entity == null ) {
            return null;
        }

        IcTaskrecordsDto icTaskrecordsDto = new IcTaskrecordsDto();

        icTaskrecordsDto.setCreatetime( entity.getCreatetime() );
        icTaskrecordsDto.setCreateuser( entity.getCreateuser() );
        icTaskrecordsDto.setEndtime( entity.getEndtime() );
        icTaskrecordsDto.setExecutionstate( entity.getExecutionstate() );
        icTaskrecordsDto.setId( entity.getId() );
        icTaskrecordsDto.setSparefield1( entity.getSparefield1() );
        icTaskrecordsDto.setSparefield2( entity.getSparefield2() );
        icTaskrecordsDto.setSparefield3( entity.getSparefield3() );
        icTaskrecordsDto.setSparefield4( entity.getSparefield4() );
        icTaskrecordsDto.setStarttime( entity.getStarttime() );
        icTaskrecordsDto.setSubmitmethod( entity.getSubmitmethod() );
        icTaskrecordsDto.setTaskname( entity.getTaskname() );
        icTaskrecordsDto.setTasktype( entity.getTasktype() );

        return icTaskrecordsDto;
    }

    @Override
    public List<IcTaskrecordsDto> toDto(List<IcTaskrecords> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcTaskrecordsDto> list = new ArrayList<IcTaskrecordsDto>( entityList.size() );
        for ( IcTaskrecords icTaskrecords : entityList ) {
            list.add( toDto( icTaskrecords ) );
        }

        return list;
    }

    @Override
    public IcTaskrecords toEntity(IcTaskrecordsDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcTaskrecords icTaskrecords = new IcTaskrecords();

        icTaskrecords.setCreatetime( dto.getCreatetime() );
        icTaskrecords.setCreateuser( dto.getCreateuser() );
        icTaskrecords.setEndtime( dto.getEndtime() );
        icTaskrecords.setExecutionstate( dto.getExecutionstate() );
        icTaskrecords.setId( dto.getId() );
        icTaskrecords.setSparefield1( dto.getSparefield1() );
        icTaskrecords.setSparefield2( dto.getSparefield2() );
        icTaskrecords.setSparefield3( dto.getSparefield3() );
        icTaskrecords.setSparefield4( dto.getSparefield4() );
        icTaskrecords.setStarttime( dto.getStarttime() );
        icTaskrecords.setSubmitmethod( dto.getSubmitmethod() );
        icTaskrecords.setTaskname( dto.getTaskname() );
        icTaskrecords.setTasktype( dto.getTasktype() );

        return icTaskrecords;
    }

    @Override
    public List<IcTaskrecords> toEntity(List<IcTaskrecordsDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcTaskrecords> list = new ArrayList<IcTaskrecords>( dtoList.size() );
        for ( IcTaskrecordsDto icTaskrecordsDto : dtoList ) {
            list.add( toEntity( icTaskrecordsDto ) );
        }

        return list;
    }
}
