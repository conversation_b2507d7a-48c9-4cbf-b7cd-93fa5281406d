package com.wzsec.modules.ic.service.dto;

import com.wzsec.annotation.Query;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-04-25
 */
@Data
public class IcResultDetailQueryCriteria {

    /** 支持精确查询和IN查询：单个值时精确查询，逗号分隔字符串或List时IN查询 */
    @Query(type = Query.Type.IN)
    private Object ak;

    @Query
    private String risk;

    @Query
    private String resulttype;

    @Query(type = Query.Type.IN)
    private List<String> taskname;

    @Query
    private String apicode;

    @Query(type = Query.Type.BETWEEN)
    private List<Timestamp> checktime;

    @Query(blurry = "apicode,logsign,apiname,risk,resulttype,sensitivedata,ak,applyorgname,apiurl,akapicode")
    private String blurry;
}
