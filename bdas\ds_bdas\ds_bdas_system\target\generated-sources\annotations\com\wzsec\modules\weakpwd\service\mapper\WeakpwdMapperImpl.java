package com.wzsec.modules.weakpwd.service.mapper;

import com.wzsec.modules.weakpwd.domain.Weakpwd;
import com.wzsec.modules.weakpwd.service.dto.WeakpwdDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:32+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class WeakpwdMapperImpl implements WeakpwdMapper {

    @Override
    public WeakpwdDto toDto(Weakpwd entity) {
        if ( entity == null ) {
            return null;
        }

        WeakpwdDto weakpwdDto = new WeakpwdDto();

        weakpwdDto.setCiphertext( entity.getCiphertext() );
        weakpwdDto.setCleartext( entity.getCleartext() );
        weakpwdDto.setEnctype( entity.getEnctype() );
        weakpwdDto.setId( entity.getId() );
        weakpwdDto.setInserttime( entity.getInserttime() );
        weakpwdDto.setPrivatekey( entity.getPrivatekey() );
        weakpwdDto.setSparefield1( entity.getSparefield1() );
        weakpwdDto.setSparefield2( entity.getSparefield2() );
        weakpwdDto.setSparefield3( entity.getSparefield3() );
        weakpwdDto.setSparefield4( entity.getSparefield4() );
        weakpwdDto.setSparefield5( entity.getSparefield5() );

        return weakpwdDto;
    }

    @Override
    public List<WeakpwdDto> toDto(List<Weakpwd> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<WeakpwdDto> list = new ArrayList<WeakpwdDto>( entityList.size() );
        for ( Weakpwd weakpwd : entityList ) {
            list.add( toDto( weakpwd ) );
        }

        return list;
    }

    @Override
    public Weakpwd toEntity(WeakpwdDto dto) {
        if ( dto == null ) {
            return null;
        }

        Weakpwd weakpwd = new Weakpwd();

        weakpwd.setCiphertext( dto.getCiphertext() );
        weakpwd.setCleartext( dto.getCleartext() );
        weakpwd.setEnctype( dto.getEnctype() );
        weakpwd.setId( dto.getId() );
        weakpwd.setInserttime( dto.getInserttime() );
        weakpwd.setPrivatekey( dto.getPrivatekey() );
        weakpwd.setSparefield1( dto.getSparefield1() );
        weakpwd.setSparefield2( dto.getSparefield2() );
        weakpwd.setSparefield3( dto.getSparefield3() );
        weakpwd.setSparefield4( dto.getSparefield4() );
        weakpwd.setSparefield5( dto.getSparefield5() );

        return weakpwd;
    }

    @Override
    public List<Weakpwd> toEntity(List<WeakpwdDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Weakpwd> list = new ArrayList<Weakpwd>( dtoList.size() );
        for ( WeakpwdDto weakpwdDto : dtoList ) {
            list.add( toEntity( weakpwdDto ) );
        }

        return list;
    }
}
