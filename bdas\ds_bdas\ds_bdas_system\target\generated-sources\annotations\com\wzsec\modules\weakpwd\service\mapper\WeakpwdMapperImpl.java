package com.wzsec.modules.weakpwd.service.mapper;

import com.wzsec.modules.weakpwd.domain.Weakpwd;
import com.wzsec.modules.weakpwd.service.dto.WeakpwdDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:30+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class WeakpwdMapperImpl implements WeakpwdMapper {

    @Override
    public Weakpwd toEntity(WeakpwdDto dto) {
        if ( dto == null ) {
            return null;
        }

        Weakpwd weakpwd = new Weakpwd();

        weakpwd.setId( dto.getId() );
        weakpwd.setCleartext( dto.getCleartext() );
        weakpwd.setEnctype( dto.getEnctype() );
        weakpwd.setCiphertext( dto.getCiphertext() );
        weakpwd.setPrivatekey( dto.getPrivatekey() );
        weakpwd.setSparefield1( dto.getSparefield1() );
        weakpwd.setSparefield2( dto.getSparefield2() );
        weakpwd.setSparefield3( dto.getSparefield3() );
        weakpwd.setSparefield4( dto.getSparefield4() );
        weakpwd.setSparefield5( dto.getSparefield5() );
        weakpwd.setInserttime( dto.getInserttime() );

        return weakpwd;
    }

    @Override
    public WeakpwdDto toDto(Weakpwd entity) {
        if ( entity == null ) {
            return null;
        }

        WeakpwdDto weakpwdDto = new WeakpwdDto();

        weakpwdDto.setId( entity.getId() );
        weakpwdDto.setCleartext( entity.getCleartext() );
        weakpwdDto.setEnctype( entity.getEnctype() );
        weakpwdDto.setCiphertext( entity.getCiphertext() );
        weakpwdDto.setPrivatekey( entity.getPrivatekey() );
        weakpwdDto.setSparefield1( entity.getSparefield1() );
        weakpwdDto.setSparefield2( entity.getSparefield2() );
        weakpwdDto.setSparefield3( entity.getSparefield3() );
        weakpwdDto.setSparefield4( entity.getSparefield4() );
        weakpwdDto.setSparefield5( entity.getSparefield5() );
        weakpwdDto.setInserttime( entity.getInserttime() );

        return weakpwdDto;
    }

    @Override
    public List<Weakpwd> toEntity(List<WeakpwdDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Weakpwd> list = new ArrayList<Weakpwd>( dtoList.size() );
        for ( WeakpwdDto weakpwdDto : dtoList ) {
            list.add( toEntity( weakpwdDto ) );
        }

        return list;
    }

    @Override
    public List<WeakpwdDto> toDto(List<Weakpwd> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<WeakpwdDto> list = new ArrayList<WeakpwdDto>( entityList.size() );
        for ( Weakpwd weakpwd : entityList ) {
            list.add( toDto( weakpwd ) );
        }

        return list;
    }
}
