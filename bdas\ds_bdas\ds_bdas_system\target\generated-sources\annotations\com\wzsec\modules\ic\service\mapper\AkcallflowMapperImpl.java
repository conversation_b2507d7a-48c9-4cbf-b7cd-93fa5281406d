package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.Akcallflow;
import com.wzsec.modules.ic.service.dto.AkcallflowDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:30+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class AkcallflowMapperImpl implements AkcallflowMapper {

    @Override
    public Akcallflow toEntity(AkcallflowDto dto) {
        if ( dto == null ) {
            return null;
        }

        Akcallflow akcallflow = new Akcallflow();

        akcallflow.setId( dto.getId() );
        akcallflow.setAk( dto.getAk() );
        akcallflow.setCallnum( dto.getCallnum() );
        akcallflow.setApplyorgname( dto.getApplyorgname() );
        akcallflow.setLevel1count( dto.getLevel1count() );
        akcallflow.setLevel2count( dto.getLevel2count() );
        akcallflow.setLevel3count( dto.getLevel3count() );
        akcallflow.setLevel4count( dto.getLevel4count() );
        akcallflow.setLevel1statistics( dto.getLevel1statistics() );
        akcallflow.setLevel2statistics( dto.getLevel2statistics() );
        akcallflow.setLevel3statistics( dto.getLevel3statistics() );
        akcallflow.setLevel4statistics( dto.getLevel4statistics() );
        akcallflow.setCreatetime( dto.getCreatetime() );
        akcallflow.setSparefield1( dto.getSparefield1() );
        akcallflow.setSparefield2( dto.getSparefield2() );
        akcallflow.setSparefield3( dto.getSparefield3() );
        akcallflow.setSparefield4( dto.getSparefield4() );
        akcallflow.setSparefield5( dto.getSparefield5() );

        return akcallflow;
    }

    @Override
    public AkcallflowDto toDto(Akcallflow entity) {
        if ( entity == null ) {
            return null;
        }

        AkcallflowDto akcallflowDto = new AkcallflowDto();

        akcallflowDto.setId( entity.getId() );
        akcallflowDto.setAk( entity.getAk() );
        akcallflowDto.setCallnum( entity.getCallnum() );
        akcallflowDto.setApplyorgname( entity.getApplyorgname() );
        akcallflowDto.setLevel1count( entity.getLevel1count() );
        akcallflowDto.setLevel2count( entity.getLevel2count() );
        akcallflowDto.setLevel3count( entity.getLevel3count() );
        akcallflowDto.setLevel4count( entity.getLevel4count() );
        akcallflowDto.setLevel1statistics( entity.getLevel1statistics() );
        akcallflowDto.setLevel2statistics( entity.getLevel2statistics() );
        akcallflowDto.setLevel3statistics( entity.getLevel3statistics() );
        akcallflowDto.setLevel4statistics( entity.getLevel4statistics() );
        akcallflowDto.setCreatetime( entity.getCreatetime() );
        akcallflowDto.setSparefield1( entity.getSparefield1() );
        akcallflowDto.setSparefield2( entity.getSparefield2() );
        akcallflowDto.setSparefield3( entity.getSparefield3() );
        akcallflowDto.setSparefield4( entity.getSparefield4() );
        akcallflowDto.setSparefield5( entity.getSparefield5() );

        return akcallflowDto;
    }

    @Override
    public List<Akcallflow> toEntity(List<AkcallflowDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Akcallflow> list = new ArrayList<Akcallflow>( dtoList.size() );
        for ( AkcallflowDto akcallflowDto : dtoList ) {
            list.add( toEntity( akcallflowDto ) );
        }

        return list;
    }

    @Override
    public List<AkcallflowDto> toDto(List<Akcallflow> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<AkcallflowDto> list = new ArrayList<AkcallflowDto>( entityList.size() );
        for ( Akcallflow akcallflow : entityList ) {
            list.add( toDto( akcallflow ) );
        }

        return list;
    }
}
