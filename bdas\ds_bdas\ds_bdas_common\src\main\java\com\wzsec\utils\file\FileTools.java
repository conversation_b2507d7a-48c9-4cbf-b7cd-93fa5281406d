package com.wzsec.utils.file;

import org.dom4j.DocumentException;
import org.dom4j.io.SAXReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.w3c.dom.Document;
import org.w3c.dom.NodeList;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.*;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @ClassName: FileTools
 * @Description: 文件操作工具类
 */
public class FileTools {

    private final static Logger logger = LoggerFactory.getLogger(FileTools.class);

    /**
     * 读取Xml文件value中的内容,解析文件内容到list
     *
     * @param filePath
     */
    public static String readXml(String filePath) {
        StringBuffer txtStr = new StringBuffer();
        SAXReader read = new SAXReader();
        File file = new File(filePath);
        try {
            org.dom4j.Document document = read.read(file);

            org.dom4j.Element root = document.getRootElement();

            for (Iterator<org.dom4j.Element> iterator = root.elementIterator(); iterator.hasNext(); ) {
                org.dom4j.Element e1 = iterator.next();

                for (Iterator<org.dom4j.Element> iterator2 = e1.elementIterator(); iterator2.hasNext(); ) {
                    org.dom4j.Element e2 = iterator2.next();
                    try {
                        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
                        DocumentBuilder builder = factory.newDocumentBuilder();
                        Document doc = builder.parse(file);
                        //获取一级名称
                        NodeList nl = doc.getElementsByTagName(e1.getName());
                        for (int i = 0; i < nl.getLength(); i++) {
                            //获取二级名称内容
                            String value = doc.getElementsByTagName(e2.getName()).item(i).getFirstChild().getNodeValue();
                            txtStr.append(value).append("\n");
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        } catch (DocumentException e) {
            e.printStackTrace();
        }
        return txtStr.toString();
    }

    /**
     * 读取Xml文件value中的内容,解析文件内容到list
     *
     * @param filePath
     */
    public static String readHtml(String filePath) {
        File file = new File(filePath);
        StringBuffer htmlStr1 = new StringBuffer();
        BufferedReader reader = null;
        String textStr = "";
        try {
            reader = new BufferedReader(new InputStreamReader(new FileInputStream(file), TxtUtil.getFileCharset(file)));
            while (reader.ready()) {
                htmlStr1.append(reader.readLine()).append("\n");
            }
            String htmlStr = htmlStr1.toString(); //含html标签的字符串
            Pattern p_script;
            Matcher m_script;
            Pattern p_style;
            Matcher m_style;
            Pattern p_html;
            Matcher m_html;
            String regEx_script = "<[\\s]*?script[^>]*?>[\\s\\S]*?<[\\s]*?\\/[\\s]*?script[\\s]*?>"; //定义script的正则表达式{或<script[^>]*?>[\\s\\S]*?<\\/script> }
            String regEx_style = "<[\\s]*?style[^>]*?>[\\s\\S]*?<[\\s]*?\\/[\\s]*?style[\\s]*?>"; //定义style的正则表达式{或<style[^>]*?>[\\s\\S]*?<\\/style> }
            String regEx_html = "<[^>]+>"; //定义HTML标签的正则表达式
            p_script = Pattern.compile(regEx_script, Pattern.CASE_INSENSITIVE);
            m_script = p_script.matcher(htmlStr);
            htmlStr = m_script.replaceAll(""); //过滤script标签
            p_style = Pattern.compile(regEx_style, Pattern.CASE_INSENSITIVE);
            m_style = p_style.matcher(htmlStr);
            htmlStr = m_style.replaceAll(""); //过滤style标签
            p_html = Pattern.compile(regEx_html, Pattern.CASE_INSENSITIVE);
            m_html = p_html.matcher(htmlStr);
            htmlStr = m_html.replaceAll(""); //过滤html标签
            textStr = htmlStr;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return textStr;
    }

    /**
     * 读取json文件
     *
     * @param jsonFile json文件名
     * @return 返回json字符串
     */
    public static List<String> readJson(File jsonFile) {
        String jsonStr = "";
        logger.info("————开始读取" + jsonFile.getPath() + "文件————");
        try {
            //File jsonFile = new File(fileName);
            FileReader fileReader = new FileReader(jsonFile);
            Reader reader = new InputStreamReader(new FileInputStream(jsonFile), "utf-8");
            int ch = 0;
            StringBuffer sb = new StringBuffer();
            while ((ch = reader.read()) != -1) {
                sb.append((char) ch);
            }
            fileReader.close();
            reader.close();
            jsonStr = sb.toString();
            logger.info("————读取" + jsonFile.getPath() + "文件结束!————");
            return Collections.singletonList(jsonStr);
        } catch (Exception e) {
            logger.info("————读取" + jsonFile.getPath() + "文件出现异常，读取失败!————");
            e.printStackTrace();
            return null;
        }
    }

    public static void main(String[] args) {
        String filePath = "E:\\test0928\\XML.xml";
        String fileStr = readXml(filePath);
        List<String> documentList = new ArrayList<>();
        if (fileStr != null || fileStr.length() > 0) {
            String[] split = fileStr.split("。|，|\\s+|\r|\\r\\n|,|；|:|：|;|\n|\t|=|'|\"|’|‘|“|”|（|）|\\{|}|[|]|\\(|\\)|！|#|￥|……|&|\\*|!|\\$|\\^");
            for (String s : split) {
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(s)) {
                    documentList.add(s.trim());
                }
            }
        }
        System.out.println(documentList);
    }
}
