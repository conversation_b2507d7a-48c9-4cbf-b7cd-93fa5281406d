package com.wzsec.modules.oc.service.mapper;

import com.wzsec.modules.oc.domain.OcHdfsResult;
import com.wzsec.modules.oc.service.dto.OcHdfsResultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:30+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class OcHdfsResultMapperImpl implements OcHdfsResultMapper {

    @Override
    public OcHdfsResult toEntity(OcHdfsResultDto dto) {
        if ( dto == null ) {
            return null;
        }

        OcHdfsResult ocHdfsResult = new OcHdfsResult();

        ocHdfsResult.setId( dto.getId() );
        ocHdfsResult.setTaskname( dto.getTaskname() );
        ocHdfsResult.setOperationtime( dto.getOperationtime() );
        ocHdfsResult.setUsername( dto.getUsername() );
        ocHdfsResult.setIpaddress( dto.getIpaddress() );
        ocHdfsResult.setPath( dto.getPath() );
        ocHdfsResult.setOperationtype( dto.getOperationtype() );
        ocHdfsResult.setOperation( dto.getOperation() );
        ocHdfsResult.setRisk( dto.getRisk() );
        ocHdfsResult.setChecktime( dto.getChecktime() );
        ocHdfsResult.setSparefield1( dto.getSparefield1() );
        ocHdfsResult.setSparefield2( dto.getSparefield2() );
        ocHdfsResult.setSparefield3( dto.getSparefield3() );
        ocHdfsResult.setSparefield4( dto.getSparefield4() );

        return ocHdfsResult;
    }

    @Override
    public OcHdfsResultDto toDto(OcHdfsResult entity) {
        if ( entity == null ) {
            return null;
        }

        OcHdfsResultDto ocHdfsResultDto = new OcHdfsResultDto();

        ocHdfsResultDto.setId( entity.getId() );
        ocHdfsResultDto.setTaskname( entity.getTaskname() );
        ocHdfsResultDto.setOperationtime( entity.getOperationtime() );
        ocHdfsResultDto.setUsername( entity.getUsername() );
        ocHdfsResultDto.setIpaddress( entity.getIpaddress() );
        ocHdfsResultDto.setPath( entity.getPath() );
        ocHdfsResultDto.setOperationtype( entity.getOperationtype() );
        ocHdfsResultDto.setOperation( entity.getOperation() );
        ocHdfsResultDto.setRisk( entity.getRisk() );
        ocHdfsResultDto.setChecktime( entity.getChecktime() );
        ocHdfsResultDto.setSparefield1( entity.getSparefield1() );
        ocHdfsResultDto.setSparefield2( entity.getSparefield2() );
        ocHdfsResultDto.setSparefield3( entity.getSparefield3() );
        ocHdfsResultDto.setSparefield4( entity.getSparefield4() );

        return ocHdfsResultDto;
    }

    @Override
    public List<OcHdfsResult> toEntity(List<OcHdfsResultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<OcHdfsResult> list = new ArrayList<OcHdfsResult>( dtoList.size() );
        for ( OcHdfsResultDto ocHdfsResultDto : dtoList ) {
            list.add( toEntity( ocHdfsResultDto ) );
        }

        return list;
    }

    @Override
    public List<OcHdfsResultDto> toDto(List<OcHdfsResult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<OcHdfsResultDto> list = new ArrayList<OcHdfsResultDto>( entityList.size() );
        for ( OcHdfsResult ocHdfsResult : entityList ) {
            list.add( toDto( ocHdfsResult ) );
        }

        return list;
    }
}
