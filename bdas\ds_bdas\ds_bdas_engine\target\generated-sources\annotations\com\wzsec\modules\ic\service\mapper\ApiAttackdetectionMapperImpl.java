package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.ApiAttackdetection;
import com.wzsec.modules.ic.service.dto.ApiAttackdetectionDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:27:08+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ApiAttackdetectionMapperImpl implements ApiAttackdetectionMapper {

    @Override
    public ApiAttackdetectionDto toDto(ApiAttackdetection entity) {
        if ( entity == null ) {
            return null;
        }

        ApiAttackdetectionDto apiAttackdetectionDto = new ApiAttackdetectionDto();

        apiAttackdetectionDto.setAk( entity.getAk() );
        apiAttackdetectionDto.setAkapicode( entity.getAkapicode() );
        apiAttackdetectionDto.setApicode( entity.getApicode() );
        apiAttackdetectionDto.setApiname( entity.getApiname() );
        apiAttackdetectionDto.setApplyorgname( entity.getApplyorgname() );
        apiAttackdetectionDto.setAttacktype( entity.getAttacktype() );
        apiAttackdetectionDto.setCalldate( entity.getCalldate() );
        apiAttackdetectionDto.setCheckdate( entity.getCheckdate() );
        apiAttackdetectionDto.setId( entity.getId() );
        apiAttackdetectionDto.setReqip( entity.getReqip() );
        apiAttackdetectionDto.setResip( entity.getResip() );
        apiAttackdetectionDto.setRisk( entity.getRisk() );
        apiAttackdetectionDto.setSparefield1( entity.getSparefield1() );
        apiAttackdetectionDto.setSparefield2( entity.getSparefield2() );
        apiAttackdetectionDto.setSparefield3( entity.getSparefield3() );
        apiAttackdetectionDto.setSparefield4( entity.getSparefield4() );
        apiAttackdetectionDto.setSystemname( entity.getSystemname() );
        apiAttackdetectionDto.setUrl( entity.getUrl() );

        return apiAttackdetectionDto;
    }

    @Override
    public List<ApiAttackdetectionDto> toDto(List<ApiAttackdetection> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiAttackdetectionDto> list = new ArrayList<ApiAttackdetectionDto>( entityList.size() );
        for ( ApiAttackdetection apiAttackdetection : entityList ) {
            list.add( toDto( apiAttackdetection ) );
        }

        return list;
    }

    @Override
    public ApiAttackdetection toEntity(ApiAttackdetectionDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiAttackdetection apiAttackdetection = new ApiAttackdetection();

        apiAttackdetection.setAk( dto.getAk() );
        apiAttackdetection.setAkapicode( dto.getAkapicode() );
        apiAttackdetection.setApicode( dto.getApicode() );
        apiAttackdetection.setApiname( dto.getApiname() );
        apiAttackdetection.setApplyorgname( dto.getApplyorgname() );
        apiAttackdetection.setAttacktype( dto.getAttacktype() );
        apiAttackdetection.setCalldate( dto.getCalldate() );
        apiAttackdetection.setCheckdate( dto.getCheckdate() );
        apiAttackdetection.setId( dto.getId() );
        apiAttackdetection.setReqip( dto.getReqip() );
        apiAttackdetection.setResip( dto.getResip() );
        apiAttackdetection.setRisk( dto.getRisk() );
        apiAttackdetection.setSparefield1( dto.getSparefield1() );
        apiAttackdetection.setSparefield2( dto.getSparefield2() );
        apiAttackdetection.setSparefield3( dto.getSparefield3() );
        apiAttackdetection.setSparefield4( dto.getSparefield4() );
        apiAttackdetection.setSystemname( dto.getSystemname() );
        apiAttackdetection.setUrl( dto.getUrl() );

        return apiAttackdetection;
    }

    @Override
    public List<ApiAttackdetection> toEntity(List<ApiAttackdetectionDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiAttackdetection> list = new ArrayList<ApiAttackdetection>( dtoList.size() );
        for ( ApiAttackdetectionDto apiAttackdetectionDto : dtoList ) {
            list.add( toEntity( apiAttackdetectionDto ) );
        }

        return list;
    }
}
