package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.ApiAttackdetection;
import com.wzsec.modules.ic.service.dto.ApiAttackdetectionDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:01+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ApiAttackdetectionMapperImpl implements ApiAttackdetectionMapper {

    @Override
    public ApiAttackdetection toEntity(ApiAttackdetectionDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiAttackdetection apiAttackdetection = new ApiAttackdetection();

        apiAttackdetection.setId( dto.getId() );
        apiAttackdetection.setApicode( dto.getApicode() );
        apiAttackdetection.setUrl( dto.getUrl() );
        apiAttackdetection.setAttacktype( dto.getAttacktype() );
        apiAttackdetection.setRisk( dto.getRisk() );
        apiAttackdetection.setCalldate( dto.getCalldate() );
        apiAttackdetection.setCheckdate( dto.getCheckdate() );
        apiAttackdetection.setSparefield1( dto.getSparefield1() );
        apiAttackdetection.setSparefield2( dto.getSparefield2() );
        apiAttackdetection.setSparefield3( dto.getSparefield3() );
        apiAttackdetection.setSparefield4( dto.getSparefield4() );
        apiAttackdetection.setAk( dto.getAk() );
        apiAttackdetection.setApplyorgname( dto.getApplyorgname() );
        apiAttackdetection.setAkapicode( dto.getAkapicode() );
        apiAttackdetection.setSystemname( dto.getSystemname() );
        apiAttackdetection.setReqip( dto.getReqip() );
        apiAttackdetection.setApiname( dto.getApiname() );
        apiAttackdetection.setResip( dto.getResip() );

        return apiAttackdetection;
    }

    @Override
    public ApiAttackdetectionDto toDto(ApiAttackdetection entity) {
        if ( entity == null ) {
            return null;
        }

        ApiAttackdetectionDto apiAttackdetectionDto = new ApiAttackdetectionDto();

        apiAttackdetectionDto.setId( entity.getId() );
        apiAttackdetectionDto.setApicode( entity.getApicode() );
        apiAttackdetectionDto.setUrl( entity.getUrl() );
        apiAttackdetectionDto.setAttacktype( entity.getAttacktype() );
        apiAttackdetectionDto.setRisk( entity.getRisk() );
        apiAttackdetectionDto.setCalldate( entity.getCalldate() );
        apiAttackdetectionDto.setCheckdate( entity.getCheckdate() );
        apiAttackdetectionDto.setSparefield1( entity.getSparefield1() );
        apiAttackdetectionDto.setSparefield2( entity.getSparefield2() );
        apiAttackdetectionDto.setSparefield3( entity.getSparefield3() );
        apiAttackdetectionDto.setSparefield4( entity.getSparefield4() );
        apiAttackdetectionDto.setAk( entity.getAk() );
        apiAttackdetectionDto.setApplyorgname( entity.getApplyorgname() );
        apiAttackdetectionDto.setAkapicode( entity.getAkapicode() );
        apiAttackdetectionDto.setSystemname( entity.getSystemname() );
        apiAttackdetectionDto.setReqip( entity.getReqip() );
        apiAttackdetectionDto.setApiname( entity.getApiname() );
        apiAttackdetectionDto.setResip( entity.getResip() );

        return apiAttackdetectionDto;
    }

    @Override
    public List<ApiAttackdetection> toEntity(List<ApiAttackdetectionDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiAttackdetection> list = new ArrayList<ApiAttackdetection>( dtoList.size() );
        for ( ApiAttackdetectionDto apiAttackdetectionDto : dtoList ) {
            list.add( toEntity( apiAttackdetectionDto ) );
        }

        return list;
    }

    @Override
    public List<ApiAttackdetectionDto> toDto(List<ApiAttackdetection> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiAttackdetectionDto> list = new ArrayList<ApiAttackdetectionDto>( entityList.size() );
        for ( ApiAttackdetection apiAttackdetection : entityList ) {
            list.add( toDto( apiAttackdetection ) );
        }

        return list;
    }
}
