package com.wzsec.modules.license.service.mapper;

import com.wzsec.modules.license.domain.License;
import com.wzsec.modules.license.service.dto.LicenseDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:31+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class LicenseMapperImpl implements LicenseMapper {

    @Override
    public License toEntity(LicenseDto dto) {
        if ( dto == null ) {
            return null;
        }

        License license = new License();

        license.setId( dto.getId() );
        license.setSystemName( dto.getSystemName() );
        license.setVersion( dto.getVersion() );
        license.setEffectiveDate( dto.getEffectiveDate() );
        license.setValidityPeriod( dto.getValidityPeriod() );
        license.setResourceConstraints( dto.getResourceConstraints() );
        license.setEncryptionKey( dto.getEncryptionKey() );
        license.setActiveState( dto.getActiveState() );
        license.setCreateUser( dto.getCreateUser() );
        license.setUpdateUser( dto.getUpdateUser() );
        license.setCreateTime( dto.getCreateTime() );
        license.setUpdateTime( dto.getUpdateTime() );
        license.setSparefield1( dto.getSparefield1() );
        license.setSparefield2( dto.getSparefield2() );
        license.setSparefield3( dto.getSparefield3() );
        license.setSparefield4( dto.getSparefield4() );
        license.setSparefield5( dto.getSparefield5() );

        return license;
    }

    @Override
    public LicenseDto toDto(License entity) {
        if ( entity == null ) {
            return null;
        }

        LicenseDto licenseDto = new LicenseDto();

        licenseDto.setId( entity.getId() );
        licenseDto.setSystemName( entity.getSystemName() );
        licenseDto.setVersion( entity.getVersion() );
        licenseDto.setEffectiveDate( entity.getEffectiveDate() );
        licenseDto.setValidityPeriod( entity.getValidityPeriod() );
        licenseDto.setResourceConstraints( entity.getResourceConstraints() );
        licenseDto.setEncryptionKey( entity.getEncryptionKey() );
        licenseDto.setActiveState( entity.getActiveState() );
        licenseDto.setCreateUser( entity.getCreateUser() );
        licenseDto.setUpdateUser( entity.getUpdateUser() );
        licenseDto.setCreateTime( entity.getCreateTime() );
        licenseDto.setUpdateTime( entity.getUpdateTime() );
        licenseDto.setSparefield1( entity.getSparefield1() );
        licenseDto.setSparefield2( entity.getSparefield2() );
        licenseDto.setSparefield3( entity.getSparefield3() );
        licenseDto.setSparefield4( entity.getSparefield4() );
        licenseDto.setSparefield5( entity.getSparefield5() );

        return licenseDto;
    }

    @Override
    public List<License> toEntity(List<LicenseDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<License> list = new ArrayList<License>( dtoList.size() );
        for ( LicenseDto licenseDto : dtoList ) {
            list.add( toEntity( licenseDto ) );
        }

        return list;
    }

    @Override
    public List<LicenseDto> toDto(List<License> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<LicenseDto> list = new ArrayList<LicenseDto>( entityList.size() );
        for ( License license : entityList ) {
            list.add( toDto( license ) );
        }

        return list;
    }
}
