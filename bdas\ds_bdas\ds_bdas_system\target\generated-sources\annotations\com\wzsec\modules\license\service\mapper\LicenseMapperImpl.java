package com.wzsec.modules.license.service.mapper;

import com.wzsec.modules.license.domain.License;
import com.wzsec.modules.license.service.dto.LicenseDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:26:45+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class LicenseMapperImpl implements LicenseMapper {

    @Override
    public LicenseDto toDto(License entity) {
        if ( entity == null ) {
            return null;
        }

        LicenseDto licenseDto = new LicenseDto();

        licenseDto.setActiveState( entity.getActiveState() );
        licenseDto.setCreateTime( entity.getCreateTime() );
        licenseDto.setCreateUser( entity.getCreateUser() );
        licenseDto.setEffectiveDate( entity.getEffectiveDate() );
        licenseDto.setEncryptionKey( entity.getEncryptionKey() );
        licenseDto.setId( entity.getId() );
        licenseDto.setResourceConstraints( entity.getResourceConstraints() );
        licenseDto.setSparefield1( entity.getSparefield1() );
        licenseDto.setSparefield2( entity.getSparefield2() );
        licenseDto.setSparefield3( entity.getSparefield3() );
        licenseDto.setSparefield4( entity.getSparefield4() );
        licenseDto.setSparefield5( entity.getSparefield5() );
        licenseDto.setSystemName( entity.getSystemName() );
        licenseDto.setUpdateTime( entity.getUpdateTime() );
        licenseDto.setUpdateUser( entity.getUpdateUser() );
        licenseDto.setValidityPeriod( entity.getValidityPeriod() );
        licenseDto.setVersion( entity.getVersion() );

        return licenseDto;
    }

    @Override
    public List<LicenseDto> toDto(List<License> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<LicenseDto> list = new ArrayList<LicenseDto>( entityList.size() );
        for ( License license : entityList ) {
            list.add( toDto( license ) );
        }

        return list;
    }

    @Override
    public License toEntity(LicenseDto dto) {
        if ( dto == null ) {
            return null;
        }

        License license = new License();

        license.setActiveState( dto.getActiveState() );
        license.setCreateTime( dto.getCreateTime() );
        license.setCreateUser( dto.getCreateUser() );
        license.setEffectiveDate( dto.getEffectiveDate() );
        license.setEncryptionKey( dto.getEncryptionKey() );
        license.setId( dto.getId() );
        license.setResourceConstraints( dto.getResourceConstraints() );
        license.setSparefield1( dto.getSparefield1() );
        license.setSparefield2( dto.getSparefield2() );
        license.setSparefield3( dto.getSparefield3() );
        license.setSparefield4( dto.getSparefield4() );
        license.setSparefield5( dto.getSparefield5() );
        license.setSystemName( dto.getSystemName() );
        license.setUpdateTime( dto.getUpdateTime() );
        license.setUpdateUser( dto.getUpdateUser() );
        license.setValidityPeriod( dto.getValidityPeriod() );
        license.setVersion( dto.getVersion() );

        return license;
    }

    @Override
    public List<License> toEntity(List<LicenseDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<License> list = new ArrayList<License>( dtoList.size() );
        for ( LicenseDto licenseDto : dtoList ) {
            list.add( toEntity( licenseDto ) );
        }

        return list;
    }
}
