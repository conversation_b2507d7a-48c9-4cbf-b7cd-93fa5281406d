package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.Apidiscovery;
import com.wzsec.modules.ic.service.dto.ApidiscoveryDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:27:07+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ApidiscoveryMapperImpl implements ApidiscoveryMapper {

    @Override
    public ApidiscoveryDto toDto(Apidiscovery entity) {
        if ( entity == null ) {
            return null;
        }

        ApidiscoveryDto apidiscoveryDto = new ApidiscoveryDto();

        apidiscoveryDto.setAccessdomain( entity.getAccessdomain() );
        apidiscoveryDto.setApicode( entity.getApicode() );
        apidiscoveryDto.setApiip( entity.getApiip() );
        apidiscoveryDto.setApiname( entity.getApiname() );
        apidiscoveryDto.setApiport( entity.getApiport() );
        apidiscoveryDto.setApistatus( entity.getApistatus() );
        apidiscoveryDto.setCategory( entity.getCategory() );
        apidiscoveryDto.setId( entity.getId() );
        apidiscoveryDto.setInserttime( entity.getInserttime() );
        apidiscoveryDto.setLabels( entity.getLabels() );
        apidiscoveryDto.setReqExample( entity.getReqExample() );
        apidiscoveryDto.setResExample( entity.getResExample() );
        apidiscoveryDto.setRisk( entity.getRisk() );
        apidiscoveryDto.setSparefield1( entity.getSparefield1() );
        apidiscoveryDto.setSparefield2( entity.getSparefield2() );
        apidiscoveryDto.setSparefield3( entity.getSparefield3() );
        apidiscoveryDto.setSparefield4( entity.getSparefield4() );
        apidiscoveryDto.setUpdatetime( entity.getUpdatetime() );
        apidiscoveryDto.setUpdateuser( entity.getUpdateuser() );
        apidiscoveryDto.setUrl( entity.getUrl() );

        return apidiscoveryDto;
    }

    @Override
    public List<ApidiscoveryDto> toDto(List<Apidiscovery> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApidiscoveryDto> list = new ArrayList<ApidiscoveryDto>( entityList.size() );
        for ( Apidiscovery apidiscovery : entityList ) {
            list.add( toDto( apidiscovery ) );
        }

        return list;
    }

    @Override
    public Apidiscovery toEntity(ApidiscoveryDto dto) {
        if ( dto == null ) {
            return null;
        }

        Apidiscovery apidiscovery = new Apidiscovery();

        apidiscovery.setAccessdomain( dto.getAccessdomain() );
        apidiscovery.setApicode( dto.getApicode() );
        apidiscovery.setApiip( dto.getApiip() );
        apidiscovery.setApiname( dto.getApiname() );
        apidiscovery.setApiport( dto.getApiport() );
        apidiscovery.setApistatus( dto.getApistatus() );
        apidiscovery.setCategory( dto.getCategory() );
        apidiscovery.setId( dto.getId() );
        apidiscovery.setInserttime( dto.getInserttime() );
        apidiscovery.setLabels( dto.getLabels() );
        apidiscovery.setReqExample( dto.getReqExample() );
        apidiscovery.setResExample( dto.getResExample() );
        apidiscovery.setRisk( dto.getRisk() );
        apidiscovery.setSparefield1( dto.getSparefield1() );
        apidiscovery.setSparefield2( dto.getSparefield2() );
        apidiscovery.setSparefield3( dto.getSparefield3() );
        apidiscovery.setSparefield4( dto.getSparefield4() );
        apidiscovery.setUpdatetime( dto.getUpdatetime() );
        apidiscovery.setUpdateuser( dto.getUpdateuser() );
        apidiscovery.setUrl( dto.getUrl() );

        return apidiscovery;
    }

    @Override
    public List<Apidiscovery> toEntity(List<ApidiscoveryDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Apidiscovery> list = new ArrayList<Apidiscovery>( dtoList.size() );
        for ( ApidiscoveryDto apidiscoveryDto : dtoList ) {
            list.add( toEntity( apidiscoveryDto ) );
        }

        return list;
    }
}
