package com.wzsec.modules.emailsend.service.mapper;

import com.wzsec.modules.emailsend.domain.EmailRecord;
import com.wzsec.modules.emailsend.service.dto.EmailRecordDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:31+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class EmailRecordMapperImpl implements EmailRecordMapper {

    @Override
    public EmailRecordDto toDto(EmailRecord entity) {
        if ( entity == null ) {
            return null;
        }

        EmailRecordDto emailRecordDto = new EmailRecordDto();

        emailRecordDto.setChecktime( entity.getChecktime() );
        emailRecordDto.setCreatetime( entity.getCreatetime() );
        emailRecordDto.setCreateuser( entity.getCreateuser() );
        emailRecordDto.setExecutestate( entity.getExecutestate() );
        emailRecordDto.setFromUser( entity.getFromUser() );
        emailRecordDto.setHost( entity.getHost() );
        emailRecordDto.setId( entity.getId() );
        emailRecordDto.setMotif( entity.getMotif() );
        emailRecordDto.setPass( entity.getPass() );
        emailRecordDto.setPort( entity.getPort() );
        emailRecordDto.setReceiver( entity.getReceiver() );
        emailRecordDto.setSparefield1( entity.getSparefield1() );
        emailRecordDto.setSparefield2( entity.getSparefield2() );
        emailRecordDto.setSparefield3( entity.getSparefield3() );
        emailRecordDto.setSparefield4( entity.getSparefield4() );
        emailRecordDto.setSparefield5( entity.getSparefield5() );
        emailRecordDto.setSubmittype( entity.getSubmittype() );
        emailRecordDto.setTaskstate( entity.getTaskstate() );
        emailRecordDto.setUpdatetime( entity.getUpdatetime() );
        emailRecordDto.setUpdateuser( entity.getUpdateuser() );
        emailRecordDto.setUsers( entity.getUsers() );

        return emailRecordDto;
    }

    @Override
    public List<EmailRecordDto> toDto(List<EmailRecord> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<EmailRecordDto> list = new ArrayList<EmailRecordDto>( entityList.size() );
        for ( EmailRecord emailRecord : entityList ) {
            list.add( toDto( emailRecord ) );
        }

        return list;
    }

    @Override
    public EmailRecord toEntity(EmailRecordDto dto) {
        if ( dto == null ) {
            return null;
        }

        EmailRecord emailRecord = new EmailRecord();

        emailRecord.setChecktime( dto.getChecktime() );
        emailRecord.setCreatetime( dto.getCreatetime() );
        emailRecord.setCreateuser( dto.getCreateuser() );
        emailRecord.setExecutestate( dto.getExecutestate() );
        emailRecord.setFromUser( dto.getFromUser() );
        emailRecord.setHost( dto.getHost() );
        emailRecord.setId( dto.getId() );
        emailRecord.setMotif( dto.getMotif() );
        emailRecord.setPass( dto.getPass() );
        emailRecord.setPort( dto.getPort() );
        emailRecord.setReceiver( dto.getReceiver() );
        emailRecord.setSparefield1( dto.getSparefield1() );
        emailRecord.setSparefield2( dto.getSparefield2() );
        emailRecord.setSparefield3( dto.getSparefield3() );
        emailRecord.setSparefield4( dto.getSparefield4() );
        emailRecord.setSparefield5( dto.getSparefield5() );
        emailRecord.setSubmittype( dto.getSubmittype() );
        emailRecord.setTaskstate( dto.getTaskstate() );
        emailRecord.setUpdatetime( dto.getUpdatetime() );
        emailRecord.setUpdateuser( dto.getUpdateuser() );
        emailRecord.setUsers( dto.getUsers() );

        return emailRecord;
    }

    @Override
    public List<EmailRecord> toEntity(List<EmailRecordDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<EmailRecord> list = new ArrayList<EmailRecord>( dtoList.size() );
        for ( EmailRecordDto emailRecordDto : dtoList ) {
            list.add( toEntity( emailRecordDto ) );
        }

        return list;
    }
}
