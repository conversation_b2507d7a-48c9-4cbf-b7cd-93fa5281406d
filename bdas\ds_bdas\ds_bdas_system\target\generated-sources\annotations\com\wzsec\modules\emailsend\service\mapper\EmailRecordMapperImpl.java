package com.wzsec.modules.emailsend.service.mapper;

import com.wzsec.modules.emailsend.domain.EmailRecord;
import com.wzsec.modules.emailsend.service.dto.EmailRecordDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:31+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class EmailRecordMapperImpl implements EmailRecordMapper {

    @Override
    public EmailRecord toEntity(EmailRecordDto dto) {
        if ( dto == null ) {
            return null;
        }

        EmailRecord emailRecord = new EmailRecord();

        emailRecord.setId( dto.getId() );
        emailRecord.setMotif( dto.getMotif() );
        emailRecord.setHost( dto.getHost() );
        emailRecord.setPort( dto.getPort() );
        emailRecord.setFromUser( dto.getFromUser() );
        emailRecord.setPass( dto.getPass() );
        emailRecord.setUsers( dto.getUsers() );
        emailRecord.setChecktime( dto.getChecktime() );
        emailRecord.setReceiver( dto.getReceiver() );
        emailRecord.setTaskstate( dto.getTaskstate() );
        emailRecord.setSubmittype( dto.getSubmittype() );
        emailRecord.setExecutestate( dto.getExecutestate() );
        emailRecord.setCreateuser( dto.getCreateuser() );
        emailRecord.setCreatetime( dto.getCreatetime() );
        emailRecord.setUpdateuser( dto.getUpdateuser() );
        emailRecord.setUpdatetime( dto.getUpdatetime() );
        emailRecord.setSparefield1( dto.getSparefield1() );
        emailRecord.setSparefield2( dto.getSparefield2() );
        emailRecord.setSparefield3( dto.getSparefield3() );
        emailRecord.setSparefield4( dto.getSparefield4() );
        emailRecord.setSparefield5( dto.getSparefield5() );

        return emailRecord;
    }

    @Override
    public EmailRecordDto toDto(EmailRecord entity) {
        if ( entity == null ) {
            return null;
        }

        EmailRecordDto emailRecordDto = new EmailRecordDto();

        emailRecordDto.setId( entity.getId() );
        emailRecordDto.setMotif( entity.getMotif() );
        emailRecordDto.setHost( entity.getHost() );
        emailRecordDto.setPort( entity.getPort() );
        emailRecordDto.setFromUser( entity.getFromUser() );
        emailRecordDto.setPass( entity.getPass() );
        emailRecordDto.setUsers( entity.getUsers() );
        emailRecordDto.setChecktime( entity.getChecktime() );
        emailRecordDto.setReceiver( entity.getReceiver() );
        emailRecordDto.setTaskstate( entity.getTaskstate() );
        emailRecordDto.setSubmittype( entity.getSubmittype() );
        emailRecordDto.setExecutestate( entity.getExecutestate() );
        emailRecordDto.setCreateuser( entity.getCreateuser() );
        emailRecordDto.setCreatetime( entity.getCreatetime() );
        emailRecordDto.setUpdateuser( entity.getUpdateuser() );
        emailRecordDto.setUpdatetime( entity.getUpdatetime() );
        emailRecordDto.setSparefield1( entity.getSparefield1() );
        emailRecordDto.setSparefield2( entity.getSparefield2() );
        emailRecordDto.setSparefield3( entity.getSparefield3() );
        emailRecordDto.setSparefield4( entity.getSparefield4() );
        emailRecordDto.setSparefield5( entity.getSparefield5() );

        return emailRecordDto;
    }

    @Override
    public List<EmailRecord> toEntity(List<EmailRecordDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<EmailRecord> list = new ArrayList<EmailRecord>( dtoList.size() );
        for ( EmailRecordDto emailRecordDto : dtoList ) {
            list.add( toEntity( emailRecordDto ) );
        }

        return list;
    }

    @Override
    public List<EmailRecordDto> toDto(List<EmailRecord> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<EmailRecordDto> list = new ArrayList<EmailRecordDto>( entityList.size() );
        for ( EmailRecord emailRecord : entityList ) {
            list.add( toDto( emailRecord ) );
        }

        return list;
    }
}
