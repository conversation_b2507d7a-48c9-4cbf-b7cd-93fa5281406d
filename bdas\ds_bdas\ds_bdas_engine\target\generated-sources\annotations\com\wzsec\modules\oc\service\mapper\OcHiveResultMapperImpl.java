package com.wzsec.modules.oc.service.mapper;

import com.wzsec.modules.oc.domain.OcHiveResult;
import com.wzsec.modules.oc.service.dto.OcHiveResultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:01+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class OcHiveResultMapperImpl implements OcHiveResultMapper {

    @Override
    public OcHiveResult toEntity(OcHiveResultDto dto) {
        if ( dto == null ) {
            return null;
        }

        OcHiveResult ocHiveResult = new OcHiveResult();

        ocHiveResult.setId( dto.getId() );
        ocHiveResult.setTaskname( dto.getTaskname() );
        ocHiveResult.setOperationtime( dto.getOperationtime() );
        ocHiveResult.setUsername( dto.getUsername() );
        ocHiveResult.setIpaddress( dto.getIpaddress() );
        ocHiveResult.setOperationtype( dto.getOperationtype() );
        ocHiveResult.setOperation( dto.getOperation() );
        ocHiveResult.setRisk( dto.getRisk() );
        ocHiveResult.setChecktime( dto.getChecktime() );
        ocHiveResult.setSparefield1( dto.getSparefield1() );
        ocHiveResult.setSparefield2( dto.getSparefield2() );
        ocHiveResult.setSparefield3( dto.getSparefield3() );
        ocHiveResult.setSparefield4( dto.getSparefield4() );

        return ocHiveResult;
    }

    @Override
    public OcHiveResultDto toDto(OcHiveResult entity) {
        if ( entity == null ) {
            return null;
        }

        OcHiveResultDto ocHiveResultDto = new OcHiveResultDto();

        ocHiveResultDto.setId( entity.getId() );
        ocHiveResultDto.setTaskname( entity.getTaskname() );
        ocHiveResultDto.setOperationtime( entity.getOperationtime() );
        ocHiveResultDto.setUsername( entity.getUsername() );
        ocHiveResultDto.setIpaddress( entity.getIpaddress() );
        ocHiveResultDto.setOperationtype( entity.getOperationtype() );
        ocHiveResultDto.setOperation( entity.getOperation() );
        ocHiveResultDto.setRisk( entity.getRisk() );
        ocHiveResultDto.setChecktime( entity.getChecktime() );
        ocHiveResultDto.setSparefield1( entity.getSparefield1() );
        ocHiveResultDto.setSparefield2( entity.getSparefield2() );
        ocHiveResultDto.setSparefield3( entity.getSparefield3() );
        ocHiveResultDto.setSparefield4( entity.getSparefield4() );

        return ocHiveResultDto;
    }

    @Override
    public List<OcHiveResult> toEntity(List<OcHiveResultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<OcHiveResult> list = new ArrayList<OcHiveResult>( dtoList.size() );
        for ( OcHiveResultDto ocHiveResultDto : dtoList ) {
            list.add( toEntity( ocHiveResultDto ) );
        }

        return list;
    }

    @Override
    public List<OcHiveResultDto> toDto(List<OcHiveResult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<OcHiveResultDto> list = new ArrayList<OcHiveResultDto>( entityList.size() );
        for ( OcHiveResult ocHiveResult : entityList ) {
            list.add( toDto( ocHiveResult ) );
        }

        return list;
    }
}
