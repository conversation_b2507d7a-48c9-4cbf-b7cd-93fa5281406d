package com.wzsec.modules.oc.service.mapper;

import com.wzsec.modules.oc.domain.OcHiveResult;
import com.wzsec.modules.oc.service.dto.OcHiveResultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:54+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class OcHiveResultMapperImpl implements OcHiveResultMapper {

    @Override
    public OcHiveResultDto toDto(OcHiveResult entity) {
        if ( entity == null ) {
            return null;
        }

        OcHiveResultDto ocHiveResultDto = new OcHiveResultDto();

        ocHiveResultDto.setChecktime( entity.getChecktime() );
        ocHiveResultDto.setId( entity.getId() );
        ocHiveResultDto.setIpaddress( entity.getIpaddress() );
        ocHiveResultDto.setOperation( entity.getOperation() );
        ocHiveResultDto.setOperationtime( entity.getOperationtime() );
        ocHiveResultDto.setOperationtype( entity.getOperationtype() );
        ocHiveResultDto.setRisk( entity.getRisk() );
        ocHiveResultDto.setSparefield1( entity.getSparefield1() );
        ocHiveResultDto.setSparefield2( entity.getSparefield2() );
        ocHiveResultDto.setSparefield3( entity.getSparefield3() );
        ocHiveResultDto.setSparefield4( entity.getSparefield4() );
        ocHiveResultDto.setTaskname( entity.getTaskname() );
        ocHiveResultDto.setUsername( entity.getUsername() );

        return ocHiveResultDto;
    }

    @Override
    public List<OcHiveResultDto> toDto(List<OcHiveResult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<OcHiveResultDto> list = new ArrayList<OcHiveResultDto>( entityList.size() );
        for ( OcHiveResult ocHiveResult : entityList ) {
            list.add( toDto( ocHiveResult ) );
        }

        return list;
    }

    @Override
    public OcHiveResult toEntity(OcHiveResultDto dto) {
        if ( dto == null ) {
            return null;
        }

        OcHiveResult ocHiveResult = new OcHiveResult();

        ocHiveResult.setChecktime( dto.getChecktime() );
        ocHiveResult.setId( dto.getId() );
        ocHiveResult.setIpaddress( dto.getIpaddress() );
        ocHiveResult.setOperation( dto.getOperation() );
        ocHiveResult.setOperationtime( dto.getOperationtime() );
        ocHiveResult.setOperationtype( dto.getOperationtype() );
        ocHiveResult.setRisk( dto.getRisk() );
        ocHiveResult.setSparefield1( dto.getSparefield1() );
        ocHiveResult.setSparefield2( dto.getSparefield2() );
        ocHiveResult.setSparefield3( dto.getSparefield3() );
        ocHiveResult.setSparefield4( dto.getSparefield4() );
        ocHiveResult.setTaskname( dto.getTaskname() );
        ocHiveResult.setUsername( dto.getUsername() );

        return ocHiveResult;
    }

    @Override
    public List<OcHiveResult> toEntity(List<OcHiveResultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<OcHiveResult> list = new ArrayList<OcHiveResult>( dtoList.size() );
        for ( OcHiveResultDto ocHiveResultDto : dtoList ) {
            list.add( toEntity( ocHiveResultDto ) );
        }

        return list;
    }
}
