package com.wzsec.modules.sdd.rule.service.mapper;

import com.wzsec.modules.sdd.rule.domain.Rule;
import com.wzsec.modules.sdd.rule.service.dto.RuleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:01+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class RuleMapperImpl implements RuleMapper {

    @Override
    public Rule toEntity(RuleDto dto) {
        if ( dto == null ) {
            return null;
        }

        Rule rule = new Rule();

        rule.setId( dto.getId() );
        rule.setCname( dto.getCname() );
        rule.setEname( dto.getEname() );
        rule.setRuletype( dto.getRuletype() );
        rule.setApplytypename( dto.getApplytypename() );
        rule.setApplytypecname( dto.getApplytypecname() );
        rule.setRegexps( dto.getRegexps() );
        rule.setClasspath( dto.getClasspath() );
        rule.setMethodname( dto.getMethodname() );
        rule.setState( dto.getState() );
        rule.setDes( dto.getDes() );
        rule.setNote( dto.getNote() );
        rule.setCreateuser( dto.getCreateuser() );
        rule.setCreatetime( dto.getCreatetime() );
        rule.setUpdateuser( dto.getUpdateuser() );
        rule.setUpdatetime( dto.getUpdatetime() );
        rule.setSparefield1( dto.getSparefield1() );
        rule.setSparefield2( dto.getSparefield2() );
        rule.setSparefield3( dto.getSparefield3() );
        rule.setSparefield4( dto.getSparefield4() );
        rule.setSparefield5( dto.getSparefield5() );
        rule.setRisk( dto.getRisk() );

        return rule;
    }

    @Override
    public RuleDto toDto(Rule entity) {
        if ( entity == null ) {
            return null;
        }

        RuleDto ruleDto = new RuleDto();

        ruleDto.setId( entity.getId() );
        ruleDto.setCname( entity.getCname() );
        ruleDto.setEname( entity.getEname() );
        ruleDto.setRuletype( entity.getRuletype() );
        ruleDto.setApplytypename( entity.getApplytypename() );
        ruleDto.setApplytypecname( entity.getApplytypecname() );
        ruleDto.setRegexps( entity.getRegexps() );
        ruleDto.setClasspath( entity.getClasspath() );
        ruleDto.setMethodname( entity.getMethodname() );
        ruleDto.setState( entity.getState() );
        ruleDto.setDes( entity.getDes() );
        ruleDto.setNote( entity.getNote() );
        ruleDto.setCreateuser( entity.getCreateuser() );
        ruleDto.setCreatetime( entity.getCreatetime() );
        ruleDto.setUpdateuser( entity.getUpdateuser() );
        ruleDto.setUpdatetime( entity.getUpdatetime() );
        ruleDto.setSparefield1( entity.getSparefield1() );
        ruleDto.setSparefield2( entity.getSparefield2() );
        ruleDto.setSparefield3( entity.getSparefield3() );
        ruleDto.setSparefield4( entity.getSparefield4() );
        ruleDto.setSparefield5( entity.getSparefield5() );
        ruleDto.setRisk( entity.getRisk() );

        return ruleDto;
    }

    @Override
    public List<Rule> toEntity(List<RuleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Rule> list = new ArrayList<Rule>( dtoList.size() );
        for ( RuleDto ruleDto : dtoList ) {
            list.add( toEntity( ruleDto ) );
        }

        return list;
    }

    @Override
    public List<RuleDto> toDto(List<Rule> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<RuleDto> list = new ArrayList<RuleDto>( entityList.size() );
        for ( Rule rule : entityList ) {
            list.add( toDto( rule ) );
        }

        return list;
    }
}
