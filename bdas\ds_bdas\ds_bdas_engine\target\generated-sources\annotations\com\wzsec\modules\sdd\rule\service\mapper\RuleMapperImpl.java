package com.wzsec.modules.sdd.rule.service.mapper;

import com.wzsec.modules.sdd.rule.domain.Rule;
import com.wzsec.modules.sdd.rule.service.dto.RuleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:27:07+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class RuleMapperImpl implements RuleMapper {

    @Override
    public RuleDto toDto(Rule entity) {
        if ( entity == null ) {
            return null;
        }

        RuleDto ruleDto = new RuleDto();

        ruleDto.setApplytypecname( entity.getApplytypecname() );
        ruleDto.setApplytypename( entity.getApplytypename() );
        ruleDto.setClasspath( entity.getClasspath() );
        ruleDto.setCname( entity.getCname() );
        ruleDto.setCreatetime( entity.getCreatetime() );
        ruleDto.setCreateuser( entity.getCreateuser() );
        ruleDto.setDes( entity.getDes() );
        ruleDto.setEname( entity.getEname() );
        ruleDto.setId( entity.getId() );
        ruleDto.setMethodname( entity.getMethodname() );
        ruleDto.setNote( entity.getNote() );
        ruleDto.setRegexps( entity.getRegexps() );
        ruleDto.setRisk( entity.getRisk() );
        ruleDto.setRuletype( entity.getRuletype() );
        ruleDto.setSparefield1( entity.getSparefield1() );
        ruleDto.setSparefield2( entity.getSparefield2() );
        ruleDto.setSparefield3( entity.getSparefield3() );
        ruleDto.setSparefield4( entity.getSparefield4() );
        ruleDto.setSparefield5( entity.getSparefield5() );
        ruleDto.setState( entity.getState() );
        ruleDto.setUpdatetime( entity.getUpdatetime() );
        ruleDto.setUpdateuser( entity.getUpdateuser() );

        return ruleDto;
    }

    @Override
    public List<RuleDto> toDto(List<Rule> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<RuleDto> list = new ArrayList<RuleDto>( entityList.size() );
        for ( Rule rule : entityList ) {
            list.add( toDto( rule ) );
        }

        return list;
    }

    @Override
    public Rule toEntity(RuleDto dto) {
        if ( dto == null ) {
            return null;
        }

        Rule rule = new Rule();

        rule.setApplytypecname( dto.getApplytypecname() );
        rule.setApplytypename( dto.getApplytypename() );
        rule.setClasspath( dto.getClasspath() );
        rule.setCname( dto.getCname() );
        rule.setCreatetime( dto.getCreatetime() );
        rule.setCreateuser( dto.getCreateuser() );
        rule.setDes( dto.getDes() );
        rule.setEname( dto.getEname() );
        rule.setId( dto.getId() );
        rule.setMethodname( dto.getMethodname() );
        rule.setNote( dto.getNote() );
        rule.setRegexps( dto.getRegexps() );
        rule.setRisk( dto.getRisk() );
        rule.setRuletype( dto.getRuletype() );
        rule.setSparefield1( dto.getSparefield1() );
        rule.setSparefield2( dto.getSparefield2() );
        rule.setSparefield3( dto.getSparefield3() );
        rule.setSparefield4( dto.getSparefield4() );
        rule.setSparefield5( dto.getSparefield5() );
        rule.setState( dto.getState() );
        rule.setUpdatetime( dto.getUpdatetime() );
        rule.setUpdateuser( dto.getUpdateuser() );

        return rule;
    }

    @Override
    public List<Rule> toEntity(List<RuleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Rule> list = new ArrayList<Rule>( dtoList.size() );
        for ( RuleDto ruleDto : dtoList ) {
            list.add( toEntity( ruleDto ) );
        }

        return list;
    }
}
