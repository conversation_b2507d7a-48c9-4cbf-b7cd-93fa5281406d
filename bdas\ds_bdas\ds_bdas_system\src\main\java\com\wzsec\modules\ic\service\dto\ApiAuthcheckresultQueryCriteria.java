package com.wzsec.modules.ic.service.dto;

import lombok.Data;

import java.sql.Timestamp;
import java.util.List;
import com.wzsec.annotation.Query;

/**
* <AUTHOR>
* @date 2022-08-29
*/
@Data
public class ApiAuthcheckresultQueryCriteria{

    /** 支持精确查询和IN查询：单个值时精确查询，逗号分隔字符串或List时IN查询 */
    @Query(type = Query.Type.IN)
    private Object ak;

    private Timestamp dateStart;

    private Timestamp dateEnd;

    @Query(type = Query.Type.IN)
    private List<String> sparefield4;

    /** 模糊 */
    @Query
    private String apicode;

    /** 精确 */
    @Query(type = Query.Type.INNER_LIKE)
    private String protocol;

    /** 精确 */
    @Query(type = Query.Type.INNER_LIKE)
    private String reqmethod;

    /** 精确 */
    @Query(type = Query.Type.INNER_LIKE)
    private String risk;

    /** BETWEEN */
    @Query(type = Query.Type.BETWEEN)
    private List<String> inserttime;

    /**
     * 模糊
     */
    @Query(type = Query.Type.INNER_LIKE)
    private String apiname;

    @Query(blurry = "apicode,protocol,reqmethod,risk,apiname")
    private String blurry;
}
