package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.ApiUnusedak;
import com.wzsec.modules.ic.service.dto.ApiUnusedakDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:52+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ApiUnusedakMapperImpl implements ApiUnusedakMapper {

    @Override
    public ApiUnusedakDto toDto(ApiUnusedak entity) {
        if ( entity == null ) {
            return null;
        }

        ApiUnusedakDto apiUnusedakDto = new ApiUnusedakDto();

        apiUnusedakDto.setAk( entity.getAk() );
        apiUnusedakDto.setApicode( entity.getApicode() );
        apiUnusedakDto.setApiname( entity.getApiname() );
        apiUnusedakDto.setApiurl( entity.getApiurl() );
        apiUnusedakDto.setApplyorgname( entity.getApplyorgname() );
        apiUnusedakDto.setChecktime( entity.getChecktime() );
        apiUnusedakDto.setId( entity.getId() );
        apiUnusedakDto.setReqip( entity.getReqip() );
        apiUnusedakDto.setRisk( entity.getRisk() );
        apiUnusedakDto.setSparefield1( entity.getSparefield1() );
        apiUnusedakDto.setSparefield2( entity.getSparefield2() );
        apiUnusedakDto.setSparefield3( entity.getSparefield3() );
        apiUnusedakDto.setSparefield4( entity.getSparefield4() );
        apiUnusedakDto.setSystemname( entity.getSystemname() );

        return apiUnusedakDto;
    }

    @Override
    public List<ApiUnusedakDto> toDto(List<ApiUnusedak> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiUnusedakDto> list = new ArrayList<ApiUnusedakDto>( entityList.size() );
        for ( ApiUnusedak apiUnusedak : entityList ) {
            list.add( toDto( apiUnusedak ) );
        }

        return list;
    }

    @Override
    public ApiUnusedak toEntity(ApiUnusedakDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiUnusedak apiUnusedak = new ApiUnusedak();

        apiUnusedak.setAk( dto.getAk() );
        apiUnusedak.setApicode( dto.getApicode() );
        apiUnusedak.setApiname( dto.getApiname() );
        apiUnusedak.setApiurl( dto.getApiurl() );
        apiUnusedak.setApplyorgname( dto.getApplyorgname() );
        apiUnusedak.setChecktime( dto.getChecktime() );
        apiUnusedak.setId( dto.getId() );
        apiUnusedak.setReqip( dto.getReqip() );
        apiUnusedak.setRisk( dto.getRisk() );
        apiUnusedak.setSparefield1( dto.getSparefield1() );
        apiUnusedak.setSparefield2( dto.getSparefield2() );
        apiUnusedak.setSparefield3( dto.getSparefield3() );
        apiUnusedak.setSparefield4( dto.getSparefield4() );
        apiUnusedak.setSystemname( dto.getSystemname() );

        return apiUnusedak;
    }

    @Override
    public List<ApiUnusedak> toEntity(List<ApiUnusedakDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiUnusedak> list = new ArrayList<ApiUnusedak>( dtoList.size() );
        for ( ApiUnusedakDto apiUnusedakDto : dtoList ) {
            list.add( toEntity( apiUnusedakDto ) );
        }

        return list;
    }
}
