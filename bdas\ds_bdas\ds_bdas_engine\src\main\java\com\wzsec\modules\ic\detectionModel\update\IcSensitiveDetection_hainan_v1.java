package com.wzsec.modules.ic.detectionModel.update;

import cn.hutool.core.lang.Console;
import cn.hutool.core.util.StrUtil;
import com.wzsec.config.kafka.APIContentAnomalyDetection_v1;
import com.wzsec.modules.blackwhitelist.service.BlackwhitelistService;
import com.wzsec.modules.ic.config.MonitorRiskAlarmData;
import com.wzsec.modules.ic.domain.*;
import com.wzsec.modules.ic.repository.IcConfigurationRepository;
import com.wzsec.modules.ic.service.*;
import com.wzsec.modules.ic.service.dto.IcTaskDto;
import com.wzsec.modules.sdd.rule.service.RuleService;
import com.wzsec.modules.sdd.rule.service.dto.RuleDto;
import com.wzsec.utils.*;
import com.wzsec.utils.algo.ProRuleFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.ClearScrollRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchScrollRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.Scroll;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.fetch.subphase.FetchSourceContext;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * [海南-AP013]接口输出敏感内容检测
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Slf4j
@Component
public class IcSensitiveDetection_hainan_v1 {

    @Autowired
    private IcAlarmdisposalService icAlarmdisposalService;

    @Autowired
    private IcInterfaceInfoService icInterfaceInfoService;

    @Autowired
    private IcConfigurationRepository icConfigurationRepository;

    @Autowired
    private IcApprovallogService icApprovallogService;

    @Autowired
    private RuleService ruleService;

    @Autowired
    private IcResultService icResultService;

    @Autowired
    private IcResultDetailService icResultDetailService;

    @Autowired
    private BlackwhitelistService blackwhitelistService;

    @Autowired
    private IcCheckmodelService icCheckmodelService;


    /**
     * 接口输出敏感内容检测
     *
     * @param client   Elasticsearch的客户端
     * @param params   传递参数
     * @param tasktype 任务类型
     * @throws IOException IOException
     */
    public void interfaceCallVolume(RestHighLevelClient client, Map<String, Object> params, String tasktype) throws Exception {

        // 告警级别判定规则
        List<String> whitelistInterfaceList = icConfigurationRepository.configurationApicodeList(tasktype); // TODO 告警过滤-白名单-告警级别标识无
        Map<String, IcInterfaceInfo> icInterfaceInfoMap = icInterfaceInfoService.queryIcInterfaceInfoMap();  // TODO 用于查询接口名称信息
        Map<String, IcApprovallog> icApprovallogMap = icApprovallogService.queryAkApprovallogMap(); //TODO 用于查询ak对应的申请日志信息
        List<RuleDto> ruleDtoList = ruleService.queryAllRule(); //TODO 获取所有开启的识别规则
        List<String> sensitivityDetectionWhiteList = blackwhitelistService.queryWhitelist();  // TODO 获取已启用的白名单,用于检测敏感数据过滤
        List<String> blackApiCodeList = icConfigurationRepository.queryBlackApiCodeList(); // TODO 用于指定接口推送kafka设置
        Map<String, IcCheckmodel> icCheckmodelMap = icCheckmodelService.queryIcCheckModelMap(); //TODO 用于查询模型配置信息

        // TODO 获取检测模型参数信息
        SceneDetectionTransfer sceneDetectionTransfer = getSceneDetectionTransfer(params, icCheckmodelMap);


        Set<String> akApiCodeSet = combinedQueryRequests(client, sceneDetectionTransfer.getIndex(), sceneDetectionTransfer.getStartTime(), sceneDetectionTransfer.getEndTime(), StringUtils.isNotBlank(String.valueOf(params.get("logDate"))));

        int akApiCodeSize = akApiCodeSet.size();
        for (String akApiCode : akApiCodeSet) {

            long startTime = System.currentTimeMillis(); // 记录任务开始时间

            akApiCodeSize--;

            // TODO 1. 抽取一条流量对象样例数据
            ApiCallNetFlow apiCallNetFlow = ESUtils.getDocumentByQueryFields(client, sceneDetectionTransfer.getIndex(), "akapicode.keyword", akApiCode, sceneDetectionTransfer.getStartTime(), sceneDetectionTransfer.getEndTime());

            // TODO 2. 获取akApiCode对应的响应结果截取参数
            // Set<String> checkinfoSet = new HashSet<>();
            // Integer unreviewedDataSize = combinedUnreviewedDataList(client, sceneDetectionTransfer.getIndex(), akApiCode, checkinfoSet)

            Map<String, Map<String, String>> sensitiveTypeMap = new HashMap<>();
            Integer unreviewedDataSize = combinedUnreviewedDataList(client, sceneDetectionTransfer.getIndex(), akApiCode, ruleDtoList, sensitivityDetectionWhiteList, sensitiveTypeMap);


            // TODO 记录耗时
            long endTime = System.currentTimeMillis(); // 记录任务结束时间
            long elapsedTimeMillis = endTime - startTime; // 计算任务耗时（毫秒）
            double elapsedTimeSeconds = elapsedTimeMillis / 1000.0; // 将耗时转换为秒

            Console.log("【当前时间 {}, 剩余{}个待检测, 已检测ak-apiCode {}, 获取结果条数 {}, 检测敏感数据耗时 {} 】", DateUtil.getNowTime(), akApiCodeSize, akApiCode, unreviewedDataSize, elapsedTimeSeconds);


            // TODO 3. 获取响应结果敏感类型(敏感类型,级别拼接)及数量统计,样例,检测数量 (识别类单独抽取)
            // Map<String, Map<String, String>> sensitiveTypeMap = SensitiveDataIdentification.getSensitiveTypeMapForList(ruleDtoList, sensitivityDetectionWhiteList);

            // TODO 4. 获取结果概要对象,写入概要表
            List<IcResult> icResults = assemblyIcResult(akApiCode, icInterfaceInfoMap, sensitiveTypeMap, icApprovallogMap, sceneDetectionTransfer.getTaskName(), apiCallNetFlow, whitelistInterfaceList);
            icResults.forEach((icResult) -> {
                icResultService.create(icResult);
            });
            // TODO 5. 获取结果详情对象,写入结果表
            List<IcResultDetail> icResultDetails = assemblyIcResultDetail(akApiCode, icInterfaceInfoMap, sensitiveTypeMap, unreviewedDataSize, icApprovallogMap, sceneDetectionTransfer.getTaskName(), apiCallNetFlow, whitelistInterfaceList);

            Map<String, Long> sensitiveTypeNumMap = new HashMap<>();
            for (IcResultDetail icResultDetail : icResultDetails) {
                // TODO 6. 结果详情入库
                icResultDetailService.create(icResultDetail);
                // TODO 7. <敏感类型,告警级别 , 数量>
                sensitiveTypeNumMap.put(icResultDetail.getResulttype() + "," + icResultDetail.getRisk(), icResultDetail.getCheckcount());
            }
            // TODO 8. 组装告警处置对象信息, 告警处置写入数据库, 告警信息推送kafka
            assemblyAlarm(akApiCode, apiCallNetFlow, icInterfaceInfoMap, icApprovallogMap, sensitiveTypeNumMap, whitelistInterfaceList, blackApiCodeList);

            // TODO 立即释放内存
            apiCallNetFlow = new ApiCallNetFlow();
            sensitiveTypeMap = new HashMap<>();
            icResults = new ArrayList<>();
            icResultDetails = new ArrayList<>();
            sensitiveTypeNumMap = new HashMap<>();

        }

        // TODO 立即释放内存
        whitelistInterfaceList = new ArrayList<>();
        icInterfaceInfoMap = new HashMap<>();
        icApprovallogMap = new HashMap<>();
        ruleDtoList = new ArrayList<>();
        sensitivityDetectionWhiteList = new ArrayList<>();
        blackApiCodeList = new ArrayList<>();
        icCheckmodelMap = new HashMap<>();

        akApiCodeSet = new HashSet<>();

    }

    /**
     * 获取场景检测传输
     *
     * @param params          传递参数
     * @param icCheckmodelMap 模型配置
     * @return {@link SceneDetectionTransfer }
     */
    private static SceneDetectionTransfer getSceneDetectionTransfer(Map<String, Object> params, Map<String, IcCheckmodel> icCheckmodelMap) {
        String logDate = String.valueOf(params.get("logDate"));  // 指定清洗时间,非请求时间
        // 任务名
        IcTaskDto icTaskDto = (IcTaskDto) params.get("icTask");// 任务
        String taskName = icTaskDto.getTaskname();

        // TODO 检测模型配置
        String modelParameter = "{\"callNum\":10000,\"threshold\":10,\"timeSpan\":10}";
        if (icCheckmodelMap.containsKey(Const.INTERFACE_ALARMTYPE_AP013)) {   // AP013
            IcCheckmodel icCheckmodel = icCheckmodelMap.get(Const.INTERFACE_ALARMTYPE_AP013);
            modelParameter = StringUtils.isNotBlank(icCheckmodel.getModelparameter()) ? icCheckmodel.getModelparameter() : modelParameter;
        }
        Map<String, Object> thresholdMap = StringUtil.parseStringToMap(modelParameter);
        int timeSpan = (int) thresholdMap.get("timeSpan"); // TODO 即时执行时间间隔(分钟)

        boolean immediately = Const.TASK_SUBMITTYPE_IMMEDIATELY.equals(icTaskDto.getSubmitmethod()); // TODO 用于判断是否为即时执行
        // 计算时间范围
        Instant now = Instant.now();
        Instant startTime = null;
        Instant endTime = now;

        if (immediately) {
            startTime = now.minusSeconds(timeSpan * 60L);
        } else {
            LocalDate targetDate;
            if (logDate != null && !logDate.isEmpty()) {
                // 解析 logDate
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy.MM.dd");
                targetDate = LocalDate.parse(logDate, formatter);
            } else {
                // 使用昨天的日期
                targetDate = LocalDate.now(ZoneId.systemDefault()).minusDays(1);
            }
            // 使用系统默认时区进行转换
            ZoneId systemZone = ZoneId.systemDefault();
            startTime = targetDate.atStartOfDay(systemZone).toInstant();
            endTime = targetDate.plusDays(1).atStartOfDay(systemZone).minusSeconds(1).toInstant();
        }
        SceneDetectionTransfer sceneDetectionTransfer = new SceneDetectionTransfer();
        sceneDetectionTransfer.setStartTime(startTime);
        sceneDetectionTransfer.setEndTime(endTime);
        sceneDetectionTransfer.setTaskName(taskName);
        sceneDetectionTransfer.setIndex(Const.ES_DETECTED_INDEX_PREFIX + "*");
        return sceneDetectionTransfer;
    }


    /**
     * 处理告警处置对象信息
     *
     * @param akApiCode              ak-apiCode组合
     * @param apiCallNetFlow         流量对象
     * @param icInterfaceInfoMap     接口信息
     * @param icApprovallogMap       共享交换审批日志
     * @param sensitiveTypeNumMap    敏感类型对应数量
     * @param whitelistInterfaceList 白名单接口列表
     */
    private void assemblyAlarm(String akApiCode, ApiCallNetFlow apiCallNetFlow,
                               Map<String, IcInterfaceInfo> icInterfaceInfoMap,
                               Map<String, IcApprovallog> icApprovallogMap,
                               Map<String, Long> sensitiveTypeNumMap,
                               List<String> whitelistInterfaceList,
                               List<String> blackApiCodeList) {

        Set<String> riskSet = new HashSet<>();
        Map<String, Long> typeMap = new HashMap();
        sensitiveTypeNumMap.forEach((typeRisk, num) -> {
            String[] typeRiskSplit = typeRisk.split(",");
            String type = typeRiskSplit[0];
            String risk = typeRiskSplit[1];
            typeMap.put(type, num);
            riskSet.add(risk);
        });

        // TODO 告警详情为类型:识别数量
        StringBuilder typeSetString = new StringBuilder();
        typeMap.forEach((type, num) -> {
            typeSetString.append(type).append(":").append(num).append(",");
        });
        String sensitiveData = typeSetString.toString().endsWith(",") ? typeSetString.substring(0, typeSetString.toString().length() - 1) : typeSetString.toString();

        // TODO 告警级别取当前敏感类型最高级别
        int maxRisk = riskSet.stream().mapToInt(Integer::parseInt).max().orElse(0);
        String risk = String.valueOf(maxRisk);


        if (akApiCode.split(",").length == 3) {
            String ak = akApiCode.split(",")[0]; //ak
            String apiCode = akApiCode.split(",")[1]; // 接口编码
            String system = akApiCode.split(",")[2]; // 系统名称
            String apiName = icInterfaceInfoMap.containsKey(apiCode) ? icInterfaceInfoMap.get(apiCode).getApiname() : ""; //接口名称

            risk = whitelistInterfaceList.contains(apiCode) ? Const.RISK_NOT : risk;   // TODO 如设置白名单,该接口编码级别设置为无

            String akApiCodeIdentification = ak + "," + apiCode;

            String str = "{}, {} 调用接口 {} {} 返回敏感数据, 涉及敏感类型类型及数量 {} ";
            String eventDetails = StrUtil.format(str, DateUtils.timeCycle(new Date(), 1), ak, apiCode, apiName == null ? "" : apiName, sensitiveData);

            if (!risk.equals(Const.RISK_NOT)) { //TODO 2024.06.20 修改为不为无风险写告警处置
                IcAlarmdisposal icAlarmdisposal = new IcAlarmdisposal();
                icAlarmdisposal.setApicode(apiCode); //接口编码
                icAlarmdisposal.setApiname(apiName); //接口名称
                icAlarmdisposal.setDetectionmodel(Const.DICT_API_SENSITIVE_DATA); //检测模型
                icAlarmdisposal.setCircumstantiality(eventDetails); //事件详情
                icAlarmdisposal.setRisk(risk); //风险程度
                icAlarmdisposal.setReservefield1(risk); //风险程度
                icAlarmdisposal.setChecktime(DateUtils.getDateTime());
                icAlarmdisposal.setTreatmentstate(Const.INTERFACE_ALARM_DISPOSAL_UNHANDLED); //处置状态
                icAlarmdisposal.setEventrule(Const.DICT_API_VISITS_ARE_HIGH_CHARS);//规则
                icAlarmdisposal.setUuid(MonitorRiskAlarmData.getUuid());//事件ID(唯一性)
                icAlarmdisposal.setAk(ak); //ak

                // TODO 抽取样例取值
                icAlarmdisposal.setSystemname(apiCallNetFlow != null ? apiCallNetFlow.getSystem() : ""); // TODO 系统标识
                icAlarmdisposal.setReservefield2(apiCallNetFlow != null ? apiCallNetFlow.getId() : "");// TODO 接口数据唯一标识
                icAlarmdisposal.setSourceip(apiCallNetFlow != null ? apiCallNetFlow.getClientip() : ""); // TODO 请求IP
                icAlarmdisposal.setDestinationip(apiCallNetFlow != null ? apiCallNetFlow.getApiip() : ""); // TODO 接口服务IP
                icAlarmdisposal.setApiurl(apiCallNetFlow != null ? apiCallNetFlow.getApiuri() : ""); // TODO apiUrl

                // TODO 审批日志取值取值
                icAlarmdisposal.setReqapp(""); // TODO 请求方应用名称(无)
                icAlarmdisposal.setReqdepartment(icApprovallogMap.containsKey(akApiCodeIdentification) ? icApprovallogMap.get(akApiCodeIdentification).getApplyorgname() : ""); // TODO 请求方部门(申请部门名称)
                icAlarmdisposal.setIcressystem(icApprovallogMap.containsKey(akApiCodeIdentification) ? icApprovallogMap.get(akApiCodeIdentification).getSysname() : "");  // TODO 接口服务系统
                icAlarmdisposal.setIcresdepartment(icApprovallogMap.containsKey(akApiCodeIdentification) ? icApprovallogMap.get(akApiCodeIdentification).getMartsupplyorgname() : "");  // TODO 接口服务系统
                icAlarmdisposalService.create(icAlarmdisposal);

                // TODO 场景检测告警信息推送
                alarmInformationPush(icAlarmdisposal, blackApiCodeList);
            }
        }
    }


    /**
     * 概要表对象
     *
     * @param akApiCode              ak-apiCode组合
     * @param icInterfaceInfoMap     接口信息
     * @param sensitiveTypeMap       敏感类型
     * @param icApprovallogMap       共享交换审批日志
     * @param taskName               任务名称
     * @param apiCallNetFlow         流量对象
     * @param whitelistInterfaceList 白名单接口列表
     * @return {@link List }<{@link IcResult }>
     */
    private static List<IcResult> assemblyIcResult(String akApiCode,
                                                   Map<String, IcInterfaceInfo> icInterfaceInfoMap,
                                                   Map<String, Map<String, String>> sensitiveTypeMap,
                                                   Map<String, IcApprovallog> icApprovallogMap,
                                                   String taskName,
                                                   ApiCallNetFlow apiCallNetFlow,
                                                   List<String> whitelistInterfaceList) {

        Map<String, String> riskNumMap = sensitiveTypeMap.get("riskNum");

        List<IcResult> icResultList = new ArrayList<>();

        if (akApiCode.split(",").length == 3) {
            String ak = akApiCode.split(",")[0]; //ak
            String apiCode = akApiCode.split(",")[1]; // 接口编码
            String system = akApiCode.split(",")[2]; // 系统名称

            String akApiCodeIdentification = ak + "," + apiCode;
            String apiName = icInterfaceInfoMap.containsKey(apiCode) ? icInterfaceInfoMap.get(apiCode).getApiname() : ""; //接口名称
            String applyOrgName = icApprovallogMap.containsKey(akApiCodeIdentification) ? icApprovallogMap.get(akApiCodeIdentification).getApplyorgname() : ""; // TODO 请求方部门(申请部门名称)
            String apiUrl = apiCallNetFlow != null ? apiCallNetFlow.getApiuri() : ""; //接口URL
            // TODO 概要表对象
            riskNumMap.forEach((risk, num) -> {
                risk = whitelistInterfaceList.contains(apiCode) ? Const.RISK_NOT : risk;   // TODO 如设置白名单,该接口编码级别设置为无
                IcResult icResult = new IcResult();
                icResult.setAk(ak); //AK
                icResult.setApplyorgname(applyOrgName); //申请部门名称
                icResult.setApicode(apiCode); // 接口编码
                icResult.setApiname(apiName); // 接口名称
                icResult.setTaskname(taskName); //任务名称
                icResult.setRisk(risk); // 敏感程度
                icResult.setCheckcount(Long.valueOf(num)); //检测参数统计次数
                icResult.setLogsign(system); // 系统名称
                icResult.setChecktime(new Timestamp(System.currentTimeMillis())); //检测时间
                icResult.setApiurl(apiUrl); //URL
                icResult.setAkapicode(akApiCode); //ak-apiCode
                icResult.setSparefield4(DateUtils.getDateTime()); //检测时间
                icResultList.add(icResult);
            });
        }
        return icResultList;
    }


    /**
     * 详情表对象
     *
     * @param akApiCode              ak-apiCode组合
     * @param icInterfaceInfoMap     接口信息
     * @param sensitiveTypeMap       敏感类型
     * @param unreviewedDataSize     检测数据条数(接口请求量)
     * @param icApprovallogMap       共享交换审批日志
     * @param taskName               任务名称
     * @param apiCallNetFlow         流量对象
     * @param whitelistInterfaceList 白名单接口列表
     * @return {@link List }<{@link IcResultDetail }>
     */
    private static List<IcResultDetail> assemblyIcResultDetail(String akApiCode,
                                                               Map<String, IcInterfaceInfo> icInterfaceInfoMap,
                                                               Map<String, Map<String, String>> sensitiveTypeMap,
                                                               Integer unreviewedDataSize,
                                                               Map<String, IcApprovallog> icApprovallogMap,
                                                               String taskName,
                                                               ApiCallNetFlow apiCallNetFlow,
                                                               List<String> whitelistInterfaceList) {

        Map<String, String> fileDataTypeMap = sensitiveTypeMap.get("fileDataType");
        Map<String, String> dataExampleMap = sensitiveTypeMap.get("dataExample");
        String dataCount = sensitiveTypeMap.get("dataCount").get("count");

        List<IcResultDetail> icResultDetailList = new ArrayList<>();

        if (akApiCode.split(",").length == 3) {
            String ak = akApiCode.split(",")[0]; //ak
            String apiCode = akApiCode.split(",")[1]; // 接口编码
            String system = akApiCode.split(",")[2]; // 系统名称
            String akApiCodeIdentification = ak + "," + apiCode;
            String apiName = icInterfaceInfoMap.containsKey(apiCode) ? icInterfaceInfoMap.get(apiCode).getApiname() : ""; //接口名称
            String applyOrgName = icApprovallogMap.containsKey(akApiCodeIdentification) ? icApprovallogMap.get(akApiCodeIdentification).getApplyorgname() : ""; // TODO 请求方部门(申请部门名称)
            String apiUrl = apiCallNetFlow != null ? apiCallNetFlow.getApiuri() : ""; //接口URL
            String clientIp = apiCallNetFlow != null ? apiCallNetFlow.getClientip() : ""; //请求IP

            // TODO 详情表对象
            fileDataTypeMap.forEach((fileDataType, num) -> {
                if (fileDataType.split(",").length == 2) {
                    String[] fileData = fileDataType.split(",");
                    String type = fileData[0];
                    String risk = fileData[1];
                    risk = whitelistInterfaceList.contains(apiCode) ? Const.RISK_NOT : risk;   // TODO 如设置白名单,该接口编码级别设置为无
                    String sensitiveData = dataExampleMap.getOrDefault(fileDataType, "");
                    long checkCount = Long.parseLong(num);
                    long totalCount = Long.parseLong(dataCount);
                    IcResultDetail icResultDetail = new IcResultDetail();
                    icResultDetail.setTaskname(taskName); //任务名称
                    icResultDetail.setApicode(apiCode); // 接口编码
                    icResultDetail.setApiname(apiName); // 接口名称
                    icResultDetail.setCheckcount(checkCount); //检测参数统计次数
                    icResultDetail.setTotalcount(totalCount); //检测参数总次数
                    icResultDetail.setTotallinecount(Long.valueOf(unreviewedDataSize)); //检测参数行总次数
                    icResultDetail.setChecktime(new Timestamp(System.currentTimeMillis())); //检测时间
                    double rate = 100 * ((double) checkCount / totalCount);
                    BigDecimal bDec = new BigDecimal(rate);
                    double ratio = bDec.setScale(2, RoundingMode.HALF_UP).doubleValue();
                    icResultDetail.setRatio(String.valueOf(ratio));  //比例
                    icResultDetail.setLogsign(system); // 系统名称
                    icResultDetail.setResulttype(type); //结果类型
                    icResultDetail.setRisk(risk); // 敏感程度
                    icResultDetail.setSensitivedata(sensitiveData); // 敏感数据示例
                    icResultDetail.setAk(ak); //AK
                    icResultDetail.setApplyorgname(applyOrgName); //申请部门名称
                    icResultDetail.setApiurl(apiUrl); //URL
                    icResultDetail.setAkapicode(akApiCode); //ak-apiCode
                    icResultDetail.setCheckrule("p_checkSensitiveData");  //检测规则
                    icResultDetail.setReqip(clientIp);  //请求IP
                    icResultDetailList.add(icResultDetail);
                }
            });

        }
        return icResultDetailList;
    }


    /**
     * 根据ak-apiCode查询唯一值
     *
     * @param client Elasticsearch的客户端
     * @param index  索引名称
     * @throws IOException IOException
     */
    private Set<String> combinedQueryRequests(RestHighLevelClient client, String index, Instant startTime, Instant endTime, boolean isDesignatedTime) throws IOException {

        // 创建初始搜索请求
        SearchRequest searchRequest = new SearchRequest(index);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.size(1000); // 每次滚动查询的文档数量

        // 是否指定具体时间,指定则按照索引进行查询,未指定则根据 @timestamp 的时间区间进行查询,主要兼容即时检测逻辑
        if (!isDesignatedTime) {
            searchSourceBuilder.query(
                    QueryBuilders.boolQuery()
                            .must(QueryBuilders.existsQuery("akapicode")));
        } else {
            searchSourceBuilder.query(
                    QueryBuilders.boolQuery()
                            .must(QueryBuilders.existsQuery("akapicode"))
                            .filter(QueryBuilders.rangeQuery("@timestamp")
                                    .from(startTime.toString())
                                    .to(endTime.toString())));
        }

        searchSourceBuilder.fetchSource(new String[]{"akapicode"}, null);
        searchSourceBuilder.sort("@timestamp", SortOrder.DESC); // 按时间排序，最新的在前
        searchRequest.source(searchSourceBuilder);
        searchRequest.scroll(TimeValue.timeValueMinutes(1L));

        // 执行初始搜索请求
        SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
        String scrollId = searchResponse.getScrollId();

        // 处理搜索响应
        Set<String> combinationDocumentSet = new HashSet<>();

        while (true) {
            for (SearchHit hit : searchResponse.getHits().getHits()) {
                String akapicode = (String) hit.getSourceAsMap().get("akapicode");
                combinationDocumentSet.add(akapicode);
            }

            // 获取下一页结果
            SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
            scrollRequest.scroll(TimeValue.timeValueMinutes(1L));
            searchResponse = client.scroll(scrollRequest, RequestOptions.DEFAULT);

            // 判断是否结束滚动查询
            if (searchResponse.getHits().getHits().length == 0) {
                break;
            }
        }

        // 清除滚动ID
        ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
        clearScrollRequest.addScrollId(scrollId);
        client.clearScroll(clearScrollRequest, RequestOptions.DEFAULT);

        return combinationDocumentSet;
    }


    /**
     * 根据ak-apiCode查询返回数据
     *
     * @param client         Elasticsearch的客户端
     * @param index          索引名称
     * @param akapicodeValue ak-apiCode值
     * @return {@link List }<{@link String }>
     * @throws IOException IOException
     */
    public static Integer combinedUnreviewedDataList(RestHighLevelClient client,
                                                     String index,
                                                     String akapicodeValue,
                                                     List<RuleDto> ruleDtoList,
                                                     List<String> sensitivityDetectionWhiteList,
                                                     Map<String, Map<String, String>> fileDataTypeExampleMap) throws Exception {


        // 存储敏感数据和数量 <敏感数据类型,数量>
        Map<String, String> fileDataTypeMap = new HashMap<>();
        // 存储数据样例Map<敏感数据类型类型,样例>
        Map<String, String> dataExampleMap = new TreeMap<>();
        // 存储风险程度及数量 <风险程度,数量>
        Map<String, String> riskNumMap = new TreeMap<>();
        int count = 0;


        // 创建初始搜索请求
        SearchRequest searchRequest = new SearchRequest(index);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.size(100); // 每次滚动查询的文档数量

        // 构建布尔查询
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.existsQuery("akapicode.keyword"));
        boolQueryBuilder.must(QueryBuilders.termQuery("akapicode.keyword", akapicodeValue));

        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.fetchSource(new FetchSourceContext(true, new String[]{"akapicode", "rescontent"}, null));
        searchRequest.source(searchSourceBuilder);

        // 设置滚动查询上下文
        final Scroll scroll = new Scroll(TimeValue.timeValueMinutes(5L));
        searchRequest.scroll(scroll);

        // 执行初始搜索请求
        SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
        String scrollId = searchResponse.getScrollId();

        int dataNum = 0;

        try {
            while (searchResponse.getHits().getHits().length > 0) {
                for (SearchHit hit : searchResponse.getHits().getHits()) {
                    try {
                        Map<String, Object> sourceMap = hit.getSourceAsMap();
                        if (sourceMap.containsKey("rescontent")) {
                            dataNum++; //统计数据条数
                            Object resContentObj = sourceMap.get("rescontent");
                            if (resContentObj instanceof Map) {
                                @SuppressWarnings("unchecked")
                                Map<String, Object> resContentMap = (Map<String, Object>) resContentObj;
                                if (resContentMap.containsKey("data")) {
                                    Object dataObj = resContentMap.get("data");
                                    if (dataObj instanceof String) {
                                        String jsonData = (String) dataObj;

                                        if (StringUtils.isNotBlank(jsonData)) {
                                            //字符截取处理
                                            String[] fieldDisassembly = jsonData.split("。|，|\\\\|\\t|\\\t|\t|\\s+|\r|\\r\\n|,|；|:|：|;|\n|\t|=|'|\"|’|‘|“|”|（|）|\\{|}|[|]|\\(|\\)|！|#|￥|……|&|\\*|!|\\$|\\^");
                                            for (String data : fieldDisassembly) {
                                                if (StringUtils.isNotBlank(data) && !data.equals("data") && !data.equals("value")
                                                        && !data.equals("t") && !data.equals("n") && !data.equals("s")
                                                        && !data.equals("r") && !data.contains("null")) {


                                                    // TODO ------ 敏感类型检测 ------
                                                    if (!sensitivityDetectionWhiteList.contains(data)) {
                                                        //对拆分后的数据依次遍历
                                                        for (RuleDto ruleDto : ruleDtoList) {
                                                            //遍历list集合中数组,拆分为字符串
                                                            // boolean checkSensResult = RuleManager.checkDataByRuleDto(data, ruleDto);

                                                            boolean checkSensResult = ProRuleFactory.checkDataByProRule(data, ruleDto.getEname());

                                                            if (checkSensResult) {
                                                                String dataType = ruleDto.getApplytypecname();//记录敏感数据类型

                                                                String risk = StringUtils.isNotBlank(ruleDto.getRisk()) ? ruleDto.getRisk() : Const.RISK_LOW;
                                                                dataType = dataType + "," + risk;

                                                                // TODO 敏感数据类型及数量
                                                                if (fileDataTypeMap.containsKey(dataType)) {
                                                                    fileDataTypeMap.put(dataType, String.valueOf(Integer.parseInt(fileDataTypeMap.get(dataType)) + 1));
                                                                } else {
                                                                    fileDataTypeMap.put(dataType, String.valueOf(1));
                                                                }

                                                                // TODO 敏感类型及数据样例
                                                                if (!dataExampleMap.containsKey(dataType)) {
                                                                    dataExampleMap.put(dataType, com.wzsec.utils.StringUtils.join(data, ","));
                                                                } else {
                                                                    // 如果已存在，则仅在尚未达到十个示例时追加
                                                                    List<String> dataExampleList = Arrays.asList(dataExampleMap.get(dataType).split(","));
                                                                    if (dataExampleList.size() < 3) {
                                                                        if (!dataExampleList.contains(data)) {
                                                                            dataExampleMap.put(dataType, dataExampleMap.get(dataType) + com.wzsec.utils.StringUtils.join(data, ","));
                                                                        }
                                                                    }
                                                                }

                                                                // TODO 级别及敏感数量
                                                                if (riskNumMap.containsKey(risk)) {
                                                                    riskNumMap.put(risk, String.valueOf(Integer.parseInt(riskNumMap.get(risk)) + 1));
                                                                } else {
                                                                    riskNumMap.put(risk, String.valueOf(1));
                                                                }
                                                                count++;
                                                                break;//已经发现该数据是敏感数据，停止改数据的检测
                                                            }
                                                        }
                                                    }
                                                    // TODO ------ 敏感类型检测 ------

                                                }
                                            }
                                        }

                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        // 记录异常，但继续处理下一个文档
                        System.err.println("处理文档时发生错误: " + e.getMessage());
                        e.printStackTrace();
                    }
                }
                // 获取下一页结果
                SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
                scrollRequest.scroll(scroll);
                searchResponse = client.scroll(scrollRequest, RequestOptions.DEFAULT);
            }


            fileDataTypeExampleMap.put("fileDataType", fileDataTypeMap);
            fileDataTypeExampleMap.put("dataExample", dataExampleMap);
            fileDataTypeExampleMap.put("riskNum", riskNumMap);

            HashMap<String, String> hashMap = new HashMap<>();
            hashMap.put("count", String.valueOf(count));
            fileDataTypeExampleMap.put("dataCount", hashMap);

        } catch (OutOfMemoryError e) {
            // 记录内存溢出错误
            System.err.println("发生内存溢出错误: " + e.getMessage());
            e.printStackTrace();
            // 可根据需要进行内存释放或其他操作
        } catch (IOException e) {
            // 记录IO异常
            System.err.println("发生IO异常: " + e.getMessage());
            e.printStackTrace();
        } finally {
            try {
                // 清除滚动ID
                ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
                clearScrollRequest.addScrollId(scrollId);
                client.clearScroll(clearScrollRequest, RequestOptions.DEFAULT);
            } catch (IOException e) {
                // 记录清除滚动ID时的异常
                System.err.println("清除滚动ID时发生错误: " + e.getMessage());
                e.printStackTrace();
            }
        }

        return dataNum;
    }


    /**
     * TODO 场景检测告警信息推送
     *
     * @param icAlarmdisposal ed报警
     */
    private static void alarmInformationPush(IcAlarmdisposal icAlarmdisposal, List<String> blackApiCodeList) {
        new MonitorRiskAlarmData().sendIcExample(icAlarmdisposal); //告警推送syslog
        // TODO API暴露敏感数据提出要求不予推送
        APIContentAnomalyDetection_v1.apiExposingSensitiveDataPushKafka_v1(icAlarmdisposal, blackApiCodeList); //告警推送kafka
    }

}

