package com.wzsec.modules.ic.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.modules.ic.domain.IcResultDetail;
import com.wzsec.modules.ic.service.IcResultDetailService;
import com.wzsec.modules.ic.service.dto.IcResultDetailQueryCriteria;
import com.wzsec.utils.DomainUtil;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-04-25
 */
// @Api(tags = "接口日志检测详情结果管理")
@RestController
@RequestMapping("/api/icResultDetail")
public class IcResultDetailController {

    private final IcResultDetailService icResultDetailService;

    public IcResultDetailController(IcResultDetailService icResultDetailService) {
        this.icResultDetailService = icResultDetailService;
    }

    @Log("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, IcResultDetailQueryCriteria criteria) throws IOException {

        // 根据申请部门名称分域
        List<String> domainAkList = DomainUtil.queryDomainAk();
        criteria.setAk(domainAkList);

        icResultDetailService.download(icResultDetailService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询接口日志检测详情结果")
    public ResponseEntity<Object> getIcResultdetails(IcResultDetailQueryCriteria criteria, Pageable pageable) {

        // 根据申请部门名称分域
        List<String> domainAkList = DomainUtil.queryDomainAk();
        criteria.setAk(domainAkList);

        return new ResponseEntity<>(icResultDetailService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增接口日志检测详情结果")
    public ResponseEntity<Object> create(@Validated @RequestBody IcResultDetail resources) {
        return new ResponseEntity<>(icResultDetailService.create(resources), HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改接口日志检测详情结果")
    public ResponseEntity<Object> update(@Validated @RequestBody IcResultDetail resources) {
        icResultDetailService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除接口日志检测详情结果")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        icResultDetailService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
