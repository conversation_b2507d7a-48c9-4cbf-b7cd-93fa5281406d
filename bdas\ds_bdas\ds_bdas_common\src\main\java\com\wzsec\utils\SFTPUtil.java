package com.wzsec.utils;

import com.jcraft.jsch.*;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileOutputStream;
import java.util.Map;
import java.util.Vector;

/**
 * @className: SFTP
 * @description: TODO SFTP工具类
 * @author: mingcb
 * @date: 2023/10/16
 **/
@Slf4j
public class SFTPUtil {

    public static void main(String[] args) {
        String host = "*************";
        int port = 22;
        String username = "root";
        String password = "<EMAIL>.!@0501";
        String remotePath = "/data/mcbtest/file/";
        String fileName = null;
        String localPath = "D:\\filemask\\fileDownload";
        //boolean file = downloadFile(host, port, username, password, remotePath, localPath);
        boolean file = uploadingFile(host,port,username,password,"D:\\testfile\\csv","/data/mcbtest/file/out");
        System.out.println(file);
    }

    public static Session getSession(String host, int port, String username, String password) throws JSchException {
        JSch jsch = new JSch();
        Session session = jsch.getSession(username, host, port);
        session.setPassword(password);
        session.setConfig("StrictHostKeyChecking", "no");// 跳过验证远程主机的key
        session.connect();
        return session;
    }

    public static void disconnect(ChannelSftp channel, Session session) {
        if (channel != null) {
            channel.disconnect();
        }
        if (session != null) {
            session.disconnect();
        }
    }

    /**
     * 下载文件到指定路径
     *
     * @param host
     * @param port
     * @param username
     * @param password
     * @param remotePath
     * @param localPath
     * @return
     */
    public static boolean downloadFile(String host, int port, String username, String password, String remotePath, String localPath) {
        boolean result = false;

        ChannelSftp channel = null;
        Session session = null;
        try {
            session = getSession(host, port, username, password);
            channel = (ChannelSftp) session.openChannel("sftp");
            channel.connect();

            // 判断远程路径是文件还是目录
            boolean isDirectory = isRemotePathDirectory(channel, remotePath);
            if (isDirectory) {
                // 如果是目录，列出目录下的文件并逐个下载
                Vector<ChannelSftp.LsEntry> files = channel.ls(remotePath);
                for (ChannelSftp.LsEntry file : files) {
                    if (!file.getAttrs().isDir()) {
                        String remoteFilePath = remotePath + File.separator + file.getFilename();
                        String localFilePath = localPath + File.separator + file.getFilename();
                        downloadFile(channel, remoteFilePath, localFilePath);
                    }
                }
            } else {
                String remoteFileName = new File(remotePath).getName();
                String localFilePath = localPath + File.separator + remoteFileName;
                // 如果是文件，直接下载
                downloadFile(channel, remotePath, localFilePath);
            }
            result = true;
        } catch (Exception e) {
            e.printStackTrace();
            log.info("下载失败，下载过程出现异常！");
            result = false;
        } finally {
            disconnect(channel, session);
            return result;
        }
    }

    /**
     * 上传文件
     *
     * @param host
     * @param port
     * @param username
     * @param password
     * @param localPath
     * @param uploadingPath
     * @return
     */
    public static boolean uploadingFile(String host, int port, String username, String password, String localPath, String uploadingPath) {
        boolean result = false;
        ChannelSftp channel = null;
        Session session = null;
        try {
            Map<String, String> filePathList = FileUtil.getFilePathList(localPath);
            session = getSession(host, port, username, password);
            channel = (ChannelSftp) session.openChannel("sftp");
            channel.connect();

            //判断uploadingPath是否是有效路径，如果报错了就创建该路径
            //ChannelSftp创建目录只能一层一层的创建，需要一层层遍历输出后路径是否存在，不存在创建
            String[] split = uploadingPath.split(Const.SEPARATOR_LINUX);
            String path = "";
            for (String s : split) {
                path += s + Const.SEPARATOR_LINUX;
                try {
                    channel.ls(path);
                    log.info("目录：" + path + " 已存在，无需创建...");
                } catch (SftpException sftpException) {
                    channel.mkdir(path);
                    log.info("目录：" + path + " 不存在，开始创建...");
                }
            }

            for (Map.Entry<String, String> entry : filePathList.entrySet()) {
                String fieldPath = entry.getKey();
                String fieldName = entry.getValue();
                String uploadingFilePath = uploadingPath.endsWith(Const.SEPARATOR_LINUX) ? (uploadingPath + fieldName) : (uploadingPath + Const.SEPARATOR_LINUX + fieldName);
                channel.put(fieldPath, uploadingFilePath); // 上传文件
                log.info("上传成功：" + uploadingFilePath);
            }
            result = true;
        } catch (Exception e) {
            log.info("上传失败，上传过程出现异常！");
            e.printStackTrace();
            result = false;
            throw e;
        } finally {
            disconnect(channel, session);
            return result;
        }
    }

    /**
     * 判断是文件还是目录
     *
     * @param channel
     * @param remotePath
     * @return
     */
    private static boolean isRemotePathDirectory(ChannelSftp channel, String remotePath) {
        try {
            channel.cd(remotePath);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 下载文件
     *
     * @param channel
     * @param remoteFilePath
     * @param localFilePath
     * @throws Exception
     */
    private static void downloadFile(ChannelSftp channel, String remoteFilePath, String localFilePath) throws Exception {
        File localFile = new File(localFilePath);
        FileOutputStream output = new FileOutputStream(localFile);
        channel.get(remoteFilePath, output);
        output.close();
    }

}
