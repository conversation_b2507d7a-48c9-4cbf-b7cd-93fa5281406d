package com.wzsec.modules.fa.service.mapper;

import com.wzsec.modules.fa.domain.Datafingerprint;
import com.wzsec.modules.fa.service.dto.DatafingerprintDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:27:08+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class DatafingerprintMapperImpl implements DatafingerprintMapper {

    @Override
    public DatafingerprintDto toDto(Datafingerprint entity) {
        if ( entity == null ) {
            return null;
        }

        DatafingerprintDto datafingerprintDto = new DatafingerprintDto();

        datafingerprintDto.setCreatetime( entity.getCreatetime() );
        datafingerprintDto.setDatafingerprint( entity.getDatafingerprint() );
        datafingerprintDto.setDatatype( entity.getDatatype() );
        datafingerprintDto.setDescription( entity.getDescription() );
        datafingerprintDto.setDlevel( entity.getDlevel() );
        datafingerprintDto.setDname( entity.getDname() );
        datafingerprintDto.setDobject( entity.getDobject() );
        datafingerprintDto.setDsize( entity.getDsize() );
        datafingerprintDto.setFingerprintsimilarity( entity.getFingerprintsimilarity() );
        datafingerprintDto.setId( entity.getId() );
        datafingerprintDto.setSparefield1( entity.getSparefield1() );
        datafingerprintDto.setSparefield2( entity.getSparefield2() );
        datafingerprintDto.setSparefield3( entity.getSparefield3() );
        datafingerprintDto.setSparefield4( entity.getSparefield4() );

        return datafingerprintDto;
    }

    @Override
    public List<DatafingerprintDto> toDto(List<Datafingerprint> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<DatafingerprintDto> list = new ArrayList<DatafingerprintDto>( entityList.size() );
        for ( Datafingerprint datafingerprint : entityList ) {
            list.add( toDto( datafingerprint ) );
        }

        return list;
    }

    @Override
    public Datafingerprint toEntity(DatafingerprintDto dto) {
        if ( dto == null ) {
            return null;
        }

        Datafingerprint datafingerprint = new Datafingerprint();

        datafingerprint.setCreatetime( dto.getCreatetime() );
        datafingerprint.setDatafingerprint( dto.getDatafingerprint() );
        datafingerprint.setDatatype( dto.getDatatype() );
        datafingerprint.setDescription( dto.getDescription() );
        datafingerprint.setDlevel( dto.getDlevel() );
        datafingerprint.setDname( dto.getDname() );
        datafingerprint.setDobject( dto.getDobject() );
        datafingerprint.setDsize( dto.getDsize() );
        datafingerprint.setFingerprintsimilarity( dto.getFingerprintsimilarity() );
        datafingerprint.setId( dto.getId() );
        datafingerprint.setSparefield1( dto.getSparefield1() );
        datafingerprint.setSparefield2( dto.getSparefield2() );
        datafingerprint.setSparefield3( dto.getSparefield3() );
        datafingerprint.setSparefield4( dto.getSparefield4() );

        return datafingerprint;
    }

    @Override
    public List<Datafingerprint> toEntity(List<DatafingerprintDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Datafingerprint> list = new ArrayList<Datafingerprint>( dtoList.size() );
        for ( DatafingerprintDto datafingerprintDto : dtoList ) {
            list.add( toEntity( datafingerprintDto ) );
        }

        return list;
    }
}
