package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.ApiInvolvesaccountpassword;
import com.wzsec.modules.ic.service.dto.ApiInvolvesaccountpasswordDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:01+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ApiInvolvesaccountpasswordMapperImpl implements ApiInvolvesaccountpasswordMapper {

    @Override
    public ApiInvolvesaccountpassword toEntity(ApiInvolvesaccountpasswordDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiInvolvesaccountpassword apiInvolvesaccountpassword = new ApiInvolvesaccountpassword();

        apiInvolvesaccountpassword.setId( dto.getId() );
        apiInvolvesaccountpassword.setApicode( dto.getApicode() );
        apiInvolvesaccountpassword.setApiname( dto.getApiname() );
        apiInvolvesaccountpassword.setRequesttype( dto.getRequesttype() );
        apiInvolvesaccountpassword.setInvolvesaccount( dto.getInvolvesaccount() );
        apiInvolvesaccountpassword.setRisk( dto.getRisk() );
        apiInvolvesaccountpassword.setChecktime( dto.getChecktime() );
        apiInvolvesaccountpassword.setSparefield1( dto.getSparefield1() );
        apiInvolvesaccountpassword.setSparefield2( dto.getSparefield2() );
        apiInvolvesaccountpassword.setSparefield3( dto.getSparefield3() );
        apiInvolvesaccountpassword.setSparefield4( dto.getSparefield4() );
        apiInvolvesaccountpassword.setApiurl( dto.getApiurl() );
        apiInvolvesaccountpassword.setAk( dto.getAk() );
        apiInvolvesaccountpassword.setApplyorgname( dto.getApplyorgname() );
        apiInvolvesaccountpassword.setAkapicode( dto.getAkapicode() );
        apiInvolvesaccountpassword.setSystemname( dto.getSystemname() );
        apiInvolvesaccountpassword.setReqip( dto.getReqip() );

        return apiInvolvesaccountpassword;
    }

    @Override
    public ApiInvolvesaccountpasswordDto toDto(ApiInvolvesaccountpassword entity) {
        if ( entity == null ) {
            return null;
        }

        ApiInvolvesaccountpasswordDto apiInvolvesaccountpasswordDto = new ApiInvolvesaccountpasswordDto();

        apiInvolvesaccountpasswordDto.setId( entity.getId() );
        apiInvolvesaccountpasswordDto.setApicode( entity.getApicode() );
        apiInvolvesaccountpasswordDto.setApiname( entity.getApiname() );
        apiInvolvesaccountpasswordDto.setRequesttype( entity.getRequesttype() );
        apiInvolvesaccountpasswordDto.setInvolvesaccount( entity.getInvolvesaccount() );
        apiInvolvesaccountpasswordDto.setRisk( entity.getRisk() );
        apiInvolvesaccountpasswordDto.setChecktime( entity.getChecktime() );
        apiInvolvesaccountpasswordDto.setSparefield1( entity.getSparefield1() );
        apiInvolvesaccountpasswordDto.setSparefield2( entity.getSparefield2() );
        apiInvolvesaccountpasswordDto.setSparefield3( entity.getSparefield3() );
        apiInvolvesaccountpasswordDto.setSparefield4( entity.getSparefield4() );
        apiInvolvesaccountpasswordDto.setApiurl( entity.getApiurl() );
        apiInvolvesaccountpasswordDto.setAk( entity.getAk() );
        apiInvolvesaccountpasswordDto.setApplyorgname( entity.getApplyorgname() );
        apiInvolvesaccountpasswordDto.setAkapicode( entity.getAkapicode() );
        apiInvolvesaccountpasswordDto.setSystemname( entity.getSystemname() );
        apiInvolvesaccountpasswordDto.setReqip( entity.getReqip() );

        return apiInvolvesaccountpasswordDto;
    }

    @Override
    public List<ApiInvolvesaccountpassword> toEntity(List<ApiInvolvesaccountpasswordDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiInvolvesaccountpassword> list = new ArrayList<ApiInvolvesaccountpassword>( dtoList.size() );
        for ( ApiInvolvesaccountpasswordDto apiInvolvesaccountpasswordDto : dtoList ) {
            list.add( toEntity( apiInvolvesaccountpasswordDto ) );
        }

        return list;
    }

    @Override
    public List<ApiInvolvesaccountpasswordDto> toDto(List<ApiInvolvesaccountpassword> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiInvolvesaccountpasswordDto> list = new ArrayList<ApiInvolvesaccountpasswordDto>( entityList.size() );
        for ( ApiInvolvesaccountpassword apiInvolvesaccountpassword : entityList ) {
            list.add( toDto( apiInvolvesaccountpassword ) );
        }

        return list;
    }
}
