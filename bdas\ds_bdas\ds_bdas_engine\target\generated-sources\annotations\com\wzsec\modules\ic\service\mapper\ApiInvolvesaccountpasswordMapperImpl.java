package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.ApiInvolvesaccountpassword;
import com.wzsec.modules.ic.service.dto.ApiInvolvesaccountpasswordDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:27:08+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ApiInvolvesaccountpasswordMapperImpl implements ApiInvolvesaccountpasswordMapper {

    @Override
    public ApiInvolvesaccountpasswordDto toDto(ApiInvolvesaccountpassword entity) {
        if ( entity == null ) {
            return null;
        }

        ApiInvolvesaccountpasswordDto apiInvolvesaccountpasswordDto = new ApiInvolvesaccountpasswordDto();

        apiInvolvesaccountpasswordDto.setAk( entity.getAk() );
        apiInvolvesaccountpasswordDto.setAkapicode( entity.getAkapicode() );
        apiInvolvesaccountpasswordDto.setApicode( entity.getApicode() );
        apiInvolvesaccountpasswordDto.setApiname( entity.getApiname() );
        apiInvolvesaccountpasswordDto.setApiurl( entity.getApiurl() );
        apiInvolvesaccountpasswordDto.setApplyorgname( entity.getApplyorgname() );
        apiInvolvesaccountpasswordDto.setChecktime( entity.getChecktime() );
        apiInvolvesaccountpasswordDto.setId( entity.getId() );
        apiInvolvesaccountpasswordDto.setInvolvesaccount( entity.getInvolvesaccount() );
        apiInvolvesaccountpasswordDto.setReqip( entity.getReqip() );
        apiInvolvesaccountpasswordDto.setRequesttype( entity.getRequesttype() );
        apiInvolvesaccountpasswordDto.setRisk( entity.getRisk() );
        apiInvolvesaccountpasswordDto.setSparefield1( entity.getSparefield1() );
        apiInvolvesaccountpasswordDto.setSparefield2( entity.getSparefield2() );
        apiInvolvesaccountpasswordDto.setSparefield3( entity.getSparefield3() );
        apiInvolvesaccountpasswordDto.setSparefield4( entity.getSparefield4() );
        apiInvolvesaccountpasswordDto.setSystemname( entity.getSystemname() );

        return apiInvolvesaccountpasswordDto;
    }

    @Override
    public List<ApiInvolvesaccountpasswordDto> toDto(List<ApiInvolvesaccountpassword> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiInvolvesaccountpasswordDto> list = new ArrayList<ApiInvolvesaccountpasswordDto>( entityList.size() );
        for ( ApiInvolvesaccountpassword apiInvolvesaccountpassword : entityList ) {
            list.add( toDto( apiInvolvesaccountpassword ) );
        }

        return list;
    }

    @Override
    public ApiInvolvesaccountpassword toEntity(ApiInvolvesaccountpasswordDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiInvolvesaccountpassword apiInvolvesaccountpassword = new ApiInvolvesaccountpassword();

        apiInvolvesaccountpassword.setAk( dto.getAk() );
        apiInvolvesaccountpassword.setAkapicode( dto.getAkapicode() );
        apiInvolvesaccountpassword.setApicode( dto.getApicode() );
        apiInvolvesaccountpassword.setApiname( dto.getApiname() );
        apiInvolvesaccountpassword.setApiurl( dto.getApiurl() );
        apiInvolvesaccountpassword.setApplyorgname( dto.getApplyorgname() );
        apiInvolvesaccountpassword.setChecktime( dto.getChecktime() );
        apiInvolvesaccountpassword.setId( dto.getId() );
        apiInvolvesaccountpassword.setInvolvesaccount( dto.getInvolvesaccount() );
        apiInvolvesaccountpassword.setReqip( dto.getReqip() );
        apiInvolvesaccountpassword.setRequesttype( dto.getRequesttype() );
        apiInvolvesaccountpassword.setRisk( dto.getRisk() );
        apiInvolvesaccountpassword.setSparefield1( dto.getSparefield1() );
        apiInvolvesaccountpassword.setSparefield2( dto.getSparefield2() );
        apiInvolvesaccountpassword.setSparefield3( dto.getSparefield3() );
        apiInvolvesaccountpassword.setSparefield4( dto.getSparefield4() );
        apiInvolvesaccountpassword.setSystemname( dto.getSystemname() );

        return apiInvolvesaccountpassword;
    }

    @Override
    public List<ApiInvolvesaccountpassword> toEntity(List<ApiInvolvesaccountpasswordDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiInvolvesaccountpassword> list = new ArrayList<ApiInvolvesaccountpassword>( dtoList.size() );
        for ( ApiInvolvesaccountpasswordDto apiInvolvesaccountpasswordDto : dtoList ) {
            list.add( toEntity( apiInvolvesaccountpasswordDto ) );
        }

        return list;
    }
}
