package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcResultDetail;
import com.wzsec.modules.ic.service.dto.IcResultDetailDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:28+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class IcResultDetailMapperImpl implements IcResultDetailMapper {

    @Override
    public IcResultDetailDto toDto(IcResultDetail entity) {
        if ( entity == null ) {
            return null;
        }

        IcResultDetailDto icResultDetailDto = new IcResultDetailDto();

        icResultDetailDto.setAk( entity.getAk() );
        icResultDetailDto.setAkapicode( entity.getAkapicode() );
        icResultDetailDto.setApicode( entity.getApicode() );
        icResultDetailDto.setApimethod( entity.getApimethod() );
        icResultDetailDto.setApiname( entity.getApiname() );
        icResultDetailDto.setApitype( entity.getApitype() );
        icResultDetailDto.setApiurl( entity.getApiurl() );
        icResultDetailDto.setAppid( entity.getAppid() );
        icResultDetailDto.setApplyorgname( entity.getApplyorgname() );
        icResultDetailDto.setAppname( entity.getAppname() );
        icResultDetailDto.setCheckcount( entity.getCheckcount() );
        icResultDetailDto.setChecklinecount( entity.getChecklinecount() );
        icResultDetailDto.setCheckparam( entity.getCheckparam() );
        icResultDetailDto.setCheckrule( entity.getCheckrule() );
        icResultDetailDto.setChecktime( entity.getChecktime() );
        icResultDetailDto.setCustSimplename( entity.getCustSimplename() );
        icResultDetailDto.setCustname( entity.getCustname() );
        icResultDetailDto.setExample( entity.getExample() );
        icResultDetailDto.setId( entity.getId() );
        icResultDetailDto.setImportance( entity.getImportance() );
        icResultDetailDto.setLogsign( entity.getLogsign() );
        icResultDetailDto.setParamMean( entity.getParamMean() );
        icResultDetailDto.setRatio( entity.getRatio() );
        icResultDetailDto.setReqip( entity.getReqip() );
        icResultDetailDto.setResulttype( entity.getResulttype() );
        icResultDetailDto.setRisk( entity.getRisk() );
        icResultDetailDto.setSensitivedata( entity.getSensitivedata() );
        icResultDetailDto.setSparefield1( entity.getSparefield1() );
        icResultDetailDto.setSparefield2( entity.getSparefield2() );
        icResultDetailDto.setSparefield3( entity.getSparefield3() );
        icResultDetailDto.setSparefield4( entity.getSparefield4() );
        icResultDetailDto.setTaskname( entity.getTaskname() );
        icResultDetailDto.setTotalcount( entity.getTotalcount() );
        icResultDetailDto.setTotallinecount( entity.getTotallinecount() );
        icResultDetailDto.setUserid( entity.getUserid() );

        return icResultDetailDto;
    }

    @Override
    public List<IcResultDetailDto> toDto(List<IcResultDetail> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcResultDetailDto> list = new ArrayList<IcResultDetailDto>( entityList.size() );
        for ( IcResultDetail icResultDetail : entityList ) {
            list.add( toDto( icResultDetail ) );
        }

        return list;
    }

    @Override
    public IcResultDetail toEntity(IcResultDetailDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcResultDetail icResultDetail = new IcResultDetail();

        icResultDetail.setAk( dto.getAk() );
        icResultDetail.setAkapicode( dto.getAkapicode() );
        icResultDetail.setApicode( dto.getApicode() );
        icResultDetail.setApimethod( dto.getApimethod() );
        icResultDetail.setApiname( dto.getApiname() );
        icResultDetail.setApitype( dto.getApitype() );
        icResultDetail.setApiurl( dto.getApiurl() );
        icResultDetail.setAppid( dto.getAppid() );
        icResultDetail.setApplyorgname( dto.getApplyorgname() );
        icResultDetail.setAppname( dto.getAppname() );
        icResultDetail.setCheckcount( dto.getCheckcount() );
        icResultDetail.setChecklinecount( dto.getChecklinecount() );
        icResultDetail.setCheckparam( dto.getCheckparam() );
        icResultDetail.setCheckrule( dto.getCheckrule() );
        icResultDetail.setChecktime( dto.getChecktime() );
        icResultDetail.setCustSimplename( dto.getCustSimplename() );
        icResultDetail.setCustname( dto.getCustname() );
        icResultDetail.setExample( dto.getExample() );
        icResultDetail.setId( dto.getId() );
        icResultDetail.setImportance( dto.getImportance() );
        icResultDetail.setLogsign( dto.getLogsign() );
        icResultDetail.setParamMean( dto.getParamMean() );
        icResultDetail.setRatio( dto.getRatio() );
        icResultDetail.setReqip( dto.getReqip() );
        icResultDetail.setResulttype( dto.getResulttype() );
        icResultDetail.setRisk( dto.getRisk() );
        icResultDetail.setSensitivedata( dto.getSensitivedata() );
        icResultDetail.setSparefield1( dto.getSparefield1() );
        icResultDetail.setSparefield2( dto.getSparefield2() );
        icResultDetail.setSparefield3( dto.getSparefield3() );
        icResultDetail.setSparefield4( dto.getSparefield4() );
        icResultDetail.setTaskname( dto.getTaskname() );
        icResultDetail.setTotalcount( dto.getTotalcount() );
        icResultDetail.setTotallinecount( dto.getTotallinecount() );
        icResultDetail.setUserid( dto.getUserid() );

        return icResultDetail;
    }

    @Override
    public List<IcResultDetail> toEntity(List<IcResultDetailDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcResultDetail> list = new ArrayList<IcResultDetail>( dtoList.size() );
        for ( IcResultDetailDto icResultDetailDto : dtoList ) {
            list.add( toEntity( icResultDetailDto ) );
        }

        return list;
    }
}
