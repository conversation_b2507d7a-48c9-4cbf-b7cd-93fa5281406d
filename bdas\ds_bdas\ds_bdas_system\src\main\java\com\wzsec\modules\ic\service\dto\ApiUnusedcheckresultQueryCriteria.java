package com.wzsec.modules.ic.service.dto;

import com.wzsec.annotation.Query;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-05-19
 */
@Data
public class ApiUnusedcheckresultQueryCriteria {

    /** 支持精确查询和IN查询：单个值时精确查询，逗号分隔字符串或List时IN查询 */
    @Query(type = Query.Type.IN)
    private Object apicode;

    private Timestamp dateStart;

    private Timestamp dateEnd;

    @Query(type = Query.Type.IN)
    private List<String> sparefield4;

    @Query
    private String risk;

    @Query(type = Query.Type.BETWEEN)
    private List<String> checktime;

    @Query(type = Query.Type.INNER_LIKE)
    private String sparefield3;


    @Query(blurry = "apicode,risk,sparefield3")
    private String blurry;
}
