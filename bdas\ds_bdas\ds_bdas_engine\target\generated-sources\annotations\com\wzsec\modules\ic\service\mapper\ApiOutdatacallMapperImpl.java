package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.ApiOutdatacall;
import com.wzsec.modules.ic.service.dto.ApiOutdatacallDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:02+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ApiOutdatacallMapperImpl implements ApiOutdatacallMapper {

    @Override
    public ApiOutdatacall toEntity(ApiOutdatacallDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiOutdatacall apiOutdatacall = new ApiOutdatacall();

        apiOutdatacall.setId( dto.getId() );
        apiOutdatacall.setAk( dto.getAk() );
        apiOutdatacall.setApplyorgname( dto.getApplyorgname() );
        apiOutdatacall.setApicode( dto.getApicode() );
        apiOutdatacall.setApiname( dto.getApiname() );
        apiOutdatacall.setAkapicode( dto.getAkapicode() );
        apiOutdatacall.setSystemname( dto.getSystemname() );
        apiOutdatacall.setResultcount( dto.getResultcount() );
        apiOutdatacall.setAvgcount( dto.getAvgcount() );
        apiOutdatacall.setRisk( dto.getRisk() );
        apiOutdatacall.setChecktime( dto.getChecktime() );
        apiOutdatacall.setSparefield1( dto.getSparefield1() );
        apiOutdatacall.setSparefield2( dto.getSparefield2() );
        apiOutdatacall.setSparefield3( dto.getSparefield3() );
        apiOutdatacall.setSparefield4( dto.getSparefield4() );
        apiOutdatacall.setReqip( dto.getReqip() );
        apiOutdatacall.setApiurl( dto.getApiurl() );

        return apiOutdatacall;
    }

    @Override
    public ApiOutdatacallDto toDto(ApiOutdatacall entity) {
        if ( entity == null ) {
            return null;
        }

        ApiOutdatacallDto apiOutdatacallDto = new ApiOutdatacallDto();

        apiOutdatacallDto.setId( entity.getId() );
        apiOutdatacallDto.setAk( entity.getAk() );
        apiOutdatacallDto.setApplyorgname( entity.getApplyorgname() );
        apiOutdatacallDto.setApicode( entity.getApicode() );
        apiOutdatacallDto.setApiname( entity.getApiname() );
        apiOutdatacallDto.setAkapicode( entity.getAkapicode() );
        apiOutdatacallDto.setSystemname( entity.getSystemname() );
        apiOutdatacallDto.setResultcount( entity.getResultcount() );
        apiOutdatacallDto.setAvgcount( entity.getAvgcount() );
        apiOutdatacallDto.setRisk( entity.getRisk() );
        apiOutdatacallDto.setChecktime( entity.getChecktime() );
        apiOutdatacallDto.setSparefield1( entity.getSparefield1() );
        apiOutdatacallDto.setSparefield2( entity.getSparefield2() );
        apiOutdatacallDto.setSparefield3( entity.getSparefield3() );
        apiOutdatacallDto.setSparefield4( entity.getSparefield4() );
        apiOutdatacallDto.setReqip( entity.getReqip() );
        apiOutdatacallDto.setApiurl( entity.getApiurl() );

        return apiOutdatacallDto;
    }

    @Override
    public List<ApiOutdatacall> toEntity(List<ApiOutdatacallDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiOutdatacall> list = new ArrayList<ApiOutdatacall>( dtoList.size() );
        for ( ApiOutdatacallDto apiOutdatacallDto : dtoList ) {
            list.add( toEntity( apiOutdatacallDto ) );
        }

        return list;
    }

    @Override
    public List<ApiOutdatacallDto> toDto(List<ApiOutdatacall> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiOutdatacallDto> list = new ArrayList<ApiOutdatacallDto>( entityList.size() );
        for ( ApiOutdatacall apiOutdatacall : entityList ) {
            list.add( toDto( apiOutdatacall ) );
        }

        return list;
    }
}
