package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.ApiOutdatacall;
import com.wzsec.modules.ic.service.dto.ApiOutdatacallDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:27:07+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ApiOutdatacallMapperImpl implements ApiOutdatacallMapper {

    @Override
    public ApiOutdatacallDto toDto(ApiOutdatacall entity) {
        if ( entity == null ) {
            return null;
        }

        ApiOutdatacallDto apiOutdatacallDto = new ApiOutdatacallDto();

        apiOutdatacallDto.setAk( entity.getAk() );
        apiOutdatacallDto.setAkapicode( entity.getAkapicode() );
        apiOutdatacallDto.setApicode( entity.getApicode() );
        apiOutdatacallDto.setApiname( entity.getApiname() );
        apiOutdatacallDto.setApiurl( entity.getApiurl() );
        apiOutdatacallDto.setApplyorgname( entity.getApplyorgname() );
        apiOutdatacallDto.setAvgcount( entity.getAvgcount() );
        apiOutdatacallDto.setChecktime( entity.getChecktime() );
        apiOutdatacallDto.setId( entity.getId() );
        apiOutdatacallDto.setReqip( entity.getReqip() );
        apiOutdatacallDto.setResultcount( entity.getResultcount() );
        apiOutdatacallDto.setRisk( entity.getRisk() );
        apiOutdatacallDto.setSparefield1( entity.getSparefield1() );
        apiOutdatacallDto.setSparefield2( entity.getSparefield2() );
        apiOutdatacallDto.setSparefield3( entity.getSparefield3() );
        apiOutdatacallDto.setSparefield4( entity.getSparefield4() );
        apiOutdatacallDto.setSystemname( entity.getSystemname() );

        return apiOutdatacallDto;
    }

    @Override
    public List<ApiOutdatacallDto> toDto(List<ApiOutdatacall> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiOutdatacallDto> list = new ArrayList<ApiOutdatacallDto>( entityList.size() );
        for ( ApiOutdatacall apiOutdatacall : entityList ) {
            list.add( toDto( apiOutdatacall ) );
        }

        return list;
    }

    @Override
    public ApiOutdatacall toEntity(ApiOutdatacallDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiOutdatacall apiOutdatacall = new ApiOutdatacall();

        apiOutdatacall.setAk( dto.getAk() );
        apiOutdatacall.setAkapicode( dto.getAkapicode() );
        apiOutdatacall.setApicode( dto.getApicode() );
        apiOutdatacall.setApiname( dto.getApiname() );
        apiOutdatacall.setApiurl( dto.getApiurl() );
        apiOutdatacall.setApplyorgname( dto.getApplyorgname() );
        apiOutdatacall.setAvgcount( dto.getAvgcount() );
        apiOutdatacall.setChecktime( dto.getChecktime() );
        apiOutdatacall.setId( dto.getId() );
        apiOutdatacall.setReqip( dto.getReqip() );
        apiOutdatacall.setResultcount( dto.getResultcount() );
        apiOutdatacall.setRisk( dto.getRisk() );
        apiOutdatacall.setSparefield1( dto.getSparefield1() );
        apiOutdatacall.setSparefield2( dto.getSparefield2() );
        apiOutdatacall.setSparefield3( dto.getSparefield3() );
        apiOutdatacall.setSparefield4( dto.getSparefield4() );
        apiOutdatacall.setSystemname( dto.getSystemname() );

        return apiOutdatacall;
    }

    @Override
    public List<ApiOutdatacall> toEntity(List<ApiOutdatacallDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiOutdatacall> list = new ArrayList<ApiOutdatacall>( dtoList.size() );
        for ( ApiOutdatacallDto apiOutdatacallDto : dtoList ) {
            list.add( toEntity( apiOutdatacallDto ) );
        }

        return list;
    }
}
