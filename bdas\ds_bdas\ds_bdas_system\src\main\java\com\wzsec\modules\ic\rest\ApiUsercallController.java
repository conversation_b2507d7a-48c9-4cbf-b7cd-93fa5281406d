package com.wzsec.modules.ic.rest;

import cn.hutool.core.lang.Console;
import com.wzsec.aop.log.Log;
import com.wzsec.modules.ic.domain.ApiUsercall;
import com.wzsec.modules.ic.service.ApiUsercallService;
import com.wzsec.modules.ic.service.dto.ApiUsercallQueryCriteria;
import com.wzsec.utils.DomainUtil;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-05-19
 */
@RestController
@RequestMapping("/api/apiUsercall")
public class ApiUsercallController {

    private final ApiUsercallService apiUsercallService;

    public ApiUsercallController(ApiUsercallService apiUsercallService) {
        this.apiUsercallService = apiUsercallService;
    }

    @Log("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, ApiUsercallQueryCriteria criteria) throws IOException {
        try {
            Timestamp dateStart = criteria.getDateStart();
            Timestamp dateEnd = criteria.getDateEnd();
            List<String> checkTime = new ArrayList<>();
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateStart));
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateEnd));
            criteria.setChecktime(checkTime);
        } catch (Exception e) {
            Console.log("未填写查询时间");
        }

        // 根据申请部门名称分域
        List<String> domainApiCodeList = DomainUtil.queryDomainApiCode();
        criteria.setAccount(domainApiCodeList);

        apiUsercallService.download(apiUsercallService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询用户API调用量")
    public ResponseEntity<Object> getApiUsercalls(ApiUsercallQueryCriteria criteria, Pageable pageable) {
        try {
            Timestamp dateStart = criteria.getDateStart();
            Timestamp dateEnd = criteria.getDateEnd();
            List<String> checkTime = new ArrayList<>();
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateStart));
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateEnd));
            criteria.setChecktime(checkTime);
        } catch (Exception e) {
            Console.log("未填写查询时间");
        }

        // 根据申请部门名称分域
        List<String> domainApiCodeList = DomainUtil.queryDomainApiCode();
        criteria.setAccount(domainApiCodeList);

        return new ResponseEntity<>(apiUsercallService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增用户API调用量")
    public ResponseEntity<Object> create(@Validated @RequestBody ApiUsercall resources) {
        return new ResponseEntity<>(apiUsercallService.create(resources), HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改用户API调用量")
    public ResponseEntity<Object> update(@Validated @RequestBody ApiUsercall resources) {
        apiUsercallService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除用户API调用量")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        apiUsercallService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
