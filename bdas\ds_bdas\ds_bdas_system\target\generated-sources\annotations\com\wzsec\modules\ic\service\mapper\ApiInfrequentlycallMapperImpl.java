package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.ApiInfrequentlycall;
import com.wzsec.modules.ic.service.dto.ApiInfrequentlycallDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:26:46+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ApiInfrequentlycallMapperImpl implements ApiInfrequentlycallMapper {

    @Override
    public ApiInfrequentlycallDto toDto(ApiInfrequentlycall entity) {
        if ( entity == null ) {
            return null;
        }

        ApiInfrequentlycallDto apiInfrequentlycallDto = new ApiInfrequentlycallDto();

        apiInfrequentlycallDto.setAk( entity.getAk() );
        apiInfrequentlycallDto.setAkapicode( entity.getAkapicode() );
        apiInfrequentlycallDto.setApicode( entity.getApicode() );
        apiInfrequentlycallDto.setApiname( entity.getApiname() );
        apiInfrequentlycallDto.setApiurl( entity.getApiurl() );
        apiInfrequentlycallDto.setApplyorgname( entity.getApplyorgname() );
        apiInfrequentlycallDto.setCallcount( entity.getCallcount() );
        apiInfrequentlycallDto.setChecktime( entity.getChecktime() );
        apiInfrequentlycallDto.setId( entity.getId() );
        apiInfrequentlycallDto.setReqip( entity.getReqip() );
        apiInfrequentlycallDto.setRisk( entity.getRisk() );
        apiInfrequentlycallDto.setSparefield1( entity.getSparefield1() );
        apiInfrequentlycallDto.setSparefield2( entity.getSparefield2() );
        apiInfrequentlycallDto.setSparefield3( entity.getSparefield3() );
        apiInfrequentlycallDto.setSparefield4( entity.getSparefield4() );
        apiInfrequentlycallDto.setSystemname( entity.getSystemname() );

        return apiInfrequentlycallDto;
    }

    @Override
    public List<ApiInfrequentlycallDto> toDto(List<ApiInfrequentlycall> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiInfrequentlycallDto> list = new ArrayList<ApiInfrequentlycallDto>( entityList.size() );
        for ( ApiInfrequentlycall apiInfrequentlycall : entityList ) {
            list.add( toDto( apiInfrequentlycall ) );
        }

        return list;
    }

    @Override
    public ApiInfrequentlycall toEntity(ApiInfrequentlycallDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiInfrequentlycall apiInfrequentlycall = new ApiInfrequentlycall();

        apiInfrequentlycall.setAk( dto.getAk() );
        apiInfrequentlycall.setAkapicode( dto.getAkapicode() );
        apiInfrequentlycall.setApicode( dto.getApicode() );
        apiInfrequentlycall.setApiname( dto.getApiname() );
        apiInfrequentlycall.setApiurl( dto.getApiurl() );
        apiInfrequentlycall.setApplyorgname( dto.getApplyorgname() );
        apiInfrequentlycall.setCallcount( dto.getCallcount() );
        apiInfrequentlycall.setChecktime( dto.getChecktime() );
        apiInfrequentlycall.setId( dto.getId() );
        apiInfrequentlycall.setReqip( dto.getReqip() );
        apiInfrequentlycall.setRisk( dto.getRisk() );
        apiInfrequentlycall.setSparefield1( dto.getSparefield1() );
        apiInfrequentlycall.setSparefield2( dto.getSparefield2() );
        apiInfrequentlycall.setSparefield3( dto.getSparefield3() );
        apiInfrequentlycall.setSparefield4( dto.getSparefield4() );
        apiInfrequentlycall.setSystemname( dto.getSystemname() );

        return apiInfrequentlycall;
    }

    @Override
    public List<ApiInfrequentlycall> toEntity(List<ApiInfrequentlycallDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiInfrequentlycall> list = new ArrayList<ApiInfrequentlycall>( dtoList.size() );
        for ( ApiInfrequentlycallDto apiInfrequentlycallDto : dtoList ) {
            list.add( toEntity( apiInfrequentlycallDto ) );
        }

        return list;
    }
}
