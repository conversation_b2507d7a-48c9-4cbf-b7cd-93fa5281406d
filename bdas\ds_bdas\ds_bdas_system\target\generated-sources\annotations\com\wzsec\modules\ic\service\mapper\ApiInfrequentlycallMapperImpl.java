package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.ApiInfrequentlycall;
import com.wzsec.modules.ic.service.dto.ApiInfrequentlycallDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:30+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ApiInfrequentlycallMapperImpl implements ApiInfrequentlycallMapper {

    @Override
    public ApiInfrequentlycall toEntity(ApiInfrequentlycallDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiInfrequentlycall apiInfrequentlycall = new ApiInfrequentlycall();

        apiInfrequentlycall.setId( dto.getId() );
        apiInfrequentlycall.setApicode( dto.getApicode() );
        apiInfrequentlycall.setCallcount( dto.getCallcount() );
        apiInfrequentlycall.setRisk( dto.getRisk() );
        apiInfrequentlycall.setChecktime( dto.getChecktime() );
        apiInfrequentlycall.setSparefield1( dto.getSparefield1() );
        apiInfrequentlycall.setSparefield2( dto.getSparefield2() );
        apiInfrequentlycall.setSparefield3( dto.getSparefield3() );
        apiInfrequentlycall.setSparefield4( dto.getSparefield4() );
        apiInfrequentlycall.setApiname( dto.getApiname() );
        apiInfrequentlycall.setApiurl( dto.getApiurl() );
        apiInfrequentlycall.setAk( dto.getAk() );
        apiInfrequentlycall.setApplyorgname( dto.getApplyorgname() );
        apiInfrequentlycall.setAkapicode( dto.getAkapicode() );
        apiInfrequentlycall.setSystemname( dto.getSystemname() );
        apiInfrequentlycall.setReqip( dto.getReqip() );

        return apiInfrequentlycall;
    }

    @Override
    public ApiInfrequentlycallDto toDto(ApiInfrequentlycall entity) {
        if ( entity == null ) {
            return null;
        }

        ApiInfrequentlycallDto apiInfrequentlycallDto = new ApiInfrequentlycallDto();

        apiInfrequentlycallDto.setId( entity.getId() );
        apiInfrequentlycallDto.setApicode( entity.getApicode() );
        apiInfrequentlycallDto.setCallcount( entity.getCallcount() );
        apiInfrequentlycallDto.setRisk( entity.getRisk() );
        apiInfrequentlycallDto.setChecktime( entity.getChecktime() );
        apiInfrequentlycallDto.setSparefield1( entity.getSparefield1() );
        apiInfrequentlycallDto.setSparefield2( entity.getSparefield2() );
        apiInfrequentlycallDto.setSparefield3( entity.getSparefield3() );
        apiInfrequentlycallDto.setSparefield4( entity.getSparefield4() );
        apiInfrequentlycallDto.setApiname( entity.getApiname() );
        apiInfrequentlycallDto.setApiurl( entity.getApiurl() );
        apiInfrequentlycallDto.setAk( entity.getAk() );
        apiInfrequentlycallDto.setApplyorgname( entity.getApplyorgname() );
        apiInfrequentlycallDto.setAkapicode( entity.getAkapicode() );
        apiInfrequentlycallDto.setSystemname( entity.getSystemname() );
        apiInfrequentlycallDto.setReqip( entity.getReqip() );

        return apiInfrequentlycallDto;
    }

    @Override
    public List<ApiInfrequentlycall> toEntity(List<ApiInfrequentlycallDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiInfrequentlycall> list = new ArrayList<ApiInfrequentlycall>( dtoList.size() );
        for ( ApiInfrequentlycallDto apiInfrequentlycallDto : dtoList ) {
            list.add( toEntity( apiInfrequentlycallDto ) );
        }

        return list;
    }

    @Override
    public List<ApiInfrequentlycallDto> toDto(List<ApiInfrequentlycall> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiInfrequentlycallDto> list = new ArrayList<ApiInfrequentlycallDto>( entityList.size() );
        for ( ApiInfrequentlycall apiInfrequentlycall : entityList ) {
            list.add( toDto( apiInfrequentlycall ) );
        }

        return list;
    }
}
