package com.wzsec.modules.sdd.weakpwd.service.mapper;

import com.wzsec.modules.sdd.weakpwd.domain.WPTask;
import com.wzsec.modules.sdd.weakpwd.service.dto.WPTaskDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:01+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class WPTaskMapperImpl implements WPTaskMapper {

    @Override
    public WPTask toEntity(WPTaskDto dto) {
        if ( dto == null ) {
            return null;
        }

        WPTask wPTask = new WPTask();

        wPTask.setId( dto.getId() );
        wPTask.setTaskno( dto.getTaskno() );
        wPTask.setSubmittype( dto.getSubmittype() );
        wPTask.setCron( dto.getCron() );
        wPTask.setExecutestate( dto.getExecutestate() );
        wPTask.setState( dto.getState() );
        wPTask.setNote( dto.getNote() );
        wPTask.setCreateuser( dto.getCreateuser() );
        wPTask.setCreatetime( dto.getCreatetime() );
        wPTask.setUpdateuser( dto.getUpdateuser() );
        wPTask.setUpdatetime( dto.getUpdatetime() );
        wPTask.setSparefield1( dto.getSparefield1() );
        wPTask.setSparefield2( dto.getSparefield2() );
        wPTask.setSparefield3( dto.getSparefield3() );
        wPTask.setSparefield4( dto.getSparefield4() );
        wPTask.setSparefield5( dto.getSparefield5() );

        return wPTask;
    }

    @Override
    public WPTaskDto toDto(WPTask entity) {
        if ( entity == null ) {
            return null;
        }

        WPTaskDto wPTaskDto = new WPTaskDto();

        wPTaskDto.setId( entity.getId() );
        wPTaskDto.setTaskno( entity.getTaskno() );
        wPTaskDto.setSubmittype( entity.getSubmittype() );
        wPTaskDto.setCron( entity.getCron() );
        wPTaskDto.setExecutestate( entity.getExecutestate() );
        wPTaskDto.setState( entity.getState() );
        wPTaskDto.setNote( entity.getNote() );
        wPTaskDto.setCreateuser( entity.getCreateuser() );
        wPTaskDto.setCreatetime( entity.getCreatetime() );
        wPTaskDto.setUpdateuser( entity.getUpdateuser() );
        wPTaskDto.setUpdatetime( entity.getUpdatetime() );
        wPTaskDto.setSparefield1( entity.getSparefield1() );
        wPTaskDto.setSparefield2( entity.getSparefield2() );
        wPTaskDto.setSparefield3( entity.getSparefield3() );
        wPTaskDto.setSparefield4( entity.getSparefield4() );
        wPTaskDto.setSparefield5( entity.getSparefield5() );

        return wPTaskDto;
    }

    @Override
    public List<WPTask> toEntity(List<WPTaskDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<WPTask> list = new ArrayList<WPTask>( dtoList.size() );
        for ( WPTaskDto wPTaskDto : dtoList ) {
            list.add( toEntity( wPTaskDto ) );
        }

        return list;
    }

    @Override
    public List<WPTaskDto> toDto(List<WPTask> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<WPTaskDto> list = new ArrayList<WPTaskDto>( entityList.size() );
        for ( WPTask wPTask : entityList ) {
            list.add( toDto( wPTask ) );
        }

        return list;
    }
}
