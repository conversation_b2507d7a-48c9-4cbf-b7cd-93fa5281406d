package com.wzsec.modules.fa.service.mapper;

import com.wzsec.modules.fa.domain.FaResult;
import com.wzsec.modules.fa.service.dto.FaResultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:01+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class FaResultMapperImpl implements FaResultMapper {

    @Override
    public FaResult toEntity(FaResultDto dto) {
        if ( dto == null ) {
            return null;
        }

        FaResult faResult = new FaResult();

        faResult.setId( dto.getId() );
        faResult.setTaskname( dto.getTaskname() );
        faResult.setTasktype( dto.getTasktype() );
        faResult.setSourceid( dto.getSourceid() );
        faResult.setFilepath( dto.getFilepath() );
        faResult.setTotalcount( dto.getTotalcount() );
        faResult.setCheckcount( dto.getCheckcount() );
        faResult.setRatio( dto.getRatio() );
        faResult.setChecktime( dto.getChecktime() );
        faResult.setSparefield1( dto.getSparefield1() );
        faResult.setSparefield2( dto.getSparefield2() );
        faResult.setSparefield3( dto.getSparefield3() );
        faResult.setSparefield4( dto.getSparefield4() );

        return faResult;
    }

    @Override
    public FaResultDto toDto(FaResult entity) {
        if ( entity == null ) {
            return null;
        }

        FaResultDto faResultDto = new FaResultDto();

        faResultDto.setId( entity.getId() );
        faResultDto.setTaskname( entity.getTaskname() );
        faResultDto.setTasktype( entity.getTasktype() );
        faResultDto.setSourceid( entity.getSourceid() );
        faResultDto.setFilepath( entity.getFilepath() );
        faResultDto.setTotalcount( entity.getTotalcount() );
        faResultDto.setCheckcount( entity.getCheckcount() );
        faResultDto.setRatio( entity.getRatio() );
        faResultDto.setChecktime( entity.getChecktime() );
        faResultDto.setSparefield1( entity.getSparefield1() );
        faResultDto.setSparefield2( entity.getSparefield2() );
        faResultDto.setSparefield3( entity.getSparefield3() );
        faResultDto.setSparefield4( entity.getSparefield4() );

        return faResultDto;
    }

    @Override
    public List<FaResult> toEntity(List<FaResultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<FaResult> list = new ArrayList<FaResult>( dtoList.size() );
        for ( FaResultDto faResultDto : dtoList ) {
            list.add( toEntity( faResultDto ) );
        }

        return list;
    }

    @Override
    public List<FaResultDto> toDto(List<FaResult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<FaResultDto> list = new ArrayList<FaResultDto>( entityList.size() );
        for ( FaResult faResult : entityList ) {
            list.add( toDto( faResult ) );
        }

        return list;
    }
}
