package com.wzsec.modules.fa.service.mapper;

import com.wzsec.modules.fa.domain.FaResult;
import com.wzsec.modules.fa.service.dto.FaResultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:53+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class FaResultMapperImpl implements FaResultMapper {

    @Override
    public FaResultDto toDto(FaResult entity) {
        if ( entity == null ) {
            return null;
        }

        FaResultDto faResultDto = new FaResultDto();

        faResultDto.setCheckcount( entity.getCheckcount() );
        faResultDto.setChecktime( entity.getChecktime() );
        faResultDto.setFilepath( entity.getFilepath() );
        faResultDto.setId( entity.getId() );
        faResultDto.setRatio( entity.getRatio() );
        faResultDto.setSourceid( entity.getSourceid() );
        faResultDto.setSparefield1( entity.getSparefield1() );
        faResultDto.setSparefield2( entity.getSparefield2() );
        faResultDto.setSparefield3( entity.getSparefield3() );
        faResultDto.setSparefield4( entity.getSparefield4() );
        faResultDto.setTaskname( entity.getTaskname() );
        faResultDto.setTasktype( entity.getTasktype() );
        faResultDto.setTotalcount( entity.getTotalcount() );

        return faResultDto;
    }

    @Override
    public List<FaResultDto> toDto(List<FaResult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<FaResultDto> list = new ArrayList<FaResultDto>( entityList.size() );
        for ( FaResult faResult : entityList ) {
            list.add( toDto( faResult ) );
        }

        return list;
    }

    @Override
    public FaResult toEntity(FaResultDto dto) {
        if ( dto == null ) {
            return null;
        }

        FaResult faResult = new FaResult();

        faResult.setCheckcount( dto.getCheckcount() );
        faResult.setChecktime( dto.getChecktime() );
        faResult.setFilepath( dto.getFilepath() );
        faResult.setId( dto.getId() );
        faResult.setRatio( dto.getRatio() );
        faResult.setSourceid( dto.getSourceid() );
        faResult.setSparefield1( dto.getSparefield1() );
        faResult.setSparefield2( dto.getSparefield2() );
        faResult.setSparefield3( dto.getSparefield3() );
        faResult.setSparefield4( dto.getSparefield4() );
        faResult.setTaskname( dto.getTaskname() );
        faResult.setTasktype( dto.getTasktype() );
        faResult.setTotalcount( dto.getTotalcount() );

        return faResult;
    }

    @Override
    public List<FaResult> toEntity(List<FaResultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<FaResult> list = new ArrayList<FaResult>( dtoList.size() );
        for ( FaResultDto faResultDto : dtoList ) {
            list.add( toEntity( faResultDto ) );
        }

        return list;
    }
}
