package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcStrategyConfig;
import com.wzsec.modules.ic.service.dto.IcStrategyConfigDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:01+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class IcStrategyConfigMapperImpl implements IcStrategyConfigMapper {

    @Override
    public IcStrategyConfig toEntity(IcStrategyConfigDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcStrategyConfig icStrategyConfig = new IcStrategyConfig();

        icStrategyConfig.setId( dto.getId() );
        icStrategyConfig.setStrategyname( dto.getStrategyname() );
        icStrategyConfig.setStrategydes( dto.getStrategydes() );
        icStrategyConfig.setStrategytype( dto.getStrategytype() );
        icStrategyConfig.setApicode( dto.getApicode() );
        icStrategyConfig.setApimethod( dto.getApimethod() );
        icStrategyConfig.setIncheckrule( dto.getIncheckrule() );
        icStrategyConfig.setOutcheckrule( dto.getOutcheckrule() );
        icStrategyConfig.setCreateuser( dto.getCreateuser() );
        icStrategyConfig.setCreatetime( dto.getCreatetime() );
        icStrategyConfig.setUpdateuser( dto.getUpdateuser() );
        icStrategyConfig.setUpdatetime( dto.getUpdatetime() );
        icStrategyConfig.setNote( dto.getNote() );
        icStrategyConfig.setSparefield1( dto.getSparefield1() );
        icStrategyConfig.setSparefield2( dto.getSparefield2() );
        icStrategyConfig.setSparefield3( dto.getSparefield3() );
        icStrategyConfig.setSparefield4( dto.getSparefield4() );

        return icStrategyConfig;
    }

    @Override
    public IcStrategyConfigDto toDto(IcStrategyConfig entity) {
        if ( entity == null ) {
            return null;
        }

        IcStrategyConfigDto icStrategyConfigDto = new IcStrategyConfigDto();

        icStrategyConfigDto.setId( entity.getId() );
        icStrategyConfigDto.setStrategyname( entity.getStrategyname() );
        icStrategyConfigDto.setStrategydes( entity.getStrategydes() );
        icStrategyConfigDto.setStrategytype( entity.getStrategytype() );
        icStrategyConfigDto.setApicode( entity.getApicode() );
        icStrategyConfigDto.setApimethod( entity.getApimethod() );
        icStrategyConfigDto.setIncheckrule( entity.getIncheckrule() );
        icStrategyConfigDto.setOutcheckrule( entity.getOutcheckrule() );
        icStrategyConfigDto.setCreateuser( entity.getCreateuser() );
        icStrategyConfigDto.setCreatetime( entity.getCreatetime() );
        icStrategyConfigDto.setUpdateuser( entity.getUpdateuser() );
        icStrategyConfigDto.setUpdatetime( entity.getUpdatetime() );
        icStrategyConfigDto.setNote( entity.getNote() );
        icStrategyConfigDto.setSparefield1( entity.getSparefield1() );
        icStrategyConfigDto.setSparefield2( entity.getSparefield2() );
        icStrategyConfigDto.setSparefield3( entity.getSparefield3() );
        icStrategyConfigDto.setSparefield4( entity.getSparefield4() );

        return icStrategyConfigDto;
    }

    @Override
    public List<IcStrategyConfig> toEntity(List<IcStrategyConfigDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcStrategyConfig> list = new ArrayList<IcStrategyConfig>( dtoList.size() );
        for ( IcStrategyConfigDto icStrategyConfigDto : dtoList ) {
            list.add( toEntity( icStrategyConfigDto ) );
        }

        return list;
    }

    @Override
    public List<IcStrategyConfigDto> toDto(List<IcStrategyConfig> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcStrategyConfigDto> list = new ArrayList<IcStrategyConfigDto>( entityList.size() );
        for ( IcStrategyConfig icStrategyConfig : entityList ) {
            list.add( toDto( icStrategyConfig ) );
        }

        return list;
    }
}
