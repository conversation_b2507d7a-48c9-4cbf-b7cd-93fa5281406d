package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcStrategyConfig;
import com.wzsec.modules.ic.service.dto.IcStrategyConfigDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:27:08+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class IcStrategyConfigMapperImpl implements IcStrategyConfigMapper {

    @Override
    public IcStrategyConfigDto toDto(IcStrategyConfig entity) {
        if ( entity == null ) {
            return null;
        }

        IcStrategyConfigDto icStrategyConfigDto = new IcStrategyConfigDto();

        icStrategyConfigDto.setApicode( entity.getApicode() );
        icStrategyConfigDto.setApimethod( entity.getApimethod() );
        icStrategyConfigDto.setCreatetime( entity.getCreatetime() );
        icStrategyConfigDto.setCreateuser( entity.getCreateuser() );
        icStrategyConfigDto.setId( entity.getId() );
        icStrategyConfigDto.setIncheckrule( entity.getIncheckrule() );
        icStrategyConfigDto.setNote( entity.getNote() );
        icStrategyConfigDto.setOutcheckrule( entity.getOutcheckrule() );
        icStrategyConfigDto.setSparefield1( entity.getSparefield1() );
        icStrategyConfigDto.setSparefield2( entity.getSparefield2() );
        icStrategyConfigDto.setSparefield3( entity.getSparefield3() );
        icStrategyConfigDto.setSparefield4( entity.getSparefield4() );
        icStrategyConfigDto.setStrategydes( entity.getStrategydes() );
        icStrategyConfigDto.setStrategyname( entity.getStrategyname() );
        icStrategyConfigDto.setStrategytype( entity.getStrategytype() );
        icStrategyConfigDto.setUpdatetime( entity.getUpdatetime() );
        icStrategyConfigDto.setUpdateuser( entity.getUpdateuser() );

        return icStrategyConfigDto;
    }

    @Override
    public List<IcStrategyConfigDto> toDto(List<IcStrategyConfig> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcStrategyConfigDto> list = new ArrayList<IcStrategyConfigDto>( entityList.size() );
        for ( IcStrategyConfig icStrategyConfig : entityList ) {
            list.add( toDto( icStrategyConfig ) );
        }

        return list;
    }

    @Override
    public IcStrategyConfig toEntity(IcStrategyConfigDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcStrategyConfig icStrategyConfig = new IcStrategyConfig();

        icStrategyConfig.setApicode( dto.getApicode() );
        icStrategyConfig.setApimethod( dto.getApimethod() );
        icStrategyConfig.setCreatetime( dto.getCreatetime() );
        icStrategyConfig.setCreateuser( dto.getCreateuser() );
        icStrategyConfig.setId( dto.getId() );
        icStrategyConfig.setIncheckrule( dto.getIncheckrule() );
        icStrategyConfig.setNote( dto.getNote() );
        icStrategyConfig.setOutcheckrule( dto.getOutcheckrule() );
        icStrategyConfig.setSparefield1( dto.getSparefield1() );
        icStrategyConfig.setSparefield2( dto.getSparefield2() );
        icStrategyConfig.setSparefield3( dto.getSparefield3() );
        icStrategyConfig.setSparefield4( dto.getSparefield4() );
        icStrategyConfig.setStrategydes( dto.getStrategydes() );
        icStrategyConfig.setStrategyname( dto.getStrategyname() );
        icStrategyConfig.setStrategytype( dto.getStrategytype() );
        icStrategyConfig.setUpdatetime( dto.getUpdatetime() );
        icStrategyConfig.setUpdateuser( dto.getUpdateuser() );

        return icStrategyConfig;
    }

    @Override
    public List<IcStrategyConfig> toEntity(List<IcStrategyConfigDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcStrategyConfig> list = new ArrayList<IcStrategyConfig>( dtoList.size() );
        for ( IcStrategyConfigDto icStrategyConfigDto : dtoList ) {
            list.add( toEntity( icStrategyConfigDto ) );
        }

        return list;
    }
}
