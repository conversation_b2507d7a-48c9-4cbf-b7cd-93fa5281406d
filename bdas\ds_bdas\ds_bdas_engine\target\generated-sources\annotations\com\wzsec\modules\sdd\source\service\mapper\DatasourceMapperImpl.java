package com.wzsec.modules.sdd.source.service.mapper;

import com.wzsec.modules.sdd.source.domain.Datasource;
import com.wzsec.modules.sdd.source.service.dto.DatasourceDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:27:07+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class DatasourceMapperImpl implements DatasourceMapper {

    @Override
    public DatasourceDto toDto(Datasource entity) {
        if ( entity == null ) {
            return null;
        }

        DatasourceDto datasourceDto = new DatasourceDto();

        datasourceDto.setClientcharset( entity.getClientcharset() );
        datasourceDto.setCreatetime( entity.getCreatetime() );
        datasourceDto.setCreateuser( entity.getCreateuser() );
        datasourceDto.setDbcharset( entity.getDbcharset() );
        datasourceDto.setDbname( entity.getDbname() );
        datasourceDto.setDriverprogram( entity.getDriverprogram() );
        datasourceDto.setId( entity.getId() );
        datasourceDto.setIsvalid( entity.getIsvalid() );
        datasourceDto.setNote( entity.getNote() );
        datasourceDto.setPassword( entity.getPassword() );
        datasourceDto.setSparefield1( entity.getSparefield1() );
        datasourceDto.setSparefield2( entity.getSparefield2() );
        datasourceDto.setSparefield3( entity.getSparefield3() );
        datasourceDto.setSparefield4( entity.getSparefield4() );
        datasourceDto.setSparefield5( entity.getSparefield5() );
        datasourceDto.setSrcname( entity.getSrcname() );
        datasourceDto.setSrcurl( entity.getSrcurl() );
        datasourceDto.setType( entity.getType() );
        datasourceDto.setUpdatetime( entity.getUpdatetime() );
        datasourceDto.setUpdateuser( entity.getUpdateuser() );
        datasourceDto.setUsername( entity.getUsername() );

        return datasourceDto;
    }

    @Override
    public List<DatasourceDto> toDto(List<Datasource> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<DatasourceDto> list = new ArrayList<DatasourceDto>( entityList.size() );
        for ( Datasource datasource : entityList ) {
            list.add( toDto( datasource ) );
        }

        return list;
    }

    @Override
    public Datasource toEntity(DatasourceDto dto) {
        if ( dto == null ) {
            return null;
        }

        Datasource datasource = new Datasource();

        datasource.setClientcharset( dto.getClientcharset() );
        datasource.setCreatetime( dto.getCreatetime() );
        datasource.setCreateuser( dto.getCreateuser() );
        datasource.setDbcharset( dto.getDbcharset() );
        datasource.setDbname( dto.getDbname() );
        datasource.setDriverprogram( dto.getDriverprogram() );
        datasource.setId( dto.getId() );
        datasource.setIsvalid( dto.getIsvalid() );
        datasource.setNote( dto.getNote() );
        datasource.setPassword( dto.getPassword() );
        datasource.setSparefield1( dto.getSparefield1() );
        datasource.setSparefield2( dto.getSparefield2() );
        datasource.setSparefield3( dto.getSparefield3() );
        datasource.setSparefield4( dto.getSparefield4() );
        datasource.setSparefield5( dto.getSparefield5() );
        datasource.setSrcname( dto.getSrcname() );
        datasource.setSrcurl( dto.getSrcurl() );
        datasource.setType( dto.getType() );
        datasource.setUpdatetime( dto.getUpdatetime() );
        datasource.setUpdateuser( dto.getUpdateuser() );
        datasource.setUsername( dto.getUsername() );

        return datasource;
    }

    @Override
    public List<Datasource> toEntity(List<DatasourceDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Datasource> list = new ArrayList<Datasource>( dtoList.size() );
        for ( DatasourceDto datasourceDto : dtoList ) {
            list.add( toEntity( datasourceDto ) );
        }

        return list;
    }
}
