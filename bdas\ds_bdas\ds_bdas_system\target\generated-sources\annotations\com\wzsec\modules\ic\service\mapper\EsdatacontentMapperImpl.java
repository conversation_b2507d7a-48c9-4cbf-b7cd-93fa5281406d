package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.Esdatacontent;
import com.wzsec.modules.ic.service.dto.EsdatacontentDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:30+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class EsdatacontentMapperImpl implements EsdatacontentMapper {

    @Override
    public EsdatacontentDto toDto(Esdatacontent entity) {
        if ( entity == null ) {
            return null;
        }

        EsdatacontentDto esdatacontentDto = new EsdatacontentDto();

        esdatacontentDto.setAccount( entity.getAccount() );
        esdatacontentDto.setApicode( entity.getApicode() );
        esdatacontentDto.setApiip( entity.getApiip() );
        esdatacontentDto.setApiport( entity.getApiport() );
        esdatacontentDto.setApiuri( entity.getApiuri() );
        esdatacontentDto.setCalltime( entity.getCalltime() );
        esdatacontentDto.setCleantime( entity.getCleantime() );
        esdatacontentDto.setClientip( entity.getClientip() );
        esdatacontentDto.setClientmac( entity.getClientmac() );
        esdatacontentDto.setDatatype( entity.getDatatype() );
        esdatacontentDto.setId( entity.getId() );
        esdatacontentDto.setModule( entity.getModule() );
        esdatacontentDto.setRepstatus( entity.getRepstatus() );
        esdatacontentDto.setReqcontent( entity.getReqcontent() );
        esdatacontentDto.setReqmethod( entity.getReqmethod() );
        esdatacontentDto.setRescontent( entity.getRescontent() );
        esdatacontentDto.setResstatus( entity.getResstatus() );
        esdatacontentDto.setSystem( entity.getSystem() );

        return esdatacontentDto;
    }

    @Override
    public List<EsdatacontentDto> toDto(List<Esdatacontent> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<EsdatacontentDto> list = new ArrayList<EsdatacontentDto>( entityList.size() );
        for ( Esdatacontent esdatacontent : entityList ) {
            list.add( toDto( esdatacontent ) );
        }

        return list;
    }

    @Override
    public Esdatacontent toEntity(EsdatacontentDto dto) {
        if ( dto == null ) {
            return null;
        }

        Esdatacontent esdatacontent = new Esdatacontent();

        esdatacontent.setAccount( dto.getAccount() );
        esdatacontent.setApicode( dto.getApicode() );
        esdatacontent.setApiip( dto.getApiip() );
        esdatacontent.setApiport( dto.getApiport() );
        esdatacontent.setApiuri( dto.getApiuri() );
        esdatacontent.setCalltime( dto.getCalltime() );
        esdatacontent.setCleantime( dto.getCleantime() );
        esdatacontent.setClientip( dto.getClientip() );
        esdatacontent.setClientmac( dto.getClientmac() );
        esdatacontent.setDatatype( dto.getDatatype() );
        esdatacontent.setId( dto.getId() );
        esdatacontent.setModule( dto.getModule() );
        esdatacontent.setRepstatus( dto.getRepstatus() );
        esdatacontent.setReqcontent( dto.getReqcontent() );
        esdatacontent.setReqmethod( dto.getReqmethod() );
        esdatacontent.setRescontent( dto.getRescontent() );
        esdatacontent.setResstatus( dto.getResstatus() );
        esdatacontent.setSystem( dto.getSystem() );

        return esdatacontent;
    }

    @Override
    public List<Esdatacontent> toEntity(List<EsdatacontentDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Esdatacontent> list = new ArrayList<Esdatacontent>( dtoList.size() );
        for ( EsdatacontentDto esdatacontentDto : dtoList ) {
            list.add( toEntity( esdatacontentDto ) );
        }

        return list;
    }
}
