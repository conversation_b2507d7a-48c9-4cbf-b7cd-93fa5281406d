package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.Esdatacontent;
import com.wzsec.modules.ic.service.dto.EsdatacontentDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:31+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class EsdatacontentMapperImpl implements EsdatacontentMapper {

    @Override
    public Esdatacontent toEntity(EsdatacontentDto dto) {
        if ( dto == null ) {
            return null;
        }

        Esdatacontent esdatacontent = new Esdatacontent();

        esdatacontent.setId( dto.getId() );
        esdatacontent.setApicode( dto.getApicode() );
        esdatacontent.setSystem( dto.getSystem() );
        esdatacontent.setModule( dto.getModule() );
        esdatacontent.setApiuri( dto.getApiuri() );
        esdatacontent.setApiip( dto.getApiip() );
        esdatacontent.setApiport( dto.getApiport() );
        esdatacontent.setClientip( dto.getClientip() );
        esdatacontent.setClientmac( dto.getClientmac() );
        esdatacontent.setReqmethod( dto.getReqmethod() );
        esdatacontent.setReqcontent( dto.getReqcontent() );
        esdatacontent.setRepstatus( dto.getRepstatus() );
        esdatacontent.setResstatus( dto.getResstatus() );
        esdatacontent.setRescontent( dto.getRescontent() );
        esdatacontent.setAccount( dto.getAccount() );
        esdatacontent.setDatatype( dto.getDatatype() );
        esdatacontent.setCalltime( dto.getCalltime() );
        esdatacontent.setCleantime( dto.getCleantime() );

        return esdatacontent;
    }

    @Override
    public EsdatacontentDto toDto(Esdatacontent entity) {
        if ( entity == null ) {
            return null;
        }

        EsdatacontentDto esdatacontentDto = new EsdatacontentDto();

        esdatacontentDto.setId( entity.getId() );
        esdatacontentDto.setApicode( entity.getApicode() );
        esdatacontentDto.setSystem( entity.getSystem() );
        esdatacontentDto.setModule( entity.getModule() );
        esdatacontentDto.setApiuri( entity.getApiuri() );
        esdatacontentDto.setApiip( entity.getApiip() );
        esdatacontentDto.setApiport( entity.getApiport() );
        esdatacontentDto.setClientip( entity.getClientip() );
        esdatacontentDto.setClientmac( entity.getClientmac() );
        esdatacontentDto.setReqmethod( entity.getReqmethod() );
        esdatacontentDto.setReqcontent( entity.getReqcontent() );
        esdatacontentDto.setRepstatus( entity.getRepstatus() );
        esdatacontentDto.setResstatus( entity.getResstatus() );
        esdatacontentDto.setRescontent( entity.getRescontent() );
        esdatacontentDto.setAccount( entity.getAccount() );
        esdatacontentDto.setDatatype( entity.getDatatype() );
        esdatacontentDto.setCalltime( entity.getCalltime() );
        esdatacontentDto.setCleantime( entity.getCleantime() );

        return esdatacontentDto;
    }

    @Override
    public List<Esdatacontent> toEntity(List<EsdatacontentDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Esdatacontent> list = new ArrayList<Esdatacontent>( dtoList.size() );
        for ( EsdatacontentDto esdatacontentDto : dtoList ) {
            list.add( toEntity( esdatacontentDto ) );
        }

        return list;
    }

    @Override
    public List<EsdatacontentDto> toDto(List<Esdatacontent> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<EsdatacontentDto> list = new ArrayList<EsdatacontentDto>( entityList.size() );
        for ( Esdatacontent esdatacontent : entityList ) {
            list.add( toDto( esdatacontent ) );
        }

        return list;
    }
}
