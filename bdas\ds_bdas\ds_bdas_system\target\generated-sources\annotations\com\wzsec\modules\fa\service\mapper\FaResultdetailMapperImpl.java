package com.wzsec.modules.fa.service.mapper;

import com.wzsec.modules.fa.domain.FaResultdetail;
import com.wzsec.modules.fa.service.dto.FaResultdetailDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:31+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class FaResultdetailMapperImpl implements FaResultdetailMapper {

    @Override
    public FaResultdetail toEntity(FaResultdetailDto dto) {
        if ( dto == null ) {
            return null;
        }

        FaResultdetail faResultdetail = new FaResultdetail();

        faResultdetail.setId( dto.getId() );
        faResultdetail.setTaskname( dto.getTaskname() );
        faResultdetail.setTasktype( dto.getTasktype() );
        faResultdetail.setFilename( dto.getFilename() );
        faResultdetail.setFilepath( dto.getFilepath() );
        faResultdetail.setFilesize( dto.getFilesize() );
        faResultdetail.setFiletype( dto.getFiletype() );
        faResultdetail.setFilesuffix( dto.getFilesuffix() );
        faResultdetail.setIshide( dto.getIshide() );
        faResultdetail.setIsread( dto.getIsread() );
        faResultdetail.setIswrite( dto.getIswrite() );
        faResultdetail.setLastupdate( dto.getLastupdate() );
        faResultdetail.setTotalcount( dto.getTotalcount() );
        faResultdetail.setCheckcount( dto.getCheckcount() );
        faResultdetail.setRatio( dto.getRatio() );
        faResultdetail.setSensitivedata( dto.getSensitivedata() );
        faResultdetail.setExample( dto.getExample() );
        faResultdetail.setChecktime( dto.getChecktime() );
        faResultdetail.setSparefield1( dto.getSparefield1() );
        faResultdetail.setSparefield2( dto.getSparefield2() );
        faResultdetail.setSparefield3( dto.getSparefield3() );
        faResultdetail.setSparefield4( dto.getSparefield4() );

        return faResultdetail;
    }

    @Override
    public FaResultdetailDto toDto(FaResultdetail entity) {
        if ( entity == null ) {
            return null;
        }

        FaResultdetailDto faResultdetailDto = new FaResultdetailDto();

        faResultdetailDto.setId( entity.getId() );
        faResultdetailDto.setTaskname( entity.getTaskname() );
        faResultdetailDto.setTasktype( entity.getTasktype() );
        faResultdetailDto.setFilename( entity.getFilename() );
        faResultdetailDto.setFilepath( entity.getFilepath() );
        faResultdetailDto.setFilesize( entity.getFilesize() );
        faResultdetailDto.setFiletype( entity.getFiletype() );
        faResultdetailDto.setFilesuffix( entity.getFilesuffix() );
        faResultdetailDto.setIshide( entity.getIshide() );
        faResultdetailDto.setIsread( entity.getIsread() );
        faResultdetailDto.setIswrite( entity.getIswrite() );
        faResultdetailDto.setLastupdate( entity.getLastupdate() );
        faResultdetailDto.setTotalcount( entity.getTotalcount() );
        faResultdetailDto.setCheckcount( entity.getCheckcount() );
        faResultdetailDto.setRatio( entity.getRatio() );
        faResultdetailDto.setSensitivedata( entity.getSensitivedata() );
        faResultdetailDto.setExample( entity.getExample() );
        faResultdetailDto.setChecktime( entity.getChecktime() );
        faResultdetailDto.setSparefield1( entity.getSparefield1() );
        faResultdetailDto.setSparefield2( entity.getSparefield2() );
        faResultdetailDto.setSparefield3( entity.getSparefield3() );
        faResultdetailDto.setSparefield4( entity.getSparefield4() );

        return faResultdetailDto;
    }

    @Override
    public List<FaResultdetail> toEntity(List<FaResultdetailDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<FaResultdetail> list = new ArrayList<FaResultdetail>( dtoList.size() );
        for ( FaResultdetailDto faResultdetailDto : dtoList ) {
            list.add( toEntity( faResultdetailDto ) );
        }

        return list;
    }

    @Override
    public List<FaResultdetailDto> toDto(List<FaResultdetail> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<FaResultdetailDto> list = new ArrayList<FaResultdetailDto>( entityList.size() );
        for ( FaResultdetail faResultdetail : entityList ) {
            list.add( toDto( faResultdetail ) );
        }

        return list;
    }
}
