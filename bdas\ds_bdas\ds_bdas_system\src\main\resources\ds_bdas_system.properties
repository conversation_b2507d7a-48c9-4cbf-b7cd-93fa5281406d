# 联通一体化数据安全管控平台获取用户信息接口
integratedPlatform.userInfo=http://127.0.0.1:8802/services/user/dgpc/oidc/oauth2/userInfo

# 数据分域：0 分域 1不分域
data.domain.mode=1
# 不分域部门集deptIds
data.domain.dept=0,1

#######################################敏感数据发现业务相关配置#######################################

###########################敏感数据发现任务-Start###########################
#FTP文件下载本地保存路径
ftp.download.save.localpath=D:/CtyunWork//download/ftp
#HDFS文件下载本地保存路径
hdfs.download.save.localpath=D:/CtyunWork/download/hdfs
#linux文件下载本地保存路径
linux.download.save.localpath=D:/CtyunWork/download/linux
###########################敏感数据发现任务-End###########################


###########################端口扫描-Start###########################
#端口扫描结果本地保存路径
scanner.host.save.localpath=D:/CtyunWork/scanner/host
#端口扫描连接超时(毫秒)
scanner.host.connect.timeout=500
#端口扫描线程数
scanner.host.thread.num=100
###########################端口扫描-End###########################


###########################引擎地址-Start###########################
#数据源测试连接
engine.source.testconn=http://localhost:8802/engine/datasource/test/

#执行敏感数据发现任务
engine.dotask=http://localhost:8802/engine/task/exec/
#定时执行敏感数据发现任务
engine.dotimingtask=http://localhost:8802/engine/task/timingexec/

#执行弱密码发现任务
engine.wp.dotask=http://localhost:8802/engine/wp/task/exec/
#定时执行弱密码发现任务
engine.wp.dotimingtask=http://localhost:8802/engine/wp/task/timingexec/

#执行接口日志审计任务
engine.ic.dotask=http://localhost:8802/engine/ic/task/exec/
#定时执行脱敏审计任务
engine.ic.dotimingtask=http://localhost:8802/engine/ic/task/timingexec/

#执行文件审计任务
#(文件审计任务不配置IP端口是因为考虑到文件审计会部署在多个服务器，需要通过字段表file_audit_engine_config和任务配置具体执行的引擎地址)
engine.fa.dotask=/engine/fa/task/exec/
#定时文件审计任务
engine.fa.dotimingtask=/engine/fa/task/timingexec/

#执行数据操作审计任务
engine.oc.dotask=http://localhost:8802/engine/oc/task/exec/

#定时执行脱敏审计任务
engine.oc.dotimingtask=http://localhost:8802/engine/oc/task/timingexec/

###########################引擎地址-End###########################


###########################弱密码库-Start###########################
#hbase集群地址
#hbase.zookeeper.quorum=***********:2181,***********:2181,***********:2181
hbase.zookeeper.quorum=**********:2181,**********:2181,**********:2181,**********:2181,**********:2181
#弱密码批次导入数量
batch.save.number=10000
#弱密码库列族
hbase.table.cf=weakpwd
#弱密码库列
hbase.table.column=md5,twosha1
#hbase弱密码table
hbase.table=sdd_wp_weakpwd
#弱密码库位置（mysql，hbase）
weak.pw.db=mysql
###########################弱密码库-End###########################


#################新增用户默认配置#################
user.default.password=Ct$Ad@20
user.default.roles=2,4,6
user.default.job=11
user.default.dept=2

#################告警推送syslog配置#################
syslog.host=**************
syslog.port=514
# syslog协议分两种(tcp,udp)
syslog.protocol=tcp


# ###### 待审数据存储ES配置 ##########
elasticsearch.hostlist=**************:9200
elasticsearch.username=elastic
elasticsearch.password=wzsec@2022