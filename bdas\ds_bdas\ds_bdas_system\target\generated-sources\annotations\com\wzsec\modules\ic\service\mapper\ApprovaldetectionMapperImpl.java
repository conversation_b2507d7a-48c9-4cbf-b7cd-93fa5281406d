package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.Approvaldetection;
import com.wzsec.modules.ic.service.dto.ApprovaldetectionDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:30+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ApprovaldetectionMapperImpl implements ApprovaldetectionMapper {

    @Override
    public Approvaldetection toEntity(ApprovaldetectionDto dto) {
        if ( dto == null ) {
            return null;
        }

        Approvaldetection approvaldetection = new Approvaldetection();

        approvaldetection.setId( dto.getId() );
        approvaldetection.setAk( dto.getAk() );
        approvaldetection.setSk( dto.getSk() );
        approvaldetection.setAudittime( dto.getAudittime() );
        approvaldetection.setMartsupplyorgname( dto.getMartsupplyorgname() );
        approvaldetection.setName( dto.getName() );
        approvaldetection.setApplyorgname( dto.getApplyorgname() );
        approvaldetection.setApplytime( dto.getApplytime() );
        approvaldetection.setSysname( dto.getSysname() );
        approvaldetection.setShareapplyid( dto.getShareapplyid() );
        approvaldetection.setApicode( dto.getApicode() );
        approvaldetection.setApiname( dto.getApiname() );
        approvaldetection.setResourcename( dto.getResourcename() );
        approvaldetection.setActualoutput( dto.getActualoutput() );
        approvaldetection.setApplyoutput( dto.getApplyoutput() );
        approvaldetection.setRisk( dto.getRisk() );
        approvaldetection.setCreatetime( dto.getCreatetime() );
        approvaldetection.setSparefield1( dto.getSparefield1() );
        approvaldetection.setSparefield2( dto.getSparefield2() );
        approvaldetection.setSparefield3( dto.getSparefield3() );
        approvaldetection.setSparefield4( dto.getSparefield4() );
        approvaldetection.setSparefield5( dto.getSparefield5() );

        return approvaldetection;
    }

    @Override
    public ApprovaldetectionDto toDto(Approvaldetection entity) {
        if ( entity == null ) {
            return null;
        }

        ApprovaldetectionDto approvaldetectionDto = new ApprovaldetectionDto();

        approvaldetectionDto.setId( entity.getId() );
        approvaldetectionDto.setAk( entity.getAk() );
        approvaldetectionDto.setSk( entity.getSk() );
        approvaldetectionDto.setAudittime( entity.getAudittime() );
        approvaldetectionDto.setMartsupplyorgname( entity.getMartsupplyorgname() );
        approvaldetectionDto.setName( entity.getName() );
        approvaldetectionDto.setApplyorgname( entity.getApplyorgname() );
        approvaldetectionDto.setApplytime( entity.getApplytime() );
        approvaldetectionDto.setSysname( entity.getSysname() );
        approvaldetectionDto.setShareapplyid( entity.getShareapplyid() );
        approvaldetectionDto.setApicode( entity.getApicode() );
        approvaldetectionDto.setApiname( entity.getApiname() );
        approvaldetectionDto.setResourcename( entity.getResourcename() );
        approvaldetectionDto.setActualoutput( entity.getActualoutput() );
        approvaldetectionDto.setApplyoutput( entity.getApplyoutput() );
        approvaldetectionDto.setRisk( entity.getRisk() );
        approvaldetectionDto.setCreatetime( entity.getCreatetime() );
        approvaldetectionDto.setSparefield1( entity.getSparefield1() );
        approvaldetectionDto.setSparefield2( entity.getSparefield2() );
        approvaldetectionDto.setSparefield3( entity.getSparefield3() );
        approvaldetectionDto.setSparefield4( entity.getSparefield4() );
        approvaldetectionDto.setSparefield5( entity.getSparefield5() );

        return approvaldetectionDto;
    }

    @Override
    public List<Approvaldetection> toEntity(List<ApprovaldetectionDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Approvaldetection> list = new ArrayList<Approvaldetection>( dtoList.size() );
        for ( ApprovaldetectionDto approvaldetectionDto : dtoList ) {
            list.add( toEntity( approvaldetectionDto ) );
        }

        return list;
    }

    @Override
    public List<ApprovaldetectionDto> toDto(List<Approvaldetection> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApprovaldetectionDto> list = new ArrayList<ApprovaldetectionDto>( entityList.size() );
        for ( Approvaldetection approvaldetection : entityList ) {
            list.add( toDto( approvaldetection ) );
        }

        return list;
    }
}
