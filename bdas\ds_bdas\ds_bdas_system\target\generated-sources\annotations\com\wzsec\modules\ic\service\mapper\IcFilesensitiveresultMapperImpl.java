package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcFilesensitiveresult;
import com.wzsec.modules.ic.service.dto.IcFilesensitiveresultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:26:48+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class IcFilesensitiveresultMapperImpl implements IcFilesensitiveresultMapper {

    @Override
    public IcFilesensitiveresultDto toDto(IcFilesensitiveresult entity) {
        if ( entity == null ) {
            return null;
        }

        IcFilesensitiveresultDto icFilesensitiveresultDto = new IcFilesensitiveresultDto();

        icFilesensitiveresultDto.setApicode( entity.getApicode() );
        icFilesensitiveresultDto.setApiname( entity.getApiname() );
        icFilesensitiveresultDto.setBehaviortype( entity.getBehaviortype() );
        icFilesensitiveresultDto.setChecktime( entity.getChecktime() );
        icFilesensitiveresultDto.setClientip( entity.getClientip() );
        icFilesensitiveresultDto.setFilename( entity.getFilename() );
        icFilesensitiveresultDto.setFilesize( entity.getFilesize() );
        icFilesensitiveresultDto.setId( entity.getId() );
        icFilesensitiveresultDto.setRisk( entity.getRisk() );
        icFilesensitiveresultDto.setServerip( entity.getServerip() );
        icFilesensitiveresultDto.setServerport( entity.getServerport() );
        icFilesensitiveresultDto.setSparefield1( entity.getSparefield1() );
        icFilesensitiveresultDto.setSparefield2( entity.getSparefield2() );
        icFilesensitiveresultDto.setSparefield3( entity.getSparefield3() );
        icFilesensitiveresultDto.setSparefield4( entity.getSparefield4() );
        icFilesensitiveresultDto.setSparefield5( entity.getSparefield5() );
        icFilesensitiveresultDto.setTaskname( entity.getTaskname() );
        icFilesensitiveresultDto.setUrl( entity.getUrl() );

        return icFilesensitiveresultDto;
    }

    @Override
    public List<IcFilesensitiveresultDto> toDto(List<IcFilesensitiveresult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcFilesensitiveresultDto> list = new ArrayList<IcFilesensitiveresultDto>( entityList.size() );
        for ( IcFilesensitiveresult icFilesensitiveresult : entityList ) {
            list.add( toDto( icFilesensitiveresult ) );
        }

        return list;
    }

    @Override
    public IcFilesensitiveresult toEntity(IcFilesensitiveresultDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcFilesensitiveresult icFilesensitiveresult = new IcFilesensitiveresult();

        icFilesensitiveresult.setApicode( dto.getApicode() );
        icFilesensitiveresult.setApiname( dto.getApiname() );
        icFilesensitiveresult.setBehaviortype( dto.getBehaviortype() );
        icFilesensitiveresult.setChecktime( dto.getChecktime() );
        icFilesensitiveresult.setClientip( dto.getClientip() );
        icFilesensitiveresult.setFilename( dto.getFilename() );
        icFilesensitiveresult.setFilesize( dto.getFilesize() );
        icFilesensitiveresult.setId( dto.getId() );
        icFilesensitiveresult.setRisk( dto.getRisk() );
        icFilesensitiveresult.setServerip( dto.getServerip() );
        icFilesensitiveresult.setServerport( dto.getServerport() );
        icFilesensitiveresult.setSparefield1( dto.getSparefield1() );
        icFilesensitiveresult.setSparefield2( dto.getSparefield2() );
        icFilesensitiveresult.setSparefield3( dto.getSparefield3() );
        icFilesensitiveresult.setSparefield4( dto.getSparefield4() );
        icFilesensitiveresult.setSparefield5( dto.getSparefield5() );
        icFilesensitiveresult.setTaskname( dto.getTaskname() );
        icFilesensitiveresult.setUrl( dto.getUrl() );

        return icFilesensitiveresult;
    }

    @Override
    public List<IcFilesensitiveresult> toEntity(List<IcFilesensitiveresultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcFilesensitiveresult> list = new ArrayList<IcFilesensitiveresult>( dtoList.size() );
        for ( IcFilesensitiveresultDto icFilesensitiveresultDto : dtoList ) {
            list.add( toEntity( icFilesensitiveresultDto ) );
        }

        return list;
    }
}
