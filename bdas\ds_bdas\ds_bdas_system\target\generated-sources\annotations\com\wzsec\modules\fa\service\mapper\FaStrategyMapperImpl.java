package com.wzsec.modules.fa.service.mapper;

import com.wzsec.modules.fa.domain.FaStrategy;
import com.wzsec.modules.fa.service.dto.FaStrategyDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:31+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class FaStrategyMapperImpl implements FaStrategyMapper {

    @Override
    public FaStrategy toEntity(FaStrategyDto dto) {
        if ( dto == null ) {
            return null;
        }

        FaStrategy faStrategy = new FaStrategy();

        faStrategy.setId( dto.getId() );
        faStrategy.setCname( dto.getCname() );
        faStrategy.setEname( dto.getEname() );
        faStrategy.setRuleids( dto.getRuleids() );
        faStrategy.setState( dto.getState() );
        faStrategy.setDes( dto.getDes() );
        faStrategy.setNote( dto.getNote() );
        faStrategy.setCreateuser( dto.getCreateuser() );
        faStrategy.setCreatetime( dto.getCreatetime() );
        faStrategy.setUpdateuser( dto.getUpdateuser() );
        faStrategy.setUpdatetime( dto.getUpdatetime() );
        faStrategy.setSparefield1( dto.getSparefield1() );
        faStrategy.setSparefield2( dto.getSparefield2() );
        faStrategy.setSparefield3( dto.getSparefield3() );
        faStrategy.setSparefield4( dto.getSparefield4() );
        faStrategy.setSparefield5( dto.getSparefield5() );

        return faStrategy;
    }

    @Override
    public FaStrategyDto toDto(FaStrategy entity) {
        if ( entity == null ) {
            return null;
        }

        FaStrategyDto faStrategyDto = new FaStrategyDto();

        faStrategyDto.setId( entity.getId() );
        faStrategyDto.setCname( entity.getCname() );
        faStrategyDto.setEname( entity.getEname() );
        faStrategyDto.setRuleids( entity.getRuleids() );
        faStrategyDto.setState( entity.getState() );
        faStrategyDto.setDes( entity.getDes() );
        faStrategyDto.setNote( entity.getNote() );
        faStrategyDto.setCreateuser( entity.getCreateuser() );
        faStrategyDto.setCreatetime( entity.getCreatetime() );
        faStrategyDto.setUpdateuser( entity.getUpdateuser() );
        faStrategyDto.setUpdatetime( entity.getUpdatetime() );
        faStrategyDto.setSparefield1( entity.getSparefield1() );
        faStrategyDto.setSparefield2( entity.getSparefield2() );
        faStrategyDto.setSparefield3( entity.getSparefield3() );
        faStrategyDto.setSparefield4( entity.getSparefield4() );
        faStrategyDto.setSparefield5( entity.getSparefield5() );

        return faStrategyDto;
    }

    @Override
    public List<FaStrategy> toEntity(List<FaStrategyDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<FaStrategy> list = new ArrayList<FaStrategy>( dtoList.size() );
        for ( FaStrategyDto faStrategyDto : dtoList ) {
            list.add( toEntity( faStrategyDto ) );
        }

        return list;
    }

    @Override
    public List<FaStrategyDto> toDto(List<FaStrategy> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<FaStrategyDto> list = new ArrayList<FaStrategyDto>( entityList.size() );
        for ( FaStrategy faStrategy : entityList ) {
            list.add( toDto( faStrategy ) );
        }

        return list;
    }
}
