package com.wzsec.modules.fa.service.mapper;

import com.wzsec.modules.fa.domain.FaStrategy;
import com.wzsec.modules.fa.service.dto.FaStrategyDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:28+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class FaStrategyMapperImpl implements FaStrategyMapper {

    @Override
    public FaStrategyDto toDto(FaStrategy entity) {
        if ( entity == null ) {
            return null;
        }

        FaStrategyDto faStrategyDto = new FaStrategyDto();

        faStrategyDto.setCname( entity.getCname() );
        faStrategyDto.setCreatetime( entity.getCreatetime() );
        faStrategyDto.setCreateuser( entity.getCreateuser() );
        faStrategyDto.setDes( entity.getDes() );
        faStrategyDto.setEname( entity.getEname() );
        faStrategyDto.setId( entity.getId() );
        faStrategyDto.setNote( entity.getNote() );
        faStrategyDto.setRuleids( entity.getRuleids() );
        faStrategyDto.setSparefield1( entity.getSparefield1() );
        faStrategyDto.setSparefield2( entity.getSparefield2() );
        faStrategyDto.setSparefield3( entity.getSparefield3() );
        faStrategyDto.setSparefield4( entity.getSparefield4() );
        faStrategyDto.setSparefield5( entity.getSparefield5() );
        faStrategyDto.setState( entity.getState() );
        faStrategyDto.setUpdatetime( entity.getUpdatetime() );
        faStrategyDto.setUpdateuser( entity.getUpdateuser() );

        return faStrategyDto;
    }

    @Override
    public List<FaStrategyDto> toDto(List<FaStrategy> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<FaStrategyDto> list = new ArrayList<FaStrategyDto>( entityList.size() );
        for ( FaStrategy faStrategy : entityList ) {
            list.add( toDto( faStrategy ) );
        }

        return list;
    }

    @Override
    public FaStrategy toEntity(FaStrategyDto dto) {
        if ( dto == null ) {
            return null;
        }

        FaStrategy faStrategy = new FaStrategy();

        faStrategy.setCname( dto.getCname() );
        faStrategy.setCreatetime( dto.getCreatetime() );
        faStrategy.setCreateuser( dto.getCreateuser() );
        faStrategy.setDes( dto.getDes() );
        faStrategy.setEname( dto.getEname() );
        faStrategy.setId( dto.getId() );
        faStrategy.setNote( dto.getNote() );
        faStrategy.setRuleids( dto.getRuleids() );
        faStrategy.setSparefield1( dto.getSparefield1() );
        faStrategy.setSparefield2( dto.getSparefield2() );
        faStrategy.setSparefield3( dto.getSparefield3() );
        faStrategy.setSparefield4( dto.getSparefield4() );
        faStrategy.setSparefield5( dto.getSparefield5() );
        faStrategy.setState( dto.getState() );
        faStrategy.setUpdatetime( dto.getUpdatetime() );
        faStrategy.setUpdateuser( dto.getUpdateuser() );

        return faStrategy;
    }

    @Override
    public List<FaStrategy> toEntity(List<FaStrategyDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<FaStrategy> list = new ArrayList<FaStrategy>( dtoList.size() );
        for ( FaStrategyDto faStrategyDto : dtoList ) {
            list.add( toEntity( faStrategyDto ) );
        }

        return list;
    }
}
