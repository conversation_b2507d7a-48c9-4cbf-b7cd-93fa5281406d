package com.wzsec.modules.fa.service.mapper;

import com.wzsec.modules.fa.domain.FaResultdetail;
import com.wzsec.modules.fa.service.dto.FaResultdetailDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:27:08+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class FaResultdetailMapperImpl implements FaResultdetailMapper {

    @Override
    public FaResultdetailDto toDto(FaResultdetail entity) {
        if ( entity == null ) {
            return null;
        }

        FaResultdetailDto faResultdetailDto = new FaResultdetailDto();

        faResultdetailDto.setCheckcount( entity.getCheckcount() );
        faResultdetailDto.setChecktime( entity.getChecktime() );
        faResultdetailDto.setExample( entity.getExample() );
        faResultdetailDto.setFilepath( entity.getFilepath() );
        faResultdetailDto.setFilesize( entity.getFilesize() );
        faResultdetailDto.setFilesuffix( entity.getFilesuffix() );
        faResultdetailDto.setFiletype( entity.getFiletype() );
        faResultdetailDto.setId( entity.getId() );
        faResultdetailDto.setIshide( entity.getIshide() );
        faResultdetailDto.setIsread( entity.getIsread() );
        faResultdetailDto.setIswrite( entity.getIswrite() );
        faResultdetailDto.setLastupdate( entity.getLastupdate() );
        faResultdetailDto.setRatio( entity.getRatio() );
        faResultdetailDto.setSensitivedata( entity.getSensitivedata() );
        faResultdetailDto.setSparefield1( entity.getSparefield1() );
        faResultdetailDto.setSparefield2( entity.getSparefield2() );
        faResultdetailDto.setSparefield3( entity.getSparefield3() );
        faResultdetailDto.setSparefield4( entity.getSparefield4() );
        faResultdetailDto.setTaskname( entity.getTaskname() );
        faResultdetailDto.setTasktype( entity.getTasktype() );
        faResultdetailDto.setTotalcount( entity.getTotalcount() );

        return faResultdetailDto;
    }

    @Override
    public List<FaResultdetailDto> toDto(List<FaResultdetail> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<FaResultdetailDto> list = new ArrayList<FaResultdetailDto>( entityList.size() );
        for ( FaResultdetail faResultdetail : entityList ) {
            list.add( toDto( faResultdetail ) );
        }

        return list;
    }

    @Override
    public FaResultdetail toEntity(FaResultdetailDto dto) {
        if ( dto == null ) {
            return null;
        }

        FaResultdetail faResultdetail = new FaResultdetail();

        faResultdetail.setCheckcount( dto.getCheckcount() );
        faResultdetail.setChecktime( dto.getChecktime() );
        faResultdetail.setExample( dto.getExample() );
        faResultdetail.setFilepath( dto.getFilepath() );
        faResultdetail.setFilesize( dto.getFilesize() );
        faResultdetail.setFilesuffix( dto.getFilesuffix() );
        faResultdetail.setFiletype( dto.getFiletype() );
        faResultdetail.setId( dto.getId() );
        faResultdetail.setIshide( dto.getIshide() );
        faResultdetail.setIsread( dto.getIsread() );
        faResultdetail.setIswrite( dto.getIswrite() );
        faResultdetail.setLastupdate( dto.getLastupdate() );
        faResultdetail.setRatio( dto.getRatio() );
        faResultdetail.setSensitivedata( dto.getSensitivedata() );
        faResultdetail.setSparefield1( dto.getSparefield1() );
        faResultdetail.setSparefield2( dto.getSparefield2() );
        faResultdetail.setSparefield3( dto.getSparefield3() );
        faResultdetail.setSparefield4( dto.getSparefield4() );
        faResultdetail.setTaskname( dto.getTaskname() );
        faResultdetail.setTasktype( dto.getTasktype() );
        faResultdetail.setTotalcount( dto.getTotalcount() );

        return faResultdetail;
    }

    @Override
    public List<FaResultdetail> toEntity(List<FaResultdetailDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<FaResultdetail> list = new ArrayList<FaResultdetail>( dtoList.size() );
        for ( FaResultdetailDto faResultdetailDto : dtoList ) {
            list.add( toEntity( faResultdetailDto ) );
        }

        return list;
    }
}
