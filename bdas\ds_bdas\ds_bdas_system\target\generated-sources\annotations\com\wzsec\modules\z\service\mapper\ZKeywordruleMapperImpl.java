package com.wzsec.modules.z.service.mapper;

import com.wzsec.modules.z.domain.ZKeywordrule;
import com.wzsec.modules.z.service.dto.ZKeywordruleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:31+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ZKeywordruleMapperImpl implements ZKeywordruleMapper {

    @Override
    public ZKeywordruleDto toDto(ZKeywordrule entity) {
        if ( entity == null ) {
            return null;
        }

        ZKeywordruleDto zKeywordruleDto = new ZKeywordruleDto();

        zKeywordruleDto.setDes( entity.getDes() );
        zKeywordruleDto.setEnname( entity.getEnname() );
        zKeywordruleDto.setId( entity.getId() );
        zKeywordruleDto.setKeyword( entity.getKeyword() );
        zKeywordruleDto.setLevel( entity.getLevel() );
        zKeywordruleDto.setMatchnumber( entity.getMatchnumber() );
        zKeywordruleDto.setMatchtype( entity.getMatchtype() );
        zKeywordruleDto.setName( entity.getName() );
        zKeywordruleDto.setNote( entity.getNote() );
        zKeywordruleDto.setSeparatorstr( entity.getSeparatorstr() );
        zKeywordruleDto.setSparefield1( entity.getSparefield1() );
        zKeywordruleDto.setSparefield2( entity.getSparefield2() );
        zKeywordruleDto.setSparefield3( entity.getSparefield3() );
        zKeywordruleDto.setSparefield4( entity.getSparefield4() );
        zKeywordruleDto.setSparefield5( entity.getSparefield5() );
        zKeywordruleDto.setTestscenario( entity.getTestscenario() );

        return zKeywordruleDto;
    }

    @Override
    public List<ZKeywordruleDto> toDto(List<ZKeywordrule> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ZKeywordruleDto> list = new ArrayList<ZKeywordruleDto>( entityList.size() );
        for ( ZKeywordrule zKeywordrule : entityList ) {
            list.add( toDto( zKeywordrule ) );
        }

        return list;
    }

    @Override
    public ZKeywordrule toEntity(ZKeywordruleDto dto) {
        if ( dto == null ) {
            return null;
        }

        ZKeywordrule zKeywordrule = new ZKeywordrule();

        zKeywordrule.setDes( dto.getDes() );
        zKeywordrule.setEnname( dto.getEnname() );
        zKeywordrule.setId( dto.getId() );
        zKeywordrule.setKeyword( dto.getKeyword() );
        zKeywordrule.setLevel( dto.getLevel() );
        zKeywordrule.setMatchnumber( dto.getMatchnumber() );
        zKeywordrule.setMatchtype( dto.getMatchtype() );
        zKeywordrule.setName( dto.getName() );
        zKeywordrule.setNote( dto.getNote() );
        zKeywordrule.setSeparatorstr( dto.getSeparatorstr() );
        zKeywordrule.setSparefield1( dto.getSparefield1() );
        zKeywordrule.setSparefield2( dto.getSparefield2() );
        zKeywordrule.setSparefield3( dto.getSparefield3() );
        zKeywordrule.setSparefield4( dto.getSparefield4() );
        zKeywordrule.setSparefield5( dto.getSparefield5() );
        zKeywordrule.setTestscenario( dto.getTestscenario() );

        return zKeywordrule;
    }

    @Override
    public List<ZKeywordrule> toEntity(List<ZKeywordruleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ZKeywordrule> list = new ArrayList<ZKeywordrule>( dtoList.size() );
        for ( ZKeywordruleDto zKeywordruleDto : dtoList ) {
            list.add( toEntity( zKeywordruleDto ) );
        }

        return list;
    }
}
