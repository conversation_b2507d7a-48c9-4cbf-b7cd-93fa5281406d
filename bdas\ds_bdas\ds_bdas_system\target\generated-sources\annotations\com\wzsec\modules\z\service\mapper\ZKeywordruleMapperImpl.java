package com.wzsec.modules.z.service.mapper;

import com.wzsec.modules.z.domain.ZKeywordrule;
import com.wzsec.modules.z.service.dto.ZKeywordruleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:30+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ZKeywordruleMapperImpl implements ZKeywordruleMapper {

    @Override
    public ZKeywordrule toEntity(ZKeywordruleDto dto) {
        if ( dto == null ) {
            return null;
        }

        ZKeywordrule zKeywordrule = new ZKeywordrule();

        zKeywordrule.setId( dto.getId() );
        zKeywordrule.setName( dto.getName() );
        zKeywordrule.setEnname( dto.getEnname() );
        zKeywordrule.setDes( dto.getDes() );
        zKeywordrule.setLevel( dto.getLevel() );
        zKeywordrule.setMatchtype( dto.getMatchtype() );
        zKeywordrule.setSeparatorstr( dto.getSeparatorstr() );
        zKeywordrule.setKeyword( dto.getKeyword() );
        zKeywordrule.setMatchnumber( dto.getMatchnumber() );
        zKeywordrule.setTestscenario( dto.getTestscenario() );
        zKeywordrule.setNote( dto.getNote() );
        zKeywordrule.setSparefield1( dto.getSparefield1() );
        zKeywordrule.setSparefield2( dto.getSparefield2() );
        zKeywordrule.setSparefield3( dto.getSparefield3() );
        zKeywordrule.setSparefield4( dto.getSparefield4() );
        zKeywordrule.setSparefield5( dto.getSparefield5() );

        return zKeywordrule;
    }

    @Override
    public ZKeywordruleDto toDto(ZKeywordrule entity) {
        if ( entity == null ) {
            return null;
        }

        ZKeywordruleDto zKeywordruleDto = new ZKeywordruleDto();

        zKeywordruleDto.setId( entity.getId() );
        zKeywordruleDto.setName( entity.getName() );
        zKeywordruleDto.setEnname( entity.getEnname() );
        zKeywordruleDto.setDes( entity.getDes() );
        zKeywordruleDto.setLevel( entity.getLevel() );
        zKeywordruleDto.setMatchtype( entity.getMatchtype() );
        zKeywordruleDto.setSeparatorstr( entity.getSeparatorstr() );
        zKeywordruleDto.setKeyword( entity.getKeyword() );
        zKeywordruleDto.setMatchnumber( entity.getMatchnumber() );
        zKeywordruleDto.setTestscenario( entity.getTestscenario() );
        zKeywordruleDto.setNote( entity.getNote() );
        zKeywordruleDto.setSparefield1( entity.getSparefield1() );
        zKeywordruleDto.setSparefield2( entity.getSparefield2() );
        zKeywordruleDto.setSparefield3( entity.getSparefield3() );
        zKeywordruleDto.setSparefield4( entity.getSparefield4() );
        zKeywordruleDto.setSparefield5( entity.getSparefield5() );

        return zKeywordruleDto;
    }

    @Override
    public List<ZKeywordrule> toEntity(List<ZKeywordruleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ZKeywordrule> list = new ArrayList<ZKeywordrule>( dtoList.size() );
        for ( ZKeywordruleDto zKeywordruleDto : dtoList ) {
            list.add( toEntity( zKeywordruleDto ) );
        }

        return list;
    }

    @Override
    public List<ZKeywordruleDto> toDto(List<ZKeywordrule> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ZKeywordruleDto> list = new ArrayList<ZKeywordruleDto>( entityList.size() );
        for ( ZKeywordrule zKeywordrule : entityList ) {
            list.add( toDto( zKeywordrule ) );
        }

        return list;
    }
}
