package com.wzsec.modules.system.service.mapper;

import com.wzsec.modules.system.domain.Dept;
import com.wzsec.modules.system.domain.Job;
import com.wzsec.modules.system.domain.Role;
import com.wzsec.modules.system.domain.User;
import com.wzsec.modules.system.domain.UserAvatar;
import com.wzsec.modules.system.service.dto.DeptSmallDto;
import com.wzsec.modules.system.service.dto.JobSmallDto;
import com.wzsec.modules.system.service.dto.RoleSmallDto;
import com.wzsec.modules.system.service.dto.UserDto;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javax.annotation.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:31+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class UserMapperImpl implements UserMapper {

    @Autowired
    private RoleMapper roleMapper;
    @Autowired
    private DeptMapper deptMapper;
    @Autowired
    private JobMapper jobMapper;

    @Override
    public List<UserDto> toDto(List<User> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<UserDto> list = new ArrayList<UserDto>( entityList.size() );
        for ( User user : entityList ) {
            list.add( toDto( user ) );
        }

        return list;
    }

    @Override
    public User toEntity(UserDto dto) {
        if ( dto == null ) {
            return null;
        }

        User user = new User();

        user.setCreateTime( dto.getCreateTime() );
        user.setDept( deptSmallDtoToDept( dto.getDept() ) );
        user.setEmail( dto.getEmail() );
        user.setEnabled( dto.getEnabled() );
        user.setId( dto.getId() );
        user.setJob( jobSmallDtoToJob( dto.getJob() ) );
        user.setLastLoginTime( dto.getLastLoginTime() );
        user.setLastPasswordResetTime( dto.getLastPasswordResetTime() );
        user.setNickName( dto.getNickName() );
        user.setPassword( dto.getPassword() );
        user.setPhone( dto.getPhone() );
        user.setRoles( roleSmallDtoSetToRoleSet( dto.getRoles() ) );
        user.setSex( dto.getSex() );
        user.setSparefield1( dto.getSparefield1() );
        user.setSparefield2( dto.getSparefield2() );
        user.setUsername( dto.getUsername() );

        return user;
    }

    @Override
    public List<User> toEntity(List<UserDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<User> list = new ArrayList<User>( dtoList.size() );
        for ( UserDto userDto : dtoList ) {
            list.add( toEntity( userDto ) );
        }

        return list;
    }

    @Override
    public UserDto toDto(User user) {
        if ( user == null ) {
            return null;
        }

        UserDto userDto = new UserDto();

        String realName = userUserAvatarRealName( user );
        if ( realName != null ) {
            userDto.setAvatar( realName );
        }
        userDto.setCreateTime( user.getCreateTime() );
        userDto.setDept( deptToDeptSmallDto( user.getDept() ) );
        userDto.setEmail( user.getEmail() );
        userDto.setEnabled( user.getEnabled() );
        userDto.setId( user.getId() );
        userDto.setJob( jobToJobSmallDto( user.getJob() ) );
        userDto.setLastLoginTime( user.getLastLoginTime() );
        userDto.setLastPasswordResetTime( user.getLastPasswordResetTime() );
        userDto.setNickName( user.getNickName() );
        userDto.setPassword( user.getPassword() );
        userDto.setPhone( user.getPhone() );
        userDto.setRoles( roleSetToRoleSmallDtoSet( user.getRoles() ) );
        userDto.setSex( user.getSex() );
        userDto.setSparefield1( user.getSparefield1() );
        userDto.setSparefield2( user.getSparefield2() );
        userDto.setUsername( user.getUsername() );

        return userDto;
    }

    protected Dept deptSmallDtoToDept(DeptSmallDto deptSmallDto) {
        if ( deptSmallDto == null ) {
            return null;
        }

        Dept dept = new Dept();

        dept.setId( deptSmallDto.getId() );
        dept.setName( deptSmallDto.getName() );

        return dept;
    }

    protected Job jobSmallDtoToJob(JobSmallDto jobSmallDto) {
        if ( jobSmallDto == null ) {
            return null;
        }

        Job job = new Job();

        job.setId( jobSmallDto.getId() );
        job.setName( jobSmallDto.getName() );

        return job;
    }

    protected Role roleSmallDtoToRole(RoleSmallDto roleSmallDto) {
        if ( roleSmallDto == null ) {
            return null;
        }

        Role role = new Role();

        role.setDataScope( roleSmallDto.getDataScope() );
        role.setId( roleSmallDto.getId() );
        role.setLevel( roleSmallDto.getLevel() );
        role.setName( roleSmallDto.getName() );

        return role;
    }

    protected Set<Role> roleSmallDtoSetToRoleSet(Set<RoleSmallDto> set) {
        if ( set == null ) {
            return null;
        }

        Set<Role> set1 = new HashSet<Role>( Math.max( (int) ( set.size() / .75f ) + 1, 16 ) );
        for ( RoleSmallDto roleSmallDto : set ) {
            set1.add( roleSmallDtoToRole( roleSmallDto ) );
        }

        return set1;
    }

    private String userUserAvatarRealName(User user) {
        if ( user == null ) {
            return null;
        }
        UserAvatar userAvatar = user.getUserAvatar();
        if ( userAvatar == null ) {
            return null;
        }
        String realName = userAvatar.getRealName();
        if ( realName == null ) {
            return null;
        }
        return realName;
    }

    protected DeptSmallDto deptToDeptSmallDto(Dept dept) {
        if ( dept == null ) {
            return null;
        }

        DeptSmallDto deptSmallDto = new DeptSmallDto();

        deptSmallDto.setId( dept.getId() );
        deptSmallDto.setName( dept.getName() );

        return deptSmallDto;
    }

    protected JobSmallDto jobToJobSmallDto(Job job) {
        if ( job == null ) {
            return null;
        }

        JobSmallDto jobSmallDto = new JobSmallDto();

        jobSmallDto.setId( job.getId() );
        jobSmallDto.setName( job.getName() );

        return jobSmallDto;
    }

    protected RoleSmallDto roleToRoleSmallDto(Role role) {
        if ( role == null ) {
            return null;
        }

        RoleSmallDto roleSmallDto = new RoleSmallDto();

        roleSmallDto.setDataScope( role.getDataScope() );
        roleSmallDto.setId( role.getId() );
        roleSmallDto.setLevel( role.getLevel() );
        roleSmallDto.setName( role.getName() );

        return roleSmallDto;
    }

    protected Set<RoleSmallDto> roleSetToRoleSmallDtoSet(Set<Role> set) {
        if ( set == null ) {
            return null;
        }

        Set<RoleSmallDto> set1 = new HashSet<RoleSmallDto>( Math.max( (int) ( set.size() / .75f ) + 1, 16 ) );
        for ( Role role : set ) {
            set1.add( roleToRoleSmallDto( role ) );
        }

        return set1;
    }
}
