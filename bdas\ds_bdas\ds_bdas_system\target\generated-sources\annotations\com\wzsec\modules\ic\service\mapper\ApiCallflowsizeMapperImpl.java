package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.ApiCallflowsize;
import com.wzsec.modules.ic.service.dto.ApiCallflowsizeDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:31+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ApiCallflowsizeMapperImpl implements ApiCallflowsizeMapper {

    @Override
    public ApiCallflowsize toEntity(ApiCallflowsizeDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiCallflowsize apiCallflowsize = new ApiCallflowsize();

        apiCallflowsize.setId( dto.getId() );
        apiCallflowsize.setApicode( dto.getApicode() );
        apiCallflowsize.setCallcount( dto.getCallcount() );
        apiCallflowsize.setFlowsize( dto.getFlowsize() );
        apiCallflowsize.setRisk( dto.getRisk() );
        apiCallflowsize.setCalldate( dto.getCalldate() );
        apiCallflowsize.setCheckdate( dto.getCheckdate() );
        apiCallflowsize.setSparefield1( dto.getSparefield1() );
        apiCallflowsize.setSparefield2( dto.getSparefield2() );
        apiCallflowsize.setSparefield3( dto.getSparefield3() );
        apiCallflowsize.setSparefield4( dto.getSparefield4() );

        return apiCallflowsize;
    }

    @Override
    public ApiCallflowsizeDto toDto(ApiCallflowsize entity) {
        if ( entity == null ) {
            return null;
        }

        ApiCallflowsizeDto apiCallflowsizeDto = new ApiCallflowsizeDto();

        apiCallflowsizeDto.setId( entity.getId() );
        apiCallflowsizeDto.setApicode( entity.getApicode() );
        apiCallflowsizeDto.setCallcount( entity.getCallcount() );
        apiCallflowsizeDto.setFlowsize( entity.getFlowsize() );
        apiCallflowsizeDto.setRisk( entity.getRisk() );
        apiCallflowsizeDto.setCalldate( entity.getCalldate() );
        apiCallflowsizeDto.setCheckdate( entity.getCheckdate() );
        apiCallflowsizeDto.setSparefield1( entity.getSparefield1() );
        apiCallflowsizeDto.setSparefield2( entity.getSparefield2() );
        apiCallflowsizeDto.setSparefield3( entity.getSparefield3() );
        apiCallflowsizeDto.setSparefield4( entity.getSparefield4() );

        return apiCallflowsizeDto;
    }

    @Override
    public List<ApiCallflowsize> toEntity(List<ApiCallflowsizeDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiCallflowsize> list = new ArrayList<ApiCallflowsize>( dtoList.size() );
        for ( ApiCallflowsizeDto apiCallflowsizeDto : dtoList ) {
            list.add( toEntity( apiCallflowsizeDto ) );
        }

        return list;
    }

    @Override
    public List<ApiCallflowsizeDto> toDto(List<ApiCallflowsize> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiCallflowsizeDto> list = new ArrayList<ApiCallflowsizeDto>( entityList.size() );
        for ( ApiCallflowsize apiCallflowsize : entityList ) {
            list.add( toDto( apiCallflowsize ) );
        }

        return list;
    }
}
