package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.ApiCallflowsize;
import com.wzsec.modules.ic.service.dto.ApiCallflowsizeDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:32+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ApiCallflowsizeMapperImpl implements ApiCallflowsizeMapper {

    @Override
    public ApiCallflowsizeDto toDto(ApiCallflowsize entity) {
        if ( entity == null ) {
            return null;
        }

        ApiCallflowsizeDto apiCallflowsizeDto = new ApiCallflowsizeDto();

        apiCallflowsizeDto.setApicode( entity.getApicode() );
        apiCallflowsizeDto.setCallcount( entity.getCallcount() );
        apiCallflowsizeDto.setCalldate( entity.getCalldate() );
        apiCallflowsizeDto.setCheckdate( entity.getCheckdate() );
        apiCallflowsizeDto.setFlowsize( entity.getFlowsize() );
        apiCallflowsizeDto.setId( entity.getId() );
        apiCallflowsizeDto.setRisk( entity.getRisk() );
        apiCallflowsizeDto.setSparefield1( entity.getSparefield1() );
        apiCallflowsizeDto.setSparefield2( entity.getSparefield2() );
        apiCallflowsizeDto.setSparefield3( entity.getSparefield3() );
        apiCallflowsizeDto.setSparefield4( entity.getSparefield4() );

        return apiCallflowsizeDto;
    }

    @Override
    public List<ApiCallflowsizeDto> toDto(List<ApiCallflowsize> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiCallflowsizeDto> list = new ArrayList<ApiCallflowsizeDto>( entityList.size() );
        for ( ApiCallflowsize apiCallflowsize : entityList ) {
            list.add( toDto( apiCallflowsize ) );
        }

        return list;
    }

    @Override
    public ApiCallflowsize toEntity(ApiCallflowsizeDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiCallflowsize apiCallflowsize = new ApiCallflowsize();

        apiCallflowsize.setApicode( dto.getApicode() );
        apiCallflowsize.setCallcount( dto.getCallcount() );
        apiCallflowsize.setCalldate( dto.getCalldate() );
        apiCallflowsize.setCheckdate( dto.getCheckdate() );
        apiCallflowsize.setFlowsize( dto.getFlowsize() );
        apiCallflowsize.setId( dto.getId() );
        apiCallflowsize.setRisk( dto.getRisk() );
        apiCallflowsize.setSparefield1( dto.getSparefield1() );
        apiCallflowsize.setSparefield2( dto.getSparefield2() );
        apiCallflowsize.setSparefield3( dto.getSparefield3() );
        apiCallflowsize.setSparefield4( dto.getSparefield4() );

        return apiCallflowsize;
    }

    @Override
    public List<ApiCallflowsize> toEntity(List<ApiCallflowsizeDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiCallflowsize> list = new ArrayList<ApiCallflowsize>( dtoList.size() );
        for ( ApiCallflowsizeDto apiCallflowsizeDto : dtoList ) {
            list.add( toEntity( apiCallflowsizeDto ) );
        }

        return list;
    }
}
