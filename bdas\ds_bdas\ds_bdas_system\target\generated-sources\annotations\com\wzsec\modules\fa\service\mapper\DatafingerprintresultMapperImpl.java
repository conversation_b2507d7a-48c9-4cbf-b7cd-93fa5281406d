package com.wzsec.modules.fa.service.mapper;

import com.wzsec.modules.fa.domain.Datafingerprintresult;
import com.wzsec.modules.fa.service.dto.DatafingerprintresultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:30+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class DatafingerprintresultMapperImpl implements DatafingerprintresultMapper {

    @Override
    public Datafingerprintresult toEntity(DatafingerprintresultDto dto) {
        if ( dto == null ) {
            return null;
        }

        Datafingerprintresult datafingerprintresult = new Datafingerprintresult();

        datafingerprintresult.setId( dto.getId() );
        datafingerprintresult.setTaskname( dto.getTaskname() );
        datafingerprintresult.setTasktype( dto.getTasktype() );
        datafingerprintresult.setSourceid( dto.getSourceid() );
        datafingerprintresult.setFilepath( dto.getFilepath() );
        datafingerprintresult.setDsize( dto.getDsize() );
        datafingerprintresult.setFilesuffix( dto.getFilesuffix() );
        datafingerprintresult.setFiletype( dto.getFiletype() );
        datafingerprintresult.setFilefingerprint( dto.getFilefingerprint() );
        datafingerprintresult.setFilesimilarityfingerprint( dto.getFilesimilarityfingerprint() );
        datafingerprintresult.setMatching( dto.getMatching() );
        datafingerprintresult.setDlevel( dto.getDlevel() );
        datafingerprintresult.setCreatetime( dto.getCreatetime() );
        datafingerprintresult.setSparefield1( dto.getSparefield1() );
        datafingerprintresult.setSparefield2( dto.getSparefield2() );
        datafingerprintresult.setSparefield3( dto.getSparefield3() );
        datafingerprintresult.setSparefield4( dto.getSparefield4() );

        return datafingerprintresult;
    }

    @Override
    public DatafingerprintresultDto toDto(Datafingerprintresult entity) {
        if ( entity == null ) {
            return null;
        }

        DatafingerprintresultDto datafingerprintresultDto = new DatafingerprintresultDto();

        datafingerprintresultDto.setId( entity.getId() );
        datafingerprintresultDto.setTaskname( entity.getTaskname() );
        datafingerprintresultDto.setTasktype( entity.getTasktype() );
        datafingerprintresultDto.setSourceid( entity.getSourceid() );
        datafingerprintresultDto.setFilepath( entity.getFilepath() );
        datafingerprintresultDto.setDsize( entity.getDsize() );
        datafingerprintresultDto.setFilesuffix( entity.getFilesuffix() );
        datafingerprintresultDto.setFiletype( entity.getFiletype() );
        datafingerprintresultDto.setFilefingerprint( entity.getFilefingerprint() );
        datafingerprintresultDto.setFilesimilarityfingerprint( entity.getFilesimilarityfingerprint() );
        datafingerprintresultDto.setMatching( entity.getMatching() );
        datafingerprintresultDto.setDlevel( entity.getDlevel() );
        datafingerprintresultDto.setCreatetime( entity.getCreatetime() );
        datafingerprintresultDto.setSparefield1( entity.getSparefield1() );
        datafingerprintresultDto.setSparefield2( entity.getSparefield2() );
        datafingerprintresultDto.setSparefield3( entity.getSparefield3() );
        datafingerprintresultDto.setSparefield4( entity.getSparefield4() );

        return datafingerprintresultDto;
    }

    @Override
    public List<Datafingerprintresult> toEntity(List<DatafingerprintresultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Datafingerprintresult> list = new ArrayList<Datafingerprintresult>( dtoList.size() );
        for ( DatafingerprintresultDto datafingerprintresultDto : dtoList ) {
            list.add( toEntity( datafingerprintresultDto ) );
        }

        return list;
    }

    @Override
    public List<DatafingerprintresultDto> toDto(List<Datafingerprintresult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<DatafingerprintresultDto> list = new ArrayList<DatafingerprintresultDto>( entityList.size() );
        for ( Datafingerprintresult datafingerprintresult : entityList ) {
            list.add( toDto( datafingerprintresult ) );
        }

        return list;
    }
}
