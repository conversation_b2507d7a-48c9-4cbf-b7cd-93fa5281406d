package com.wzsec.modules.fa.service.mapper;

import com.wzsec.modules.fa.domain.Datafingerprintresult;
import com.wzsec.modules.fa.service.dto.DatafingerprintresultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:30+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class DatafingerprintresultMapperImpl implements DatafingerprintresultMapper {

    @Override
    public DatafingerprintresultDto toDto(Datafingerprintresult entity) {
        if ( entity == null ) {
            return null;
        }

        DatafingerprintresultDto datafingerprintresultDto = new DatafingerprintresultDto();

        datafingerprintresultDto.setCreatetime( entity.getCreatetime() );
        datafingerprintresultDto.setDlevel( entity.getDlevel() );
        datafingerprintresultDto.setDsize( entity.getDsize() );
        datafingerprintresultDto.setFilefingerprint( entity.getFilefingerprint() );
        datafingerprintresultDto.setFilepath( entity.getFilepath() );
        datafingerprintresultDto.setFilesimilarityfingerprint( entity.getFilesimilarityfingerprint() );
        datafingerprintresultDto.setFilesuffix( entity.getFilesuffix() );
        datafingerprintresultDto.setFiletype( entity.getFiletype() );
        datafingerprintresultDto.setId( entity.getId() );
        datafingerprintresultDto.setMatching( entity.getMatching() );
        datafingerprintresultDto.setSourceid( entity.getSourceid() );
        datafingerprintresultDto.setSparefield1( entity.getSparefield1() );
        datafingerprintresultDto.setSparefield2( entity.getSparefield2() );
        datafingerprintresultDto.setSparefield3( entity.getSparefield3() );
        datafingerprintresultDto.setSparefield4( entity.getSparefield4() );
        datafingerprintresultDto.setTaskname( entity.getTaskname() );
        datafingerprintresultDto.setTasktype( entity.getTasktype() );

        return datafingerprintresultDto;
    }

    @Override
    public List<DatafingerprintresultDto> toDto(List<Datafingerprintresult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<DatafingerprintresultDto> list = new ArrayList<DatafingerprintresultDto>( entityList.size() );
        for ( Datafingerprintresult datafingerprintresult : entityList ) {
            list.add( toDto( datafingerprintresult ) );
        }

        return list;
    }

    @Override
    public Datafingerprintresult toEntity(DatafingerprintresultDto dto) {
        if ( dto == null ) {
            return null;
        }

        Datafingerprintresult datafingerprintresult = new Datafingerprintresult();

        datafingerprintresult.setCreatetime( dto.getCreatetime() );
        datafingerprintresult.setDlevel( dto.getDlevel() );
        datafingerprintresult.setDsize( dto.getDsize() );
        datafingerprintresult.setFilefingerprint( dto.getFilefingerprint() );
        datafingerprintresult.setFilepath( dto.getFilepath() );
        datafingerprintresult.setFilesimilarityfingerprint( dto.getFilesimilarityfingerprint() );
        datafingerprintresult.setFilesuffix( dto.getFilesuffix() );
        datafingerprintresult.setFiletype( dto.getFiletype() );
        datafingerprintresult.setId( dto.getId() );
        datafingerprintresult.setMatching( dto.getMatching() );
        datafingerprintresult.setSourceid( dto.getSourceid() );
        datafingerprintresult.setSparefield1( dto.getSparefield1() );
        datafingerprintresult.setSparefield2( dto.getSparefield2() );
        datafingerprintresult.setSparefield3( dto.getSparefield3() );
        datafingerprintresult.setSparefield4( dto.getSparefield4() );
        datafingerprintresult.setTaskname( dto.getTaskname() );
        datafingerprintresult.setTasktype( dto.getTasktype() );

        return datafingerprintresult;
    }

    @Override
    public List<Datafingerprintresult> toEntity(List<DatafingerprintresultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Datafingerprintresult> list = new ArrayList<Datafingerprintresult>( dtoList.size() );
        for ( DatafingerprintresultDto datafingerprintresultDto : dtoList ) {
            list.add( toEntity( datafingerprintresultDto ) );
        }

        return list;
    }
}
