package com.wzsec.modules.oc.service.mapper;

import com.wzsec.modules.oc.domain.OcAlarmrule;
import com.wzsec.modules.oc.service.dto.OcAlarmruleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:31+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class OcAlarmruleMapperImpl implements OcAlarmruleMapper {

    @Override
    public OcAlarmrule toEntity(OcAlarmruleDto dto) {
        if ( dto == null ) {
            return null;
        }

        OcAlarmrule ocAlarmrule = new OcAlarmrule();

        ocAlarmrule.setId( dto.getId() );
        ocAlarmrule.setAlarmname( dto.getAlarmname() );
        ocAlarmrule.setCategory( dto.getCategory() );
        ocAlarmrule.setOrigin( dto.getOrigin() );
        ocAlarmrule.setAlarmrule( dto.getAlarmrule() );
        ocAlarmrule.setStatus( dto.getStatus() );
        ocAlarmrule.setLabel( dto.getLabel() );
        ocAlarmrule.setCreateuser( dto.getCreateuser() );
        ocAlarmrule.setCreatetime( dto.getCreatetime() );
        ocAlarmrule.setUpdateuser( dto.getUpdateuser() );
        ocAlarmrule.setUpdatetime( dto.getUpdatetime() );
        ocAlarmrule.setRemarks( dto.getRemarks() );
        ocAlarmrule.setSparefield1( dto.getSparefield1() );
        ocAlarmrule.setSparefield2( dto.getSparefield2() );
        ocAlarmrule.setSparefield3( dto.getSparefield3() );
        ocAlarmrule.setSparefield4( dto.getSparefield4() );

        return ocAlarmrule;
    }

    @Override
    public OcAlarmruleDto toDto(OcAlarmrule entity) {
        if ( entity == null ) {
            return null;
        }

        OcAlarmruleDto ocAlarmruleDto = new OcAlarmruleDto();

        ocAlarmruleDto.setId( entity.getId() );
        ocAlarmruleDto.setAlarmname( entity.getAlarmname() );
        ocAlarmruleDto.setCategory( entity.getCategory() );
        ocAlarmruleDto.setOrigin( entity.getOrigin() );
        ocAlarmruleDto.setAlarmrule( entity.getAlarmrule() );
        ocAlarmruleDto.setStatus( entity.getStatus() );
        ocAlarmruleDto.setLabel( entity.getLabel() );
        ocAlarmruleDto.setCreateuser( entity.getCreateuser() );
        ocAlarmruleDto.setCreatetime( entity.getCreatetime() );
        ocAlarmruleDto.setUpdateuser( entity.getUpdateuser() );
        ocAlarmruleDto.setUpdatetime( entity.getUpdatetime() );
        ocAlarmruleDto.setRemarks( entity.getRemarks() );
        ocAlarmruleDto.setSparefield1( entity.getSparefield1() );
        ocAlarmruleDto.setSparefield2( entity.getSparefield2() );
        ocAlarmruleDto.setSparefield3( entity.getSparefield3() );
        ocAlarmruleDto.setSparefield4( entity.getSparefield4() );

        return ocAlarmruleDto;
    }

    @Override
    public List<OcAlarmrule> toEntity(List<OcAlarmruleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<OcAlarmrule> list = new ArrayList<OcAlarmrule>( dtoList.size() );
        for ( OcAlarmruleDto ocAlarmruleDto : dtoList ) {
            list.add( toEntity( ocAlarmruleDto ) );
        }

        return list;
    }

    @Override
    public List<OcAlarmruleDto> toDto(List<OcAlarmrule> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<OcAlarmruleDto> list = new ArrayList<OcAlarmruleDto>( entityList.size() );
        for ( OcAlarmrule ocAlarmrule : entityList ) {
            list.add( toDto( ocAlarmrule ) );
        }

        return list;
    }
}
