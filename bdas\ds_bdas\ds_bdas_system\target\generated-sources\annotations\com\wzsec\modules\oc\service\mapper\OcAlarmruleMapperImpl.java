package com.wzsec.modules.oc.service.mapper;

import com.wzsec.modules.oc.domain.OcAlarmrule;
import com.wzsec.modules.oc.service.dto.OcAlarmruleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:30+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class OcAlarmruleMapperImpl implements OcAlarmruleMapper {

    @Override
    public OcAlarmruleDto toDto(OcAlarmrule entity) {
        if ( entity == null ) {
            return null;
        }

        OcAlarmruleDto ocAlarmruleDto = new OcAlarmruleDto();

        ocAlarmruleDto.setAlarmname( entity.getAlarmname() );
        ocAlarmruleDto.setAlarmrule( entity.getAlarmrule() );
        ocAlarmruleDto.setCategory( entity.getCategory() );
        ocAlarmruleDto.setCreatetime( entity.getCreatetime() );
        ocAlarmruleDto.setCreateuser( entity.getCreateuser() );
        ocAlarmruleDto.setId( entity.getId() );
        ocAlarmruleDto.setLabel( entity.getLabel() );
        ocAlarmruleDto.setOrigin( entity.getOrigin() );
        ocAlarmruleDto.setRemarks( entity.getRemarks() );
        ocAlarmruleDto.setSparefield1( entity.getSparefield1() );
        ocAlarmruleDto.setSparefield2( entity.getSparefield2() );
        ocAlarmruleDto.setSparefield3( entity.getSparefield3() );
        ocAlarmruleDto.setSparefield4( entity.getSparefield4() );
        ocAlarmruleDto.setStatus( entity.getStatus() );
        ocAlarmruleDto.setUpdatetime( entity.getUpdatetime() );
        ocAlarmruleDto.setUpdateuser( entity.getUpdateuser() );

        return ocAlarmruleDto;
    }

    @Override
    public List<OcAlarmruleDto> toDto(List<OcAlarmrule> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<OcAlarmruleDto> list = new ArrayList<OcAlarmruleDto>( entityList.size() );
        for ( OcAlarmrule ocAlarmrule : entityList ) {
            list.add( toDto( ocAlarmrule ) );
        }

        return list;
    }

    @Override
    public OcAlarmrule toEntity(OcAlarmruleDto dto) {
        if ( dto == null ) {
            return null;
        }

        OcAlarmrule ocAlarmrule = new OcAlarmrule();

        ocAlarmrule.setAlarmname( dto.getAlarmname() );
        ocAlarmrule.setAlarmrule( dto.getAlarmrule() );
        ocAlarmrule.setCategory( dto.getCategory() );
        ocAlarmrule.setCreatetime( dto.getCreatetime() );
        ocAlarmrule.setCreateuser( dto.getCreateuser() );
        ocAlarmrule.setId( dto.getId() );
        ocAlarmrule.setLabel( dto.getLabel() );
        ocAlarmrule.setOrigin( dto.getOrigin() );
        ocAlarmrule.setRemarks( dto.getRemarks() );
        ocAlarmrule.setSparefield1( dto.getSparefield1() );
        ocAlarmrule.setSparefield2( dto.getSparefield2() );
        ocAlarmrule.setSparefield3( dto.getSparefield3() );
        ocAlarmrule.setSparefield4( dto.getSparefield4() );
        ocAlarmrule.setStatus( dto.getStatus() );
        ocAlarmrule.setUpdatetime( dto.getUpdatetime() );
        ocAlarmrule.setUpdateuser( dto.getUpdateuser() );

        return ocAlarmrule;
    }

    @Override
    public List<OcAlarmrule> toEntity(List<OcAlarmruleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<OcAlarmrule> list = new ArrayList<OcAlarmrule>( dtoList.size() );
        for ( OcAlarmruleDto ocAlarmruleDto : dtoList ) {
            list.add( toEntity( ocAlarmruleDto ) );
        }

        return list;
    }
}
