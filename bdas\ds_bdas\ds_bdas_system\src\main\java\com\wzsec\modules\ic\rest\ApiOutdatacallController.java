package com.wzsec.modules.ic.rest;

import cn.hutool.core.lang.Console;
import com.wzsec.aop.log.Log;
import com.wzsec.modules.ic.domain.ApiOutdatacall;
import com.wzsec.modules.ic.service.ApiOutdatacallService;
import com.wzsec.modules.ic.service.dto.ApiOutdatacallQueryCriteria;
import com.wzsec.utils.DomainUtil;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-05-19
 */
// @Api(tags = "突发获取大量数据检测结果管理")
@RestController
@RequestMapping("/api/apiOutdatacall")
public class ApiOutdatacallController {

    private final ApiOutdatacallService apiOutdatacallService;

    public ApiOutdatacallController(ApiOutdatacallService apiOutdatacallService) {
        this.apiOutdatacallService = apiOutdatacallService;
    }

    @Log("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, ApiOutdatacallQueryCriteria criteria) throws IOException {
        try {
            Timestamp dateStart = criteria.getDateStart();
            Timestamp dateEnd = criteria.getDateEnd();
            List<String> checkTime = new ArrayList<>();
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateStart));
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateEnd));
            criteria.setChecktime(checkTime);
        } catch (Exception e) {
            Console.log("未填写查询时间");
        }

        // 根据申请部门名称分域
        List<String> domainAkList = DomainUtil.queryDomainAk();
        criteria.setAk(domainAkList);

        apiOutdatacallService.download(apiOutdatacallService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询突发获取大量数据检测结果")
    public ResponseEntity<Object> getApiOutdatacalls(ApiOutdatacallQueryCriteria criteria, Pageable pageable) {
        try {
            Timestamp dateStart = criteria.getDateStart();
            Timestamp dateEnd = criteria.getDateEnd();
            List<String> checkTime = new ArrayList<>();
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateStart));
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateEnd));
            criteria.setChecktime(checkTime);
        } catch (Exception e) {
            Console.log("未填写查询时间");
        }

        // 根据申请部门名称分域
        List<String> domainAkList = DomainUtil.queryDomainAk();
        criteria.setAk(domainAkList);

        return new ResponseEntity<>(apiOutdatacallService.queryAll(criteria, pageable), HttpStatus.OK);
    }


    @PostMapping
    @Log("新增突发获取大量数据检测结果")
    public ResponseEntity<Object> create(@Validated @RequestBody ApiOutdatacall resources) {
        return new ResponseEntity<>(apiOutdatacallService.create(resources), HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改突发获取大量数据检测结果")
    public ResponseEntity<Object> update(@Validated @RequestBody ApiOutdatacall resources) {
        apiOutdatacallService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除突发获取大量数据检测结果")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        apiOutdatacallService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
