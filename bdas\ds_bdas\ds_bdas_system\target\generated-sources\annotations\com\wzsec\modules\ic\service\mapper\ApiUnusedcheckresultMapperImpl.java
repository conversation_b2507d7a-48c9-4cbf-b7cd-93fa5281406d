package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.ApiUnusedcheckresult;
import com.wzsec.modules.ic.service.dto.ApiUnusedcheckresultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:26:48+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ApiUnusedcheckresultMapperImpl implements ApiUnusedcheckresultMapper {

    @Override
    public ApiUnusedcheckresultDto toDto(ApiUnusedcheckresult entity) {
        if ( entity == null ) {
            return null;
        }

        ApiUnusedcheckresultDto apiUnusedcheckresultDto = new ApiUnusedcheckresultDto();

        apiUnusedcheckresultDto.setApicode( entity.getApicode() );
        apiUnusedcheckresultDto.setApiname( entity.getApiname() );
        apiUnusedcheckresultDto.setApiurl( entity.getApiurl() );
        apiUnusedcheckresultDto.setChecktime( entity.getChecktime() );
        apiUnusedcheckresultDto.setId( entity.getId() );
        apiUnusedcheckresultDto.setRisk( entity.getRisk() );
        apiUnusedcheckresultDto.setSparefield1( entity.getSparefield1() );
        apiUnusedcheckresultDto.setSparefield2( entity.getSparefield2() );
        apiUnusedcheckresultDto.setSparefield3( entity.getSparefield3() );
        apiUnusedcheckresultDto.setSparefield4( entity.getSparefield4() );
        apiUnusedcheckresultDto.setSystemname( entity.getSystemname() );

        return apiUnusedcheckresultDto;
    }

    @Override
    public List<ApiUnusedcheckresultDto> toDto(List<ApiUnusedcheckresult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiUnusedcheckresultDto> list = new ArrayList<ApiUnusedcheckresultDto>( entityList.size() );
        for ( ApiUnusedcheckresult apiUnusedcheckresult : entityList ) {
            list.add( toDto( apiUnusedcheckresult ) );
        }

        return list;
    }

    @Override
    public ApiUnusedcheckresult toEntity(ApiUnusedcheckresultDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiUnusedcheckresult apiUnusedcheckresult = new ApiUnusedcheckresult();

        apiUnusedcheckresult.setApicode( dto.getApicode() );
        apiUnusedcheckresult.setApiname( dto.getApiname() );
        apiUnusedcheckresult.setApiurl( dto.getApiurl() );
        apiUnusedcheckresult.setChecktime( dto.getChecktime() );
        apiUnusedcheckresult.setId( dto.getId() );
        apiUnusedcheckresult.setRisk( dto.getRisk() );
        apiUnusedcheckresult.setSparefield1( dto.getSparefield1() );
        apiUnusedcheckresult.setSparefield2( dto.getSparefield2() );
        apiUnusedcheckresult.setSparefield3( dto.getSparefield3() );
        apiUnusedcheckresult.setSparefield4( dto.getSparefield4() );
        apiUnusedcheckresult.setSystemname( dto.getSystemname() );

        return apiUnusedcheckresult;
    }

    @Override
    public List<ApiUnusedcheckresult> toEntity(List<ApiUnusedcheckresultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiUnusedcheckresult> list = new ArrayList<ApiUnusedcheckresult>( dtoList.size() );
        for ( ApiUnusedcheckresultDto apiUnusedcheckresultDto : dtoList ) {
            list.add( toEntity( apiUnusedcheckresultDto ) );
        }

        return list;
    }
}
