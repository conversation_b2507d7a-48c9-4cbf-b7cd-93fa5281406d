package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcConfiguration;
import com.wzsec.modules.ic.service.dto.IcConfigurationDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:26:48+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class IcConfigurationMapperImpl implements IcConfigurationMapper {

    @Override
    public IcConfigurationDto toDto(IcConfiguration entity) {
        if ( entity == null ) {
            return null;
        }

        IcConfigurationDto icConfigurationDto = new IcConfigurationDto();

        icConfigurationDto.setApicode( entity.getApicode() );
        icConfigurationDto.setApiname( entity.getApiname() );
        icConfigurationDto.setCreatetime( entity.getCreatetime() );
        icConfigurationDto.setCreateuser( entity.getCreateuser() );
        icConfigurationDto.setId( entity.getId() );
        icConfigurationDto.setInvoketype( entity.getInvoketype() );
        icConfigurationDto.setSparefield1( entity.getSparefield1() );
        icConfigurationDto.setSparefield2( entity.getSparefield2() );
        icConfigurationDto.setSparefield3( entity.getSparefield3() );
        icConfigurationDto.setSparefield4( entity.getSparefield4() );
        icConfigurationDto.setState( entity.getState() );
        icConfigurationDto.setTasktype( entity.getTasktype() );
        icConfigurationDto.setUpdatetime( entity.getUpdatetime() );
        icConfigurationDto.setUpdateuser( entity.getUpdateuser() );

        return icConfigurationDto;
    }

    @Override
    public List<IcConfigurationDto> toDto(List<IcConfiguration> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcConfigurationDto> list = new ArrayList<IcConfigurationDto>( entityList.size() );
        for ( IcConfiguration icConfiguration : entityList ) {
            list.add( toDto( icConfiguration ) );
        }

        return list;
    }

    @Override
    public IcConfiguration toEntity(IcConfigurationDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcConfiguration icConfiguration = new IcConfiguration();

        icConfiguration.setApicode( dto.getApicode() );
        icConfiguration.setApiname( dto.getApiname() );
        icConfiguration.setCreatetime( dto.getCreatetime() );
        icConfiguration.setCreateuser( dto.getCreateuser() );
        icConfiguration.setId( dto.getId() );
        icConfiguration.setInvoketype( dto.getInvoketype() );
        icConfiguration.setSparefield1( dto.getSparefield1() );
        icConfiguration.setSparefield2( dto.getSparefield2() );
        icConfiguration.setSparefield3( dto.getSparefield3() );
        icConfiguration.setSparefield4( dto.getSparefield4() );
        icConfiguration.setState( dto.getState() );
        icConfiguration.setTasktype( dto.getTasktype() );
        icConfiguration.setUpdatetime( dto.getUpdatetime() );
        icConfiguration.setUpdateuser( dto.getUpdateuser() );

        return icConfiguration;
    }

    @Override
    public List<IcConfiguration> toEntity(List<IcConfigurationDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcConfiguration> list = new ArrayList<IcConfiguration>( dtoList.size() );
        for ( IcConfigurationDto icConfigurationDto : dtoList ) {
            list.add( toEntity( icConfigurationDto ) );
        }

        return list;
    }
}
