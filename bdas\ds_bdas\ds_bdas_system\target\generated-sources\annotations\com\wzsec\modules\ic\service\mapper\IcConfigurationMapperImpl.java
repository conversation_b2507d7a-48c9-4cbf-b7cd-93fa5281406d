package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcConfiguration;
import com.wzsec.modules.ic.service.dto.IcConfigurationDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:31+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class IcConfigurationMapperImpl implements IcConfigurationMapper {

    @Override
    public IcConfiguration toEntity(IcConfigurationDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcConfiguration icConfiguration = new IcConfiguration();

        icConfiguration.setId( dto.getId() );
        icConfiguration.setTasktype( dto.getTasktype() );
        icConfiguration.setApicode( dto.getApicode() );
        icConfiguration.setApiname( dto.getApiname() );
        icConfiguration.setInvoketype( dto.getInvoketype() );
        icConfiguration.setState( dto.getState() );
        icConfiguration.setCreateuser( dto.getCreateuser() );
        icConfiguration.setCreatetime( dto.getCreatetime() );
        icConfiguration.setUpdateuser( dto.getUpdateuser() );
        icConfiguration.setUpdatetime( dto.getUpdatetime() );
        icConfiguration.setSparefield1( dto.getSparefield1() );
        icConfiguration.setSparefield2( dto.getSparefield2() );
        icConfiguration.setSparefield3( dto.getSparefield3() );
        icConfiguration.setSparefield4( dto.getSparefield4() );

        return icConfiguration;
    }

    @Override
    public IcConfigurationDto toDto(IcConfiguration entity) {
        if ( entity == null ) {
            return null;
        }

        IcConfigurationDto icConfigurationDto = new IcConfigurationDto();

        icConfigurationDto.setId( entity.getId() );
        icConfigurationDto.setTasktype( entity.getTasktype() );
        icConfigurationDto.setApicode( entity.getApicode() );
        icConfigurationDto.setApiname( entity.getApiname() );
        icConfigurationDto.setInvoketype( entity.getInvoketype() );
        icConfigurationDto.setState( entity.getState() );
        icConfigurationDto.setCreateuser( entity.getCreateuser() );
        icConfigurationDto.setCreatetime( entity.getCreatetime() );
        icConfigurationDto.setUpdateuser( entity.getUpdateuser() );
        icConfigurationDto.setUpdatetime( entity.getUpdatetime() );
        icConfigurationDto.setSparefield1( entity.getSparefield1() );
        icConfigurationDto.setSparefield2( entity.getSparefield2() );
        icConfigurationDto.setSparefield3( entity.getSparefield3() );
        icConfigurationDto.setSparefield4( entity.getSparefield4() );

        return icConfigurationDto;
    }

    @Override
    public List<IcConfiguration> toEntity(List<IcConfigurationDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcConfiguration> list = new ArrayList<IcConfiguration>( dtoList.size() );
        for ( IcConfigurationDto icConfigurationDto : dtoList ) {
            list.add( toEntity( icConfigurationDto ) );
        }

        return list;
    }

    @Override
    public List<IcConfigurationDto> toDto(List<IcConfiguration> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcConfigurationDto> list = new ArrayList<IcConfigurationDto>( entityList.size() );
        for ( IcConfiguration icConfiguration : entityList ) {
            list.add( toDto( icConfiguration ) );
        }

        return list;
    }
}
