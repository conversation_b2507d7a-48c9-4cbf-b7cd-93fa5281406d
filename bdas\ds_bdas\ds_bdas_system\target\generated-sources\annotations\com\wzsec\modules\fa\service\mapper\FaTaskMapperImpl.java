package com.wzsec.modules.fa.service.mapper;

import com.wzsec.modules.fa.domain.FaTask;
import com.wzsec.modules.fa.service.dto.FaTaskDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:31+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class FaTaskMapperImpl implements FaTaskMapper {

    @Override
    public FaTask toEntity(FaTaskDto dto) {
        if ( dto == null ) {
            return null;
        }

        FaTask faTask = new FaTask();

        faTask.setId( dto.getId() );
        faTask.setTaskname( dto.getTaskname() );
        faTask.setTasktype( dto.getTasktype() );
        faTask.setSourceid( dto.getSourceid() );
        faTask.setFilepath( dto.getFilepath() );
        faTask.setUnzippassword( dto.getUnzippassword() );
        faTask.setSubmitmethod( dto.getSubmitmethod() );
        faTask.setCron( dto.getCron() );
        faTask.setExecutionengine( dto.getExecutionengine() );
        faTask.setExecutionstate( dto.getExecutionstate() );
        faTask.setStatus( dto.getStatus() );
        faTask.setSensitivestrategy( dto.getSensitivestrategy() );
        faTask.setCreateuser( dto.getCreateuser() );
        faTask.setCreatetime( dto.getCreatetime() );
        faTask.setUpdateuser( dto.getUpdateuser() );
        faTask.setUpdatetime( dto.getUpdatetime() );
        faTask.setSparefield1( dto.getSparefield1() );
        faTask.setSparefield2( dto.getSparefield2() );
        faTask.setSparefield3( dto.getSparefield3() );
        faTask.setSparefield4( dto.getSparefield4() );

        return faTask;
    }

    @Override
    public FaTaskDto toDto(FaTask entity) {
        if ( entity == null ) {
            return null;
        }

        FaTaskDto faTaskDto = new FaTaskDto();

        faTaskDto.setId( entity.getId() );
        faTaskDto.setTaskname( entity.getTaskname() );
        faTaskDto.setTasktype( entity.getTasktype() );
        faTaskDto.setSourceid( entity.getSourceid() );
        faTaskDto.setFilepath( entity.getFilepath() );
        faTaskDto.setUnzippassword( entity.getUnzippassword() );
        faTaskDto.setSubmitmethod( entity.getSubmitmethod() );
        faTaskDto.setCron( entity.getCron() );
        faTaskDto.setExecutionengine( entity.getExecutionengine() );
        faTaskDto.setExecutionstate( entity.getExecutionstate() );
        faTaskDto.setStatus( entity.getStatus() );
        faTaskDto.setSensitivestrategy( entity.getSensitivestrategy() );
        faTaskDto.setCreateuser( entity.getCreateuser() );
        faTaskDto.setCreatetime( entity.getCreatetime() );
        faTaskDto.setUpdateuser( entity.getUpdateuser() );
        faTaskDto.setUpdatetime( entity.getUpdatetime() );
        faTaskDto.setSparefield1( entity.getSparefield1() );
        faTaskDto.setSparefield2( entity.getSparefield2() );
        faTaskDto.setSparefield3( entity.getSparefield3() );
        faTaskDto.setSparefield4( entity.getSparefield4() );

        return faTaskDto;
    }

    @Override
    public List<FaTask> toEntity(List<FaTaskDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<FaTask> list = new ArrayList<FaTask>( dtoList.size() );
        for ( FaTaskDto faTaskDto : dtoList ) {
            list.add( toEntity( faTaskDto ) );
        }

        return list;
    }

    @Override
    public List<FaTaskDto> toDto(List<FaTask> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<FaTaskDto> list = new ArrayList<FaTaskDto>( entityList.size() );
        for ( FaTask faTask : entityList ) {
            list.add( toDto( faTask ) );
        }

        return list;
    }
}
