package com.wzsec.modules.fa.service.mapper;

import com.wzsec.modules.fa.domain.FaTask;
import com.wzsec.modules.fa.service.dto.FaTaskDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:26:48+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class FaTaskMapperImpl implements FaTaskMapper {

    @Override
    public FaTaskDto toDto(FaTask entity) {
        if ( entity == null ) {
            return null;
        }

        FaTaskDto faTaskDto = new FaTaskDto();

        faTaskDto.setCreatetime( entity.getCreatetime() );
        faTaskDto.setCreateuser( entity.getCreateuser() );
        faTaskDto.setCron( entity.getCron() );
        faTaskDto.setExecutionengine( entity.getExecutionengine() );
        faTaskDto.setExecutionstate( entity.getExecutionstate() );
        faTaskDto.setFilepath( entity.getFilepath() );
        faTaskDto.setId( entity.getId() );
        faTaskDto.setSensitivestrategy( entity.getSensitivestrategy() );
        faTaskDto.setSourceid( entity.getSourceid() );
        faTaskDto.setSparefield1( entity.getSparefield1() );
        faTaskDto.setSparefield2( entity.getSparefield2() );
        faTaskDto.setSparefield3( entity.getSparefield3() );
        faTaskDto.setSparefield4( entity.getSparefield4() );
        faTaskDto.setStatus( entity.getStatus() );
        faTaskDto.setSubmitmethod( entity.getSubmitmethod() );
        faTaskDto.setTaskname( entity.getTaskname() );
        faTaskDto.setTasktype( entity.getTasktype() );
        faTaskDto.setUnzippassword( entity.getUnzippassword() );
        faTaskDto.setUpdatetime( entity.getUpdatetime() );
        faTaskDto.setUpdateuser( entity.getUpdateuser() );

        return faTaskDto;
    }

    @Override
    public List<FaTaskDto> toDto(List<FaTask> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<FaTaskDto> list = new ArrayList<FaTaskDto>( entityList.size() );
        for ( FaTask faTask : entityList ) {
            list.add( toDto( faTask ) );
        }

        return list;
    }

    @Override
    public FaTask toEntity(FaTaskDto dto) {
        if ( dto == null ) {
            return null;
        }

        FaTask faTask = new FaTask();

        faTask.setCreatetime( dto.getCreatetime() );
        faTask.setCreateuser( dto.getCreateuser() );
        faTask.setCron( dto.getCron() );
        faTask.setExecutionengine( dto.getExecutionengine() );
        faTask.setExecutionstate( dto.getExecutionstate() );
        faTask.setFilepath( dto.getFilepath() );
        faTask.setId( dto.getId() );
        faTask.setSensitivestrategy( dto.getSensitivestrategy() );
        faTask.setSourceid( dto.getSourceid() );
        faTask.setSparefield1( dto.getSparefield1() );
        faTask.setSparefield2( dto.getSparefield2() );
        faTask.setSparefield3( dto.getSparefield3() );
        faTask.setSparefield4( dto.getSparefield4() );
        faTask.setStatus( dto.getStatus() );
        faTask.setSubmitmethod( dto.getSubmitmethod() );
        faTask.setTaskname( dto.getTaskname() );
        faTask.setTasktype( dto.getTasktype() );
        faTask.setUnzippassword( dto.getUnzippassword() );
        faTask.setUpdatetime( dto.getUpdatetime() );
        faTask.setUpdateuser( dto.getUpdateuser() );

        return faTask;
    }

    @Override
    public List<FaTask> toEntity(List<FaTaskDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<FaTask> list = new ArrayList<FaTask>( dtoList.size() );
        for ( FaTaskDto faTaskDto : dtoList ) {
            list.add( toEntity( faTaskDto ) );
        }

        return list;
    }
}
