package com.wzsec.utils;

import cn.afterturn.easypoi.entity.ImageEntity;
import lombok.extern.slf4j.Slf4j;
import org.jfree.chart.ChartFactory;
import org.jfree.chart.ChartUtilities;
import org.jfree.chart.JFreeChart;
import org.jfree.chart.StandardChartTheme;
import org.jfree.chart.axis.DateAxis;
import org.jfree.chart.labels.ItemLabelAnchor;
import org.jfree.chart.labels.ItemLabelPosition;
import org.jfree.chart.labels.StandardPieSectionLabelGenerator;
import org.jfree.chart.labels.StandardXYItemLabelGenerator;
import org.jfree.chart.plot.PiePlot;
import org.jfree.chart.plot.XYPlot;
import org.jfree.chart.renderer.xy.XYItemRenderer;
import org.jfree.chart.renderer.xy.XYLineAndShapeRenderer;
import org.jfree.data.general.DefaultPieDataset;
import org.jfree.data.time.TimeSeriesCollection;
import org.jfree.ui.RectangleInsets;
import org.jfree.ui.TextAnchor;
import org.springframework.util.Assert;

import java.awt.*;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Map;

@Slf4j
public class JfreeUtils {

    private static String tempImgPath = "D:\\tempJfree.jpeg";

    /**
     * 将图片转化为字节数组
     *
     * @return 字节数组
     */
    private static byte[] imgToByte() {
        File file = new File(tempImgPath);
        byte[] buffer = null;
        try {
            FileInputStream fis = new FileInputStream(file);
            ByteArrayOutputStream bos = new ByteArrayOutputStream(1000);
            byte[] b = new byte[1000];
            int n;
            while ((n = fis.read(b)) != -1) {
                bos.write(b, 0, n);
            }
            fis.close();
            bos.close();
            buffer = bos.toByteArray();
        } catch (IOException e) {
            log.error(e.getMessage());
        }
        //删除临时文件
        file.delete();
        return buffer;
    }

    /**
     * 饼状图
     *
     * @param datas          数据
     * @param width          宽度
     * @param height         高度
     * @param title          标题
     * @param legendColorMap 自定义饼图颜色(null即为默认)
     * @return {@link ImageEntity}
     */
    public static ImageEntity pieChart(String title, Map<String, Integer> datas, int width, int height, Map<String, Color> legendColorMap) {

        //创建主题样式
        StandardChartTheme standardChartTheme = new StandardChartTheme("CN");
        //设置标题字体
        standardChartTheme.setExtraLargeFont(new Font("宋体", Font.BOLD, 20));
        //设置图例的字体
        standardChartTheme.setRegularFont(new Font("宋体", Font.PLAIN, 15));
        //设置轴向的字体
        standardChartTheme.setLargeFont(new Font("宋体", Font.PLAIN, 15));
        //设置主题样式
        ChartFactory.setChartTheme(standardChartTheme);

        //根据jfree生成一个本地饼状图
        DefaultPieDataset pds = new DefaultPieDataset();
        datas.forEach(pds::setValue);
        //图标标题、数据集合、是否显示图例标识、是否显示tooltips、是否支持超链接
        JFreeChart chart = ChartFactory.createPieChart(title, pds, true, false, false);
        //设置抗锯齿
        chart.setTextAntiAlias(false);
        PiePlot plot = (PiePlot) chart.getPlot();
        plot.setNoDataMessage("暂无数据");
        //忽略无值的分类
        plot.setIgnoreNullValues(true);
        plot.setBackgroundAlpha(0f);
        //设置标签阴影颜色
        plot.setShadowPaint(new Color(255, 255, 255));
        //设置标签生成器(默认{0})
        plot.setLabelGenerator(new StandardPieSectionLabelGenerator("{0}({1})/{2}"));

        // 告警等级分布图颜色定义(null即为默认值)
        if (legendColorMap != null) {
            for (String legendName : legendColorMap.keySet()) {
                plot.setSectionPaint(legendName, legendColorMap.get(legendName));
            }
        }

        try {
            ChartUtilities.saveChartAsJPEG(new File(tempImgPath), chart, width, height);
        } catch (IOException e1) {
            log.error("生成饼状图失败！");
        }
        ImageEntity imageEntity = new ImageEntity(imgToByte(), width, height);
        Assert.notNull(imageEntity.getData(), "生成饼状图对象失败！");
        return imageEntity;
    }


    /**
     * 折线图
     *
     * @param title       标题
     * @param xAxisLabel  x轴标签
     * @param yAxisLabel  y轴标签
     * @param width       宽度
     * @param height      高度
     * @param lineDataset 行数据集
     * @return {@link ImageEntity}
     */
    public static ImageEntity lineChart(String title, String xAxisLabel, String yAxisLabel, int width, int height, TimeSeriesCollection lineDataset) {

        // 步骤2：根据Dataset 生成JFreeChart对象，以及做相应的设置
        JFreeChart jfreechart = ChartFactory.createTimeSeriesChart(
                title,       // 标题
                xAxisLabel,       // categoryAxisLabel （category轴，横轴，X轴的标签）
                yAxisLabel,  // valueAxisLabel（value轴，纵轴，Y轴的标签）
                lineDataset,// dataset
                true,       // legend
                true,       // tooltips
                true);      // URLs

        jfreechart.setBackgroundPaint(Color.white);
        XYPlot xyplot = (XYPlot) jfreechart.getPlot(); // 获得 plot：XYPlot！

        xyplot.getDomainAxis().setLabelFont(new Font("宋体", Font.PLAIN, 15));// X轴
        xyplot.getRangeAxis().setLabelFont(new Font("宋体", Font.PLAIN, 15)); // Y轴
        jfreechart.getLegend().setItemFont(new Font("宋体", Font.PLAIN, 15)); // 底部
        jfreechart.getTitle().setFont(new Font("宋体", Font.BOLD, 20)); // 图片标题

        //设置时间格式，同时也解决了乱码问题
        DateAxis dateaxis = (DateAxis) xyplot.getDomainAxis();
        SimpleDateFormat sfd = new SimpleDateFormat("yyyy-MM-dd");
        dateaxis.setDateFormatOverride(sfd);
        xyplot.setDomainAxis(dateaxis);

        // 以下的设置可以由用户定制，也可以省略
        XYPlot plot = (XYPlot) jfreechart.getPlot();
        XYLineAndShapeRenderer xylineandshaperenderer = (XYLineAndShapeRenderer) plot.getRenderer();
        // 设置网格背景颜色
        plot.setBackgroundPaint(Color.white);
        // 设置网格竖线颜色
        //plot.setDomainGridlinePaint(Color.pink);
        // 设置网格横线颜色
        //plot.setRangeGridlinePaint(Color.pink);
        // 设置曲线图与xy轴的距离
        plot.setAxisOffset(new RectangleInsets(0D, 0D, 0D, 10D));
        // 设置曲线是否显示数据点
        xylineandshaperenderer.setBaseShapesVisible(false);

        // 设置曲线显示各数据点的值
        XYItemRenderer xyitem = plot.getRenderer();
        xyitem.setBaseItemLabelsVisible(true);
        xyitem.setBasePositiveItemLabelPosition(new ItemLabelPosition(
                ItemLabelAnchor.OUTSIDE12, TextAnchor.BASELINE_LEFT));
        xyitem.setBaseItemLabelGenerator(new StandardXYItemLabelGenerator());
        xyitem.setBaseItemLabelFont(new Font("宋体", Font.PLAIN, 8));
        plot.setRenderer(xyitem);
        try {
            ChartUtilities.saveChartAsJPEG(new File(tempImgPath), jfreechart, width, height);
        } catch (IOException e1) {
            log.error("生成折线图失败！");
        }
        ImageEntity imageEntity = new ImageEntity(imgToByte(), width, height);
        Assert.notNull(imageEntity.getData(), "生成折线图对象失败！");
        return imageEntity;
    }

}