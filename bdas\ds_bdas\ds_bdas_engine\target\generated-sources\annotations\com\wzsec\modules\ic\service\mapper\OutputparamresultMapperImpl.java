package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.Outputparamresult;
import com.wzsec.modules.ic.service.dto.OutputparamresultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:27:07+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class OutputparamresultMapperImpl implements OutputparamresultMapper {

    @Override
    public OutputparamresultDto toDto(Outputparamresult entity) {
        if ( entity == null ) {
            return null;
        }

        OutputparamresultDto outputparamresultDto = new OutputparamresultDto();

        outputparamresultDto.setApicode( entity.getApicode() );
        outputparamresultDto.setApiname( entity.getApiname() );
        outputparamresultDto.setChecktime( entity.getChecktime() );
        outputparamresultDto.setFielddes( entity.getFielddes() );
        outputparamresultDto.setFieldlevel( entity.getFieldlevel() );
        outputparamresultDto.setId( entity.getId() );
        outputparamresultDto.setSparefield1( entity.getSparefield1() );
        outputparamresultDto.setSparefield2( entity.getSparefield2() );
        outputparamresultDto.setSparefield3( entity.getSparefield3() );
        outputparamresultDto.setSparefield4( entity.getSparefield4() );
        outputparamresultDto.setSparefield5( entity.getSparefield5() );

        return outputparamresultDto;
    }

    @Override
    public List<OutputparamresultDto> toDto(List<Outputparamresult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<OutputparamresultDto> list = new ArrayList<OutputparamresultDto>( entityList.size() );
        for ( Outputparamresult outputparamresult : entityList ) {
            list.add( toDto( outputparamresult ) );
        }

        return list;
    }

    @Override
    public Outputparamresult toEntity(OutputparamresultDto dto) {
        if ( dto == null ) {
            return null;
        }

        Outputparamresult outputparamresult = new Outputparamresult();

        outputparamresult.setApicode( dto.getApicode() );
        outputparamresult.setApiname( dto.getApiname() );
        outputparamresult.setChecktime( dto.getChecktime() );
        outputparamresult.setFielddes( dto.getFielddes() );
        outputparamresult.setFieldlevel( dto.getFieldlevel() );
        outputparamresult.setId( dto.getId() );
        outputparamresult.setSparefield1( dto.getSparefield1() );
        outputparamresult.setSparefield2( dto.getSparefield2() );
        outputparamresult.setSparefield3( dto.getSparefield3() );
        outputparamresult.setSparefield4( dto.getSparefield4() );
        outputparamresult.setSparefield5( dto.getSparefield5() );

        return outputparamresult;
    }

    @Override
    public List<Outputparamresult> toEntity(List<OutputparamresultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Outputparamresult> list = new ArrayList<Outputparamresult>( dtoList.size() );
        for ( OutputparamresultDto outputparamresultDto : dtoList ) {
            list.add( toEntity( outputparamresultDto ) );
        }

        return list;
    }
}
