package com.wzsec.utils;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;


/**
 *@Description:MD5 16位
 *<AUTHOR> by xiongpf
 *@date 2018-03-01
 */
public class MD516BIT {
    // private static Logger log = Logger.getLogger(MD516BIT.class);


	private static final String ALGORITHM_MD5 = "MD5";

	/**
	 * MD5 16位加密
	 * @param paramString
	 * @return
	 * @throws NoSuchAlgorithmException
	 * @throws UnsupportedEncodingException
	 */
	public static final String encrypt(String paramString)
			throws NoSuchAlgorithmException {
		if (paramString != null) {
			return MD516BIT.MD5_32bit(paramString).substring(8, 24);
		} else {
			return null;
		}
	}

	/**
	 * MD5 32位加密
	 * @param readyEncryptStr
	 * @return
	 * @throws NoSuchAlgorithmException
	 */
	public static final String MD5_32bit(String readyEncryptStr) throws NoSuchAlgorithmException {
		if (readyEncryptStr != null) {
			MessageDigest md = MessageDigest.getInstance(ALGORITHM_MD5);
			md.update(readyEncryptStr.getBytes());
			byte[] b = md.digest();
			StringBuilder su = new StringBuilder();
			for (int offset = 0, bLen = b.length; offset < bLen; offset++) {
				String haxHex = Integer.toHexString(b[offset] & 0xFF);
				if (haxHex.length() < 2) {
					su.append("0");
				}
				su.append(haxHex);
			}
			return su.toString();
		} else {
			return null;
		}
	}

	public static void main(String[] args) {
		try {
			String str = "123456";
			String encStr = MD516BIT.encrypt(str);
			System.out.println(encStr);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

}
