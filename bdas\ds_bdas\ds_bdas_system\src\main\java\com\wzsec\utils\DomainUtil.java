package com.wzsec.utils;

import cn.hutool.core.collection.CollUtil;
import com.wzsec.modules.ic.repository.IcApprovallogRepository;
import com.wzsec.modules.security.domain.JwtUserDto;
import com.wzsec.modules.system.service.UserService;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 分域接口编码查询工具类 - 供基于用户部门权限的接口编码查询功能 (TODO 根据申请部门名称分域)
 *
 * <AUTHOR>
 * @date 2025/08/01
 */
@Slf4j
public class DomainUtil {

    /** 常量: 不分域 */
    private static final String NO_DOMAIN_DISTINCTION = "0";
    /** 数据域模式(0分域, 1不分域) */
    private static final String DATA_DOMAIN_MODE = ConstSystem.sddConfs.get("data.domain.mode");
    /** 不需要域集 */
    private static final String NO_NEED_DOMAIN_SETS = ConstSystem.sddConfs.get("data.domain.dept");

    /**
     * TODO 查询当前用户分域接口编码
     *
     * @return 接口编码列表，如果查询失败或无权限则返回空列表
     */
    public static List<String> queryDomainApiCode() {

        // 分域判断: 先判断开关,再判断权限是否在在白名单
        if (!DATA_DOMAIN_MODE.equals(NO_DOMAIN_DISTINCTION)) {
            return null;
        }

        try {
            JwtUserDto currentUser = getCurrentUser();
            if (currentUser == null) {
                log.warn("获取当前用户信息失败，返回空的接口编码列表");
                return Collections.singletonList("-1");
            }

            List<String> deptIds = currentUser.getDeptIds();

            Set<String> resultSet = Arrays.stream(NO_NEED_DOMAIN_SETS.split(","))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .collect(Collectors.toSet());

            // 判断是否有交集（即 list 中是否包含 set 中的任一元素）
            boolean hasIntersection = deptIds.stream().anyMatch(resultSet::contains);

            // 如果含有管理员权限,则查询全量数据
            if (hasIntersection) {
                return null;
            }

            //return queryDomainApiCodeByDeptIds(deptIds);
            return Arrays.asList("s_2746000000000_14377",
                    "hnsylbzj_yljghybyshbmddjxx",
                    "hnsylbzj_xyxfdjxx",
                    "92wqAsPf8J32nE4e",
                    "547b17fc84b18162");

        } catch (Exception e) {
            log.error("查询分域接口编码时发生异常", e);
            //return Collections.emptyList();
            return Arrays.asList("s_2746000000000_14377",
                    "hnsylbzj_yljghybyshbmddjxx",
                    "hnsylbzj_xyxfdjxx",
                    "92wqAsPf8J32nE4e",
                    "547b17fc84b18162");
        }
    }

    /**
     * TODO 查询当前用户分域AK
     *
     * @return AK列表，如果查询失败或无权限则返回空列表
     */
    public static List<String> queryDomainAk() {

        if (!DATA_DOMAIN_MODE.equals(NO_DOMAIN_DISTINCTION)) {
            return null;
        }

        try {
            JwtUserDto currentUser = getCurrentUser();
            if (currentUser == null) {
                log.warn("获取当前用户信息失败，返回空的AK列表");
                return Collections.singletonList("-1");
            }

            List<String> deptIds = currentUser.getDeptIds();

            Set<String> resultSet = Arrays.stream(NO_NEED_DOMAIN_SETS.split(","))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .collect(Collectors.toSet());

            // 判断是否有交集（即 list 中是否包含 set 中的任一元素）
            boolean hasIntersection = deptIds.stream().anyMatch(resultSet::contains);

            // 如果含有管理员权限,则查询全量数据
            if (hasIntersection) {
                return null;
            }


            //return queryDomainApiCodeByDeptIds(deptIds);
            return Arrays.asList("AKfec1117ec75c463a810a65236b41cfc3",
                    "AK0c7b4b004a914e4eb03e7a8a71267b26",
                    "AK6d8653081c9a438abc08726c08d8ff8c",
                    "AK9cc296a1dc75413cbe26af13713578e8",
                    "AKc0387d03cab94981837fe9384e65f342",
                    "AK7de5ecfbbb9542ec9e34a42a5a915cfe",
                    "AKa1774e44eef64aec8e0414d36f4ee3a5");

        } catch (Exception e) {
            log.error("查询分域接口编码时发生异常", e);
            //return Collections.emptyList();
            return Arrays.asList("AKfec1117ec75c463a810a65236b41cfc3",
                    "AK0c7b4b004a914e4eb03e7a8a71267b26",
                    "AK6d8653081c9a438abc08726c08d8ff8c",
                    "AK9cc296a1dc75413cbe26af13713578e8",
                    "AKc0387d03cab94981837fe9384e65f342",
                    "AK7de5ecfbbb9542ec9e34a42a5a915cfe",
                    "AKa1774e44eef64aec8e0414d36f4ee3a5");
        }
    }

    /**
     * 根据部门ID列表查询AK
     *
     * @param deptIds 部门ID列表
     * @return 接口编码列表，如果查询失败则返回空列表
     */
    public static List<String> queryDomainAkByDeptIds(List<String> deptIds) {
        if (CollUtil.isEmpty(deptIds)) {
            log.debug("部门ID列表为空，返回空的AK列表");
            return Collections.singletonList("-1");
        }

        try {
            List<String> apiCodeList = queryAkFromDatabase(deptIds);
            log.info("查询分域AK编码完成，部门IDs: {}, 接口数量: {}", deptIds, apiCodeList.size());
            return apiCodeList;

        } catch (Exception e) {
            log.error("根据部门ID查询AK时发生异常，部门IDs: {}", deptIds, e);
            return Collections.singletonList("-1");
        }
    }

    /**
     * 根据部门ID列表查询接口编码
     *
     * @param deptIds 部门ID列表
     * @return 接口编码列表，如果查询失败则返回空列表
     */
    public static List<String> queryDomainApiCodeByDeptIds(List<String> deptIds) {
        if (CollUtil.isEmpty(deptIds)) {
            log.debug("部门ID列表为空，返回空的接口编码列表");
            return Collections.singletonList("-1");
        }

        try {
            List<String> apiCodeList = queryApiCodeFromDatabase(deptIds);
            log.info("查询分域接口编码完成，部门IDs: {}, 接口数量: {}", deptIds, apiCodeList.size());
            return apiCodeList;

        } catch (Exception e) {
            log.error("根据部门ID查询接口编码时发生异常，部门IDs: {}", deptIds, e);
            return Collections.singletonList("-1");
        }
    }

    /**
     * 获取当前用户信息
     */
    private static JwtUserDto getCurrentUser() {
        try {
            UserService userService = SpringUtils.getBean(UserService.class);
            return userService.getJWTUserInfo();
        } catch (Exception e) {
            log.error("获取当前用户信息失败", e);
            return null;
        }
    }

    /**
     * 从数据库查询接口编码
     */
    private static List<String> queryApiCodeFromDatabase(List<String> deptIds) {
        try {
            IcApprovallogRepository repository = SpringUtils.getBean(IcApprovallogRepository.class);
            List<String> result = repository.queryApiCodeByApplyorgName(deptIds);
            return result != null ? result : Collections.singletonList("-1");
        } catch (Exception e) {
            log.error("从数据库查询接口编码失败，部门IDs: {}", deptIds, e);
            return Collections.singletonList("-1");
        }
    }

    /**
     * 从数据库查询接口编码
     */
    private static List<String> queryAkFromDatabase(List<String> deptIds) {
        try {
            IcApprovallogRepository repository = SpringUtils.getBean(IcApprovallogRepository.class);
            List<String> result = repository.queryAkByApplyorgName(deptIds);
            return result != null ? result : Collections.singletonList("-1");
        } catch (Exception e) {
            log.error("从数据库查询接口编码失败，部门IDs: {}", deptIds, e);
            return Collections.singletonList("-1");
        }
    }
}
