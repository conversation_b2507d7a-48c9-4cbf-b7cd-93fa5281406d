package com.wzsec.utils.rule;

import java.util.Map;

import javax.validation.ValidationException;

import com.wzsec.utils.Const;
import com.wzsec.utils.StringUtils;
import org.apache.commons.collections4.BidiMap;
import org.apache.commons.collections4.bidimap.TreeBidiMap;


/**
 * 数据标识符规则工具类(基本不修改)，自定义规则在Rule4ProgramUtil.java
 *
 * <AUTHOR>
 * @date 2020-4-28
 */
public class Rule4IdentifierUtil {


//    public static void main(String[] args) {
//        // 组织机构代码校验
//        System.out.println("组织机构代码校验:" + p_checkValidEntpCode("********-3"));
//        // 车牌号校验
//        System.out.println("车牌号校验:" + p_checkCarnumberNO("粤B12345"));
//        // IP校验
//        System.out.println("IP校验:" + p_checkIP("***********"));
//        // 银行卡号校验
//        System.out.println("银行卡号校验:" + p_checkBankCard("6212261715002228361"));
//        // 营业执照校验
//        System.out.println("营业执照校验:" + p_checkBusinesslicense("***************"));
//        // 社会统一信用代码校验
//        System.out.println("社会统一信用代码校验:" + p_checkUnifiedCreditCode("91110108MA00E1PU0C"));
//        // 护照校验
//        System.out.println("护照校验:" + p_checkPassPortCard("********"));
//        // 军官证校验
//        System.out.println("军官证校验:" + p_checkOfficerCard("军字第2001988号"));
//        // 港澳通行证校验
//        System.out.println("港澳通行证校验:" + p_checkHMPassCheck("H1234567890"));
//        // 税务登记证校验
//        System.out.println("税务登记证校验:" + p_checkTaxationNo("***************"));
//        // 开户许可证校验
//        System.out.println("开户许可证校验:" + p_checkAccountOpeningPermitNo("J4693000413701"));
//    }


    /**
     * @Description:车辆识别号码校验
     * <AUTHOR>
     * @date 2020-5-20 17:50:19
     */
    public static boolean p_checkVIN(String data) {
        if (StringUtils.isEmpty(data))
            return false;
        return VinUtil.isValidVin(data);
    }

    /**
     * @Description:QQ号校验
     * <AUTHOR>
     * @date 2020-5-20 17:35:33
     */
    public static boolean p_checkQQ(String data) {
        if (StringUtils.isEmpty(data))
            return false;
        //5-11位纯数字，且不能0开头
        if (data.length() >= 5 && data.length() <= 11) {
            return data.matches("[1-9]([0-9]{4,10})");
        }
        return false;
    }


    /**
     * @Description:手机号码校验
     * <AUTHOR> by dongs
     * @date 2019-1-16
     */
    public static boolean p_checkMobilePhone(String data) {
        if (StringUtils.isEmpty(data))
            return false;
        if (data.startsWith("1") && data.length() == 11) {
            if (Const.phoneWhiteList.contains(data)) { // 在手机号白名单中，返回false
                return false;
            }
            return data.matches("^1(3[0-9]|4[5-9]|5[0-3,5-9]|6[5,6]|7[0-8]|8[0-9]|9[1,8,9])\\d{8}$");
        }
        return false;
    }

    /**
     * @Description:固话校验
     * <AUTHOR> by dongs
     * @date 2019-1-16
     */
    public static boolean p_checkFixedPhone(String data) {
        if (StringUtils.isEmpty(data))
            return false;
//        if (data.startsWith("01") && (data.length() == 7 || data.length() == 8)) {
//            return data.matches("^[0]{1}[0-9]{2}-[0-9]{7,8}$");
//        }
        if (data.length() == 10 || data.length() == 11 || data.length() == 12 || data.length() == 13) {
            return data.matches("\\(?0\\d{2}[) -]?\\d{8}") || data.matches("\\(?0\\d{3}[) -]?\\d{8}") || data.matches("\\(?0\\d{2}[) -]?\\d{7}") || data.matches("\\(?0\\d{3}[) -]?\\d{7}");
        }
        return false;
    }

    /**
     * @Description:身份证号校验
     * <AUTHOR> by dongs
     * @date 2019-1-16
     */
    public static boolean p_checkIDCardNo(String data) {
        if (StringUtils.isEmpty(data))
            return false;
        if (data.length() == 15 || data.length() == 18) {
            if (data.matches("^(^\\d{18}$)|(^\\d{17}(\\d|X|x))$")) {
                if (IdCardVerification.IDCardValidate(data)) {
                    return true;
                }
            }
        }
        return false;
    }


    public static void main(String[] args) {
        System.out.println(p_checkIDCardNo("110104198406240921"));
    }

    /**
     * @Description:邮箱校验
     * <AUTHOR> by dongs
     * @date 2019-1-16
     * @modifier zhangkunxiang 2020年2月14日18:27:49
     */
    public static boolean p_checkEmail(String data) {
        //             * “@”和最后一-个“. ”中间支持"字母数字.”组合任意长度，如@Com.com1.Com11.00.cn.cn.cn.后缀
        //                * 最后一个“.”后面只能是小写字母且长度大于2,如@.com.cn.aa.bb.comcomcomcomcom
        //                *//*
        //     * System.out.println(p_checkEmail("<EMAIL>")); }
        if (StringUtils.isEmpty(data))
            return false;
        if (data.contains("@") && data.contains(".")) {
            // if (CheckRule. checkingRule(Str,
            // "^\\w+([-+.]\\w+)*@\\w+([-.]\\W+)*\\. \\w+([-.]\\w+)*$")) {
            return data.matches("^[a-z0-9A-Z]+[-|a-z0-9A-Z._]+@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-z]{2,18}$");
        }
        return false;
    }

    /***
     * @Description IMSI校验
     * @param data
     * @return
     * @throws Exception
     */
    public static boolean p_checkIMSI(String data) {
        if (StringUtils.isEmpty(data))
            return false;
        if (data.startsWith("460") && data.length() == 15) {
            return data.matches("^460[0-9]{12}$");
        }
        return false;
    }

    /**
     * @Description IMEI校验(英国35 、 98 美国01 中国86 印度91)
     * <AUTHOR>
     * @version 下午5:36:38
     */
    public static boolean p_checkIMEI(String data) {
        if (StringUtils.isEmpty(data))
            return false;
        if (data.matches("^((01|35|86|91|98)\\d{13})|((01|35|86|91|98)\\d{14})|((01|35|86|91|98)\\d{15})$")) {
            String str14Bit = data.substring(0, 14); // 截取前14位
            String str15th = data.substring(14, 15); // 截取第15位
            // 计算出第15位检验位
            String checkBit = DeviceUtil.getIMEI15(str14Bit);
            if (str15th.equals(checkBit)) {
                return true; // 校验通过
            } else {
                return false; // 校验不通过
            }
        } else {
            return false;
        }
    }

    /**
     * @Description MEID校验
     * <AUTHOR>
     * @version 下午5:36:38
     */
    public static boolean p_checkMEID(String data) {
        if (StringUtils.isEmpty(data))
            return false;
        if (data.matches("^(A0|A1|A2|97|98|99)[A-F0-9]{13}$")) {
            String str14Bit = data.substring(0, 14); // 截取前14位
            String str15th = data.substring(14, 15); // 截取第15位
            // 计算出第15位检验位
            String checkBit = DeviceUtil.getMEID15(str14Bit);
            if (DeviceUtil.stringToAscii(str15th).equals(checkBit)) {
                return true; // 校验通过
            } else {
                return false; // 校验不通过
            }
        } else if (data.matches("^(A0|A1|A2|97|98|99)[A-F0-9]{12}$")
                || data.matches("^(A0|A1|A2|97|98|99)[A-F0-9]{14}$")) {
            int checkStrLength = data.length();
            int checkStrReplace0Later = data.replace("0", "").length();
            // 过滤去除包含一半字符都是“0”的或包含连续4个都是“0”的疑似MEID
            if ((checkStrLength - checkStrReplace0Later) >= (data.length() / 2) || data.contains("0000")) {
                return false;
            }
            return true; // 可能是14位、16位，有可能是，先怀疑
        } else {
            return false;
        }
    }


    /**
     * @return boolean
     * @Description IPv6校验(未测试)
     * <AUTHOR>
     * @date 2020-5-20 17:43:16
     */
    public static boolean p_checkIPv6(String data) {
        if (StringUtils.isEmpty(data))
            return false;
        String regex = "^\\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:)))(%.+)?\\s*$";
        return data.matches(regex);
    }

    /**
     * @return boolean
     * @Description IP校验
     * <AUTHOR>
     * @date 2019年11月14日 下午4:29:18
     */
    public static boolean p_checkIP(String data) {
        if (StringUtils.isEmpty(data))
            return false;
        String regex = "((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})(\\.((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})){3}";
        return data.matches(regex);
    }

//    public static void main(String[] args) {
//        System.out.println(p_checkMAC("94-C6-91-51-D1-10"));
//        System.out.println(p_checkMAC("94-c6-91-51-d1-10"));
//
//        System.out.println(p_checkMAC("94:C6:91:51:D1:10"));
//        System.out.println(p_checkMAC("94:c6:91:51:d1:10"));
//    }

    /**
     * @return boolean
     * @Description MAC校验
     * <AUTHOR>
     * @date 2020年5月9日17:10:02
     */
    public static boolean p_checkMAC(String data) {
        if (StringUtils.isEmpty(data))
            return false;
        String regex1 = "^([0-9a-fA-F]{2})((:([0-9a-fA-F]{2})){5})$";
        String regex2 = "^([0-9a-fA-F]{2})((-([0-9a-fA-F]{2})){5})$";
        return data.matches(regex1) || data.matches(regex2);
    }

    /**
     * @return boolean
     * @Description 开户许可证校验
     * <AUTHOR>
     * @date 2019年11月14日 下午6:16:00
     */
    public static boolean p_checkAccountOpeningPermitNo(String accountOpeningPermitNo) {
        if (StringUtils.isEmpty(accountOpeningPermitNo))
            return false;
        // 由1位英文字母+13位数字组成，编码规则为：英文字母（J基本户、L临时户、Z专用户）+XXXX（地区代码）+ XXXXXXX（顺序号）+
        // XX（版本号）；最后2位表示第几个版本：这个要解释一下，比如你新开，那么最后2位就是01；。
        // 核准号:J4693000413701
        String regex = "^[JLZ]{1}\\d{4}\\d{7}\\d{2}$";
        return accountOpeningPermitNo.matches(regex);
    }

    /**
     * @param passport：护照号
     * @return boolean
     * @Description 护照校验
     * <AUTHOR>
     * @date 2019年11月14日 下午4:59:01
     */
    public static boolean p_checkPassPortCard(String passport) {
        if (StringUtils.isEmpty(passport))
            return false;
        // 护照
        // 规则： 14/15开头 + 7位数字, G + 8位数字, P + 7位数字, S/D + 7或8位数字,等
        // 样本： *********, G12345678, ********
        String regex = "^((1[45]\\d{7})|(G\\d{8})|(P\\d{7})|(S\\d{7,8}))?$";
        return passport.matches(regex);
    }

    /**
     * @param officerNo：军官证号码
     * @return boolean
     * @Description 军官证校验
     * <AUTHOR>
     * @date 2019年11月14日 下午5:00:16
     */
    public static boolean p_checkOfficerCard(String officerNo) {
        if (StringUtils.isEmpty(officerNo))
            return false;
        // 军官证
        // 规则： 军/兵/士/文/职/广/（其他中文） + "字第" + 4到8位字母或数字 + "号"
        // 样本： 军字第2001988号, 士字第P011816X号
        String regex = "^[\u4E00-\u9FA5](字第)([0-9a-zA-Z]{4,8})(号?)$";
        return officerNo.matches(regex);
    }

    /**
     * @param HMPassCheck：港澳通行证号码
     * @return boolean
     * @Description 港澳通行证校验
     * <AUTHOR>
     * @date 2019年11月14日 下午5:08:41
     */
    public static boolean p_checkHMPassCheck(String HMPassCheck) {
        if (StringUtils.isEmpty(HMPassCheck))
            return false;
        // 港澳居民来往内地通行证
        // 规则： H/M + 10位或8位数字
        // 样本： H1234567890
        String regex = "^[HMhm]{1}([0-9]{10}|[0-9]{8})$";
        return HMPassCheck.matches(regex);
    }

    /**
     * @param taxationNo：税务登记证号码
     * @return boolean
     * @Description 税务登记证校验
     * <AUTHOR>
     * @date 2019年11月14日 下午5:10:11
     */
    public static boolean p_checkTaxationNo(String taxationNo) {
        if (StringUtils.isEmpty(taxationNo))
            return false;
        // 税务登记号编码规则是:纳税人识别号是指税务登记号（分组织机构代码和身份证注册）：
        // 1、税务登记证号由六位行政区划代码加九位组织机构代码组成。
        // 组织机构代码是质量技术监督局发放的组织机构代码证上的九位数字与大写拉丁字母，这个组合是唯一的。
        // 如：***************
        // 2、个体经营者办理税务登记证的由：旧的身份证15位码加5个0或新的身份证18位码加2个0；
        // 如果同一身份证办多户税务登记的，则第二户的税务登记证后两位改为“01”，第三户改为“02”。
        // 如：34222187060625900001，34222119870606259601
        if (taxationNo.length() == 15) {
            String addressNo = taxationNo.substring(0, 6);
            String regex = "\\d{6}";
            if (addressNo.matches(regex)) {
                String code = taxationNo.substring(6, 14) + "-" + taxationNo.substring(14);
                return p_checkValidEntpCode(code);
            }
            return false;
        } else if (taxationNo.length() == 20) {
            String idcard = taxationNo.substring(0, 18);
            String endStr = "";
            String regex = "";
            if (IdCardVerification.IDCardValidate(idcard)) {
                endStr = taxationNo.substring(18, 20);
                regex = "\\d{2}";
                return endStr.matches(regex);
            } else {
                idcard = taxationNo.substring(0, 15);
                if (IdCardVerification.IDCardValidate(idcard)) {
                    endStr = taxationNo.substring(15, 20);
                    regex = "\\d{5}";
                    return endStr.matches(regex);
                } else {
                    return false;
                }
            }
        }
        return false;
    }

    /**
     * @param code：组织机构代码，如：********-3,79013765-X
     * @return boolean
     * @Description 组织机构代码校验
     * <AUTHOR>
     * @date 2019年11月14日 下午4:17:49
     */
    public static final boolean p_checkValidEntpCode(String code) {
        // 由8位数字（或大写字母）和1位数字（或大写字母）组成

        // 1.全国组织机构代码由八位数字（或大写拉丁字母）本体代码和一位数字（或大写拉丁字母）校验码组成。
        // 本体代码采用系列（即分区段）顺序编码方法。校验码按照以下公式计算：
        // C9=11-MOD(∑Ci(i=1→8)×Wi,11)
        // 式中： MOD——代表求余函数；
        // i——代表代码字符从左至右位置序号；
        // Ci——代表第i位上的代码字符的值（具体代码字符见附表）；
        // C9——代表校验码；
        // Wi——代表第i位上的加权因子；
        // 当C9的值为10时，校验码应用大写的拉丁字母X表示；当C9的值为11时校验码用0表示。
        // 2.代码的表示形式
        // 为便于人工识别，应使用一个连字符“—”分隔本体代码与校验码。机读时，连字符省略。表示形式为：
        // xxxxxxxx—X
        // 3.自定义区
        // 为满足各系统管理上的特殊需要，规定本体代码PDY00001至PDY99999为自定义区，供各系统编制内部组织机构代码使用。自定义区内编制的组织机构代码不作为个系统之间信息交换的依据。
        if (StringUtils.isEmpty(code))
            return false;
        int[] ws = {3, 7, 9, 10, 5, 8, 4, 2};
        String str = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        String regex = "^([0-9A-Z]){8}-[0-9|X]$";
        if (!code.matches(regex)) {
            return false;
        }
        int sum = 0;
        for (int i = 0; i < 8; i++) {
            sum += str.indexOf(String.valueOf(code.charAt(i))) * ws[i];
        }
        // System.out.println("sum is " + sum);
        // System.out.println("sum % 11 is " + sum % 11);
        int c9 = 11 - (sum % 11);
        String sc9 = String.valueOf(c9);
        if (11 == c9) {
            sc9 = "0";
        } else if (10 == c9) {
            sc9 = "X";
        }
        // System.out.println("sc9 is " + sc9);
        return sc9.equals(String.valueOf(code.charAt(9)));
    }

    /**
     * @param carnumber：车牌号
     * @return boolean
     * @Description 车牌号校验
     * <AUTHOR>
     * @date 2019年11月14日 下午4:27:41
     */
    public static boolean p_checkCarnumberNO(String carnumber) {
        /*
         * 一、
         * 1.传统车牌。第1位为省份简称（汉字），第二位为发牌机关代号（A-Z的字母）第3到第7位为序号（由字母或数字组成，但不存在字母I和O，
         * 防止和数字1、0混淆，另外最后一位可能是“挂学警港澳使领”中的一个汉字）;
         * 2.新能源车牌。第1位和第2位与传统车牌一致，第3到第8位为序号（比传统车牌多一位）。新能源车牌的序号规则如下：
         * 小型车：第1位只能是字母D或F，第2为可以是数字或字母，第3到6位必须是数字。 大型车：第1位到第5位必须是数字，第6位只能是字母D或F。
         * 粤B12345，粤Z1234港
         *
         * 二、 1.常规车牌号：仅允许以汉字开头，后面可录入六个字符，由大写英文字母和阿拉伯数字组成。如：粤B12345；
         * 2.武警车牌：允许前两位为大写英文字母，后面可录入五个或六个字符，由大写英文字母和阿拉伯数字组成，
         * 其中第三位可录汉字也可录大写英文字母及阿拉伯数字，第三位也可空，如：WJ警00081、WJ京1234J、WJ1234X。
         * 3.最后一个为汉字的车牌：允许以汉字开头，后面可录入六个字符，前五位字符，由大写英文字母和阿拉伯数字组成，而最后一个字符为汉字，汉字包括“
         * 挂”、“学”、“警”、“军”、“港”、“澳”。如：粤Z1234港。
         * 4.新军车牌：以两位为大写英文字母开头，后面以5位阿拉伯数字组成。如：BA12345。
         */
        // 一
        String PlateNumMatch = "^(([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z](([0-9]{5}[DF])|([DF]([A-HJ-NP-Z0-9])[0-9]{4})))|([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳使领]))$";
        // 二
        // String carnumRegex =
        // "^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[警京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼]{0,1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$";
        if (StringUtils.isEmpty(carnumber))
            return false;
        else
            return carnumber.matches(PlateNumMatch);
    }


    /**
     * @param bankCard：银行卡号
     * @return boolean
     * @Description 银行卡校验
     * <AUTHOR>
     * @date 2019年11月14日 下午4:32:16
     */
    public static boolean p_checkBankCard(String bankCard) {
        /*
         * 校验过程： 1、从卡号最后一位数字开始，逆向将奇数位(1、3、5等等)相加。
         * 2、从卡号最后一位数字开始，逆向将偶数位数字，先乘以2（如果乘积为两位数，将个位十位数字相加，即将其减去9），再求和。
         * 3、将奇数位总和加上偶数位总和，结果应该可以被10整除。
         */
        if (StringUtils.isEmpty(bankCard))
            return false;
        if (bankCard.length() < 15 || bankCard.length() > 19) {
            return false;
        }
        char bit = getBankCardCheckCode(bankCard.substring(0, bankCard.length() - 1));
        if (bit == 'N') {
            return false;
        }
        return bankCard.charAt(bankCard.length() - 1) == bit;
    }


    /**
     * 从不含校验位的银行卡卡号采用 Luhm校验算法获得校验位
     *
     * @param nonCheckCodeBankCard
     * @return
     */
    private static char getBankCardCheckCode(String nonCheckCodeBankCard) {
        if (nonCheckCodeBankCard == null || nonCheckCodeBankCard.trim().length() == 0
                || !nonCheckCodeBankCard.matches("\\d+")) {
            // 如果传的不是数据返回N
            return 'N';
        }
        char[] chs = nonCheckCodeBankCard.trim().toCharArray();
        int luhmSum = 0;
        for (int i = chs.length - 1, j = 0; i >= 0; i--, j++) {
            int k = chs[i] - '0';
            if (j % 2 == 0) {
                k *= 2;
                k = k / 10 + k % 10;
            }
            luhmSum += k;
        }
        return (luhmSum % 10 == 0) ? '0' : (char) ((10 - luhmSum % 10) + '0');
    }

    /**
     * @param businesslicense： 营业执照15位或18位
     * @return boolean
     * @Description 营业执照校验
     * <AUTHOR>
     * @date 2019年11月14日 下午4:43:22
     */
    public static boolean p_checkBusinesslicense(String businesslicense) {
        if (StringUtils.isEmpty(businesslicense))
            return false;
        boolean result = false;
        if (businesslicense.length() == 15 && businesslicense.matches("^\\d{15}$")) {
            // 代码结构工商注册号由14位数字本体码和1位数字校验码组成，
            // 其中本体码从左至右依次为：6位首次登记机关码、8位顺序码，1位数字校验码组成。
            // ***************
            result = p_checkBusinesslicense15(businesslicense);
        } else if (businesslicense.length() == 18 && businesslicense.matches("^[A-Z0-9]{18}$")) {
            if (!businesslicense.contains("I") && !businesslicense.contains("O") && !businesslicense.contains("Z") && !businesslicense.contains("S") &&
                    !businesslicense.contains("V")) {
                // 统一代码由十八位阿拉伯数字或大写英文字母（不使用I、O、Z、S、V）组成，
                // 包括第1位登记管理部门代码、第2位机构类别代码、第3位~第8位登记管理机关行政区划码、第9位~第17位主体标识码（组织机构代码）、第18位校验码五个部门。
                // 913301097909487005
                result = p_checkBusinesslicense18(businesslicense);
            }
        }
        return result;
    }

    /**
     * @param businesslicense
     * @return boolean
     * @Description 15位营业执照校验
     * <AUTHOR>
     * @date 2019年11月14日 下午4:44:23
     */
    private static boolean p_checkBusinesslicense15(String businesslicense) {
        if ("".equals(businesslicense) || " ".equals(businesslicense)) {
            return false;
        } else if (businesslicense.length() != 15) {
            return false;
        }
        // 代码结构工商注册号由14位数字本体码和1位数字校验码组成，其中本体码从左至右依次为：6位首次登记机关码、8位顺序码，1位数字校验码组成。
        String businesslicensePrex14 = businesslicense.substring(0, 14);// 获取营业执照注册号前14位数字用来计算校验码
        String businesslicense15 = businesslicense.substring(14, businesslicense.length());// 获取营业执照号的校验码
        char[] chars = businesslicensePrex14.toCharArray();
        int[] ints = new int[chars.length];
        for (int i = 0; i < chars.length; i++) {
            ints[i] = Integer.parseInt(String.valueOf(chars[i]));
        }
        getCheckCode(ints);
        if (businesslicense15.equals(getCheckCode(ints) + "")) {// 比较
            return true;
        }
        return false;
    }

    /**
     * @param ints
     * @return int
     * @Description 获取 营业执照注册号的校验码
     * <AUTHOR>
     * @date 2019年11月14日 下午4:44:02
     */
    private static int getCheckCode(int[] ints) {
        if (null != ints && ints.length > 1) {
            int ti = 0;
            int si = 0;// pi|11+ti
            int cj = 0;// （si||10==0？10：si||10）*2
            int pj = 10;// pj=cj|11==0?10:cj|11
            for (int i = 0; i < ints.length; i++) {
                ti = ints[i];
                pj = (cj % 11) == 0 ? 10 : (cj % 11);
                si = pj + ti;
                cj = (0 == si % 10 ? 10 : si % 10) * 2;
                if (i == ints.length - 1) {
                    pj = (cj % 11) == 0 ? 10 : (cj % 11);
                    return pj == 1 ? 1 : 11 - pj;
                }
            }
        }
        return -1;

    }

    /**
     * @param code
     * @return boolean
     * @Description 18位营业执照校验
     * <AUTHOR>
     * @date 2019年11月14日 下午4:43:07
     */
    private static boolean p_checkBusinesslicense18(String code) {

        // 第一部分（第1位）：登记管理部门代码，使用阿拉伯数字或英文字母表示。例如，机构编制、民政、工商三个登记管理部门分别使用1、5、9表示，
        // 其他登记管理部门可使用相应阿拉伯数字或英文字母表示。
        // 第二部分（第2位）：机构类别代码，使用阿拉伯数字或英文字母表示。登记管理部门根据管理职能，确定在本部门登记的机构类别编码。
        // 例如，机构编制部门可用1表示机关单位，2表示事业单位，3表示由中央编办直接管理机构编制的群众团体；
        // 民政部门可用1表示社会团体，2表示民办非企业单位，3表示基金会；工商部门可用1表示企业，2表示个体工商户，3表示农民专业合作社。
        // 第三部分（第3—8位）：登记管理机关行政区划码，使用阿拉伯数字表示。
        // 例如，国家用100000，北京用110000，注册登记时由系统自动生成，体现法人和其他组织注册登记及其登记管理机关所在地，
        // 既满足登记管理部门按地区管理需求，也便于社会对注册登记主体所在区域进行识别。
        // （参照《中华人民共和国行政区划代码》„GB/T 2260—2007‟）
        // 第四部分（第9—17位）：主体标识码（组织机构代码），使用阿拉伯数字或英文字母表示。（参照《全国组织机构代码编制规则》„GB11714—1997‟）
        // 第五部分（第18位）：校验码，使用阿拉伯数字或英文字母表示。
        String str = "0123456789ABCDEFGHJKLMNPQRTUWXY";
        int[] ws = {1, 3, 9, 27, 19, 26, 16, 17, 20, 29, 25, 13, 8, 24, 10, 30, 28};
        String[] codes = new String[2];
        codes[0] = code.substring(0, code.length() - 1);
        codes[1] = code.substring(code.length() - 1, code.length());
        int sum = 0;
        for (int i = 0; i < 17; i++) {
            sum += str.indexOf(codes[0].charAt(i)) * ws[i];
        }
        int c18 = 31 - (sum % 31);
        if (c18 == 30) {
//            System.out.println("第18位 == 30");
            c18 = 'Y';
        } else if (c18 == 31) {
//            System.out.println("第18位 == 31");
            c18 = '0';
        }
        if (!codes[1].equals("" + c18)) {
//            System.out.println("社会信用代码有误！" + c18);
            return false;
        }
        return true;
    }

    /**
     * @param unifiedCreditCode：社会统一信用代码
     * @return boolean
     * @Description 社会统一信用代码校验
     * <AUTHOR>
     * @date 2019年11月14日 下午4:49:03
     */
    public static boolean p_checkUnifiedCreditCode(String unifiedCreditCode) {
        if (StringUtils.isEmpty(unifiedCreditCode))
            return false;
        // 统一代码由十八位的阿拉伯数字或大写英文字母（不使用I、O、Z、S、V）组成，
        // 包括第1位登记管理部门代码、
        // 第2位机构类别代码、
        // 第3位～第8位登记管理机关行政区划码、
        // 第9位～第17位主体标识码（组织机构代码）、
        // 第18位校验码五个部分。
        // 91110108MA00E1PU0C
        return UnifiedCreditCodeUtils.validateUnifiedCreditCode(unifiedCreditCode);
    }

    /**
     * 内部类
     *
     * <AUTHOR>
     * @Description 社会统一信用代码校验
     * @date 2019年11月15日 上午11:38:08
     */
    private static class UnifiedCreditCodeUtils {
        private static String baseCode = "0123456789ABCDEFGHJKLMNPQRTUWXY";
        private static char[] baseCodeArray = baseCode.toCharArray();
        private static int[] wi = {1, 3, 9, 27, 19, 26, 16, 17, 20, 29, 25, 13, 8, 24, 10, 30, 28};

        /**
         * @param unifiedCreditCode
         * @return boolean
         * @Description 社会统一信用代码校验
         * <AUTHOR>
         * @date 2019年11月14日 下午4:46:12
         */
        public static boolean validateUnifiedCreditCode(String unifiedCreditCode) {
            if ((unifiedCreditCode.equals("")) || unifiedCreditCode.length() != 18) {
                return false;
            }
            Map<Character, Integer> codes = generateCodes();
            int parityBit;
            try {
                parityBit = getParityBit(unifiedCreditCode, codes);
                if (parityBit != codes.get(unifiedCreditCode.charAt(unifiedCreditCode.length() - 1))) {
                    return false;
                }
            } catch (ValidationException e) {
                return false;
            }

            return true;
        }

        /**
         * 生成供较验使用的 Code Map
         *
         * @return BidiMap
         */
        private static BidiMap<Character, Integer> generateCodes() {
            BidiMap<Character, Integer> codes = new TreeBidiMap<>();
            for (int i = 0; i < baseCode.length(); i++) {
                codes.put(baseCodeArray[i], i);
            }
            return codes;
        }

        /**
         * @param unifiedCreditCode：统一社会信息代码
         * @param codes：带有映射关系的国家代码
         * @return int：获取较验位的值
         * @Description 获取校验码
         * <AUTHOR>
         * @date 2019年11月14日 下午4:46:34
         */
        private static int getParityBit(String unifiedCreditCode, Map<Character, Integer> codes) {
            char[] businessCodeArray = unifiedCreditCode.toCharArray();
            int sum = 0;
            for (int i = 0; i < 17; i++) {
                char key = businessCodeArray[i];
                if (baseCode.indexOf(key) == -1) {
                    throw new ValidationException("第" + String.valueOf(i + 1) + "位传入了非法的字符" + key);
                }
                sum += (codes.get(key) * wi[i]);
            }
            int result = 31 - sum % 31;
            return result == 31 ? 0 : result;
        }

        // /**
        // * @Description 获取一个随机的统一社会信用代码
        // * <AUTHOR>
        // * @date 2019年11月14日 下午4:47:08
        // * @return String：统一社会信用代码
        // */
        // public static String generateOneUnifiedCreditCode() {
        // Random random = new Random();
        // StringBuilder buf = new StringBuilder();
        // for (int i = 0; i < 17; ++i) {
        // int num = random.nextInt(baseCode.length() - 1);
        // buf.append(baseCode.charAt(num));
        // }
        // String code = buf.toString();
        // String upperCode = code.toUpperCase();
        // BidiMap<Character, Integer> codes = generateCodes();
        // int parityBit = getParityBit(upperCode, codes);
        // if (codes.getKey(parityBit) == null) {
        // System.out.println("生成社会统一信用代码不符合规则");
        // upperCode = generateOneUnifiedCreditCode();
        // } else {
        // upperCode = upperCode + codes.getKey(parityBit);
        // }
        // return upperCode;
        // }
    }

}
