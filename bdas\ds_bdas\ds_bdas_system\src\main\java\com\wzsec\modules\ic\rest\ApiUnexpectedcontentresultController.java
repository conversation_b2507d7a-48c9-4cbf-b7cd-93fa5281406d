package com.wzsec.modules.ic.rest;

import cn.hutool.core.lang.Console;
import com.wzsec.aop.log.Log;
import com.wzsec.modules.ic.domain.ApiUnexpectedcontentresult;
import com.wzsec.modules.ic.service.ApiUnexpectedcontentresultService;
import com.wzsec.modules.ic.service.dto.ApiUnexpectedcontentresultQueryCriteria;
import com.wzsec.utils.DomainUtil;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-05-19
 */
// @Api(tags = "接口出现非预期内容管理")
@RestController
@RequestMapping("/api/apiUnexpectedcontentresult")
public class ApiUnexpectedcontentresultController {

    private final ApiUnexpectedcontentresultService apiUnexpectedcontentresultService;

    public ApiUnexpectedcontentresultController(ApiUnexpectedcontentresultService apiUnexpectedcontentresultService) {
        this.apiUnexpectedcontentresultService = apiUnexpectedcontentresultService;
    }

    @Log("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, ApiUnexpectedcontentresultQueryCriteria criteria) throws IOException {
        try {
            Timestamp dateStart = criteria.getDateStart();
            Timestamp dateEnd = criteria.getDateEnd();
            List<String> checkTime = new ArrayList<>();
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateStart));
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateEnd));
            criteria.setChecktime(checkTime);
        } catch (Exception e) {
            Console.log("未填写查询时间");
        }

        // 根据申请部门名称分域
        List<String> domainApiCodeList = DomainUtil.queryDomainApiCode();
        criteria.setApicode(domainApiCodeList);

        apiUnexpectedcontentresultService.download(apiUnexpectedcontentresultService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询接口出现非预期内容")
    public ResponseEntity<Object> getApiUnexpectedcontentresults(ApiUnexpectedcontentresultQueryCriteria criteria, Pageable pageable) {
        try {
            Timestamp dateStart = criteria.getDateStart();
            Timestamp dateEnd = criteria.getDateEnd();
            List<String> checkTime = new ArrayList<>();
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateStart));
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateEnd));
            criteria.setChecktime(checkTime);
        } catch (Exception e) {
            Console.log("未填写查询时间");
        }

        // 根据申请部门名称分域
        List<String> domainApiCodeList = DomainUtil.queryDomainApiCode();
        criteria.setApicode(domainApiCodeList);

        return new ResponseEntity<>(apiUnexpectedcontentresultService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增接口出现非预期内容")
    public ResponseEntity<Object> create(@Validated @RequestBody ApiUnexpectedcontentresult resources) {
        return new ResponseEntity<>(apiUnexpectedcontentresultService.create(resources), HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改接口出现非预期内容")
    public ResponseEntity<Object> update(@Validated @RequestBody ApiUnexpectedcontentresult resources) {
        apiUnexpectedcontentresultService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除接口出现非预期内容")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        apiUnexpectedcontentresultService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
