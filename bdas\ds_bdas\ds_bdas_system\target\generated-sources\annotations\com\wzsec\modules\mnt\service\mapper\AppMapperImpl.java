package com.wzsec.modules.mnt.service.mapper;

import com.wzsec.modules.mnt.domain.App;
import com.wzsec.modules.mnt.service.dto.AppDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:28+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AppMapperImpl implements AppMapper {

    @Override
    public AppDto toDto(App entity) {
        if ( entity == null ) {
            return null;
        }

        AppDto appDto = new AppDto();

        appDto.setBackupPath( entity.getBackupPath() );
        appDto.setCreateTime( entity.getCreateTime() );
        appDto.setDeployPath( entity.getDeployPath() );
        appDto.setDeployScript( entity.getDeployScript() );
        appDto.setId( entity.getId() );
        appDto.setName( entity.getName() );
        appDto.setPort( entity.getPort() );
        appDto.setStartScript( entity.getStartScript() );
        appDto.setUploadPath( entity.getUploadPath() );

        return appDto;
    }

    @Override
    public List<AppDto> toDto(List<App> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<AppDto> list = new ArrayList<AppDto>( entityList.size() );
        for ( App app : entityList ) {
            list.add( toDto( app ) );
        }

        return list;
    }

    @Override
    public App toEntity(AppDto dto) {
        if ( dto == null ) {
            return null;
        }

        App app = new App();

        app.setBackupPath( dto.getBackupPath() );
        app.setCreateTime( dto.getCreateTime() );
        app.setDeployPath( dto.getDeployPath() );
        app.setDeployScript( dto.getDeployScript() );
        app.setId( dto.getId() );
        app.setName( dto.getName() );
        if ( dto.getPort() != null ) {
            app.setPort( dto.getPort() );
        }
        app.setStartScript( dto.getStartScript() );
        app.setUploadPath( dto.getUploadPath() );

        return app;
    }

    @Override
    public List<App> toEntity(List<AppDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<App> list = new ArrayList<App>( dtoList.size() );
        for ( AppDto appDto : dtoList ) {
            list.add( toEntity( appDto ) );
        }

        return list;
    }
}
