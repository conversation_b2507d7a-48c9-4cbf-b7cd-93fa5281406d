package com.wzsec.modules.sdd.discover.service.mapper;

import com.wzsec.modules.sdd.discover.domain.Task;
import com.wzsec.modules.sdd.discover.service.dto.TaskDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:27:07+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class TaskMapperImpl implements TaskMapper {

    @Override
    public TaskDto toDto(Task entity) {
        if ( entity == null ) {
            return null;
        }

        TaskDto taskDto = new TaskDto();

        taskDto.setCreatetime( entity.getCreatetime() );
        taskDto.setCreateuser( entity.getCreateuser() );
        taskDto.setCron( entity.getCron() );
        taskDto.setDatapath( entity.getDatapath() );
        taskDto.setDatasourceid( entity.getDatasourceid() );
        taskDto.setDatatype( entity.getDatatype() );
        taskDto.setExecutestate( entity.getExecutestate() );
        taskDto.setId( entity.getId() );
        taskDto.setNote( entity.getNote() );
        taskDto.setSparefield1( entity.getSparefield1() );
        taskDto.setSparefield2( entity.getSparefield2() );
        taskDto.setSparefield3( entity.getSparefield3() );
        taskDto.setSparefield4( entity.getSparefield4() );
        taskDto.setSparefield5( entity.getSparefield5() );
        taskDto.setState( entity.getState() );
        taskDto.setStrategyids( entity.getStrategyids() );
        taskDto.setSubmittype( entity.getSubmittype() );
        taskDto.setTaskname( entity.getTaskname() );
        taskDto.setUpdatetime( entity.getUpdatetime() );
        taskDto.setUpdateuser( entity.getUpdateuser() );

        return taskDto;
    }

    @Override
    public List<TaskDto> toDto(List<Task> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TaskDto> list = new ArrayList<TaskDto>( entityList.size() );
        for ( Task task : entityList ) {
            list.add( toDto( task ) );
        }

        return list;
    }

    @Override
    public Task toEntity(TaskDto dto) {
        if ( dto == null ) {
            return null;
        }

        Task task = new Task();

        task.setCreatetime( dto.getCreatetime() );
        task.setCreateuser( dto.getCreateuser() );
        task.setCron( dto.getCron() );
        task.setDatapath( dto.getDatapath() );
        task.setDatasourceid( dto.getDatasourceid() );
        task.setDatatype( dto.getDatatype() );
        task.setExecutestate( dto.getExecutestate() );
        task.setId( dto.getId() );
        task.setNote( dto.getNote() );
        task.setSparefield1( dto.getSparefield1() );
        task.setSparefield2( dto.getSparefield2() );
        task.setSparefield3( dto.getSparefield3() );
        task.setSparefield4( dto.getSparefield4() );
        task.setSparefield5( dto.getSparefield5() );
        task.setState( dto.getState() );
        task.setStrategyids( dto.getStrategyids() );
        task.setSubmittype( dto.getSubmittype() );
        task.setTaskname( dto.getTaskname() );
        task.setUpdatetime( dto.getUpdatetime() );
        task.setUpdateuser( dto.getUpdateuser() );

        return task;
    }

    @Override
    public List<Task> toEntity(List<TaskDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Task> list = new ArrayList<Task>( dtoList.size() );
        for ( TaskDto taskDto : dtoList ) {
            list.add( toEntity( taskDto ) );
        }

        return list;
    }
}
