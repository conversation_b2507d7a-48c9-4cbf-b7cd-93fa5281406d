package com.wzsec.modules.ic.rest;

import cn.hutool.core.lang.Console;
import com.wzsec.aop.log.Log;
import com.wzsec.modules.ic.domain.ApiInvolvesaccountpassword;
import com.wzsec.modules.ic.service.ApiInvolvesaccountpasswordService;
import com.wzsec.modules.ic.service.dto.ApiInvolvesaccountpasswordQueryCriteria;
import com.wzsec.utils.DomainUtil;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-12-28
 */
// @Api(tags = "接口涉及账号密码管理")
@RestController
@RequestMapping("/api/apiInvolvesaccountpassword")
public class ApiInvolvesaccountpasswordController {

    private final ApiInvolvesaccountpasswordService apiInvolvesaccountpasswordService;

    public ApiInvolvesaccountpasswordController(ApiInvolvesaccountpasswordService apiInvolvesaccountpasswordService) {
        this.apiInvolvesaccountpasswordService = apiInvolvesaccountpasswordService;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
//   // @PreAuthorize("@el.check('apiInvolvesaccountpassword:list')")
    public void download(HttpServletResponse response, ApiInvolvesaccountpasswordQueryCriteria criteria) throws IOException {
        try {
            Timestamp dateStart = criteria.getDateStart();
            Timestamp dateEnd = criteria.getDateEnd();
            List<String> checkTime = new ArrayList<>();
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateStart));
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateEnd));
            criteria.setChecktime(checkTime);
        } catch (Exception e) {
            Console.log("未填写查询时间");
        }

        // 根据申请部门名称分域
        List<String> domainAkList = DomainUtil.queryDomainAk();
        criteria.setAk(domainAkList);

        apiInvolvesaccountpasswordService.download(apiInvolvesaccountpasswordService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询接口涉及账号密码")
    // @ApiOperation("查询接口涉及账号密码")
//   // @PreAuthorize("@el.check('apiInvolvesaccountpassword:list')")
    public ResponseEntity<Object> getApiInvolvesaccountpasswords(ApiInvolvesaccountpasswordQueryCriteria criteria, Pageable pageable) {
        try {
            Timestamp dateStart = criteria.getDateStart();
            Timestamp dateEnd = criteria.getDateEnd();
            List<String> checkTime = new ArrayList<>();
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateStart));
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateEnd));
            criteria.setChecktime(checkTime);
        } catch (Exception e) {
            Console.log("未填写查询时间");
        }

        // 根据申请部门名称分域
        List<String> domainAkList = DomainUtil.queryDomainAk();
        criteria.setAk(domainAkList);

        return new ResponseEntity<>(apiInvolvesaccountpasswordService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增接口涉及账号密码")
    // @ApiOperation("新增接口涉及账号密码")
//   // @PreAuthorize("@el.check('apiInvolvesaccountpassword:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody ApiInvolvesaccountpassword resources) {
        return new ResponseEntity<>(apiInvolvesaccountpasswordService.create(resources), HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改接口涉及账号密码")
    // @ApiOperation("修改接口涉及账号密码")
//   // @PreAuthorize("@el.check('apiInvolvesaccountpassword:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody ApiInvolvesaccountpassword resources) {
        apiInvolvesaccountpasswordService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除接口涉及账号密码")
    // @ApiOperation("删除接口涉及账号密码")
//   // @PreAuthorize("@el.check('apiInvolvesaccountpassword:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        apiInvolvesaccountpasswordService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
