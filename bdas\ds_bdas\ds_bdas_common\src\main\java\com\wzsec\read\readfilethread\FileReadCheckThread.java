package com.wzsec.read.readfilethread;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 * @ClassName: FileReadCheckThread
 * @Description: 文件处理线程类
 * @date 2019年7月31日
 */
public class FileReadCheckThread {

    private final static Logger logger = LoggerFactory.getLogger(FileReadCheckThread.class);

    /**
     * 文件处理初始化，多线程处理文件
     *
     * @param filePath：文件夹地址
     * @param currentThreadSum：线程数量、一次文件读取数量、过多导致内存溢出
     * @param service：文件处理接口
     * @param params：参数集合
     * <AUTHOR>
     * @date 2019年7月31日
     */
    public void fileCkeckThreadInit(String filePath, List<String> fileTypes, int currentThreadSum,
                                    FileProcessHandler service, Map<String, Object> params) throws Exception {
        try {
            List<String> fileNameList = new ArrayList<String>();
            getAllFileName(filePath, fileNameList, fileTypes);// 获取目录下所有指定类型文件
            // System.out.println(new Gson().toJson(fileNameList));
            int size = fileNameList.size();
            if (size == 0) {
                StringBuffer fileTypeSB = new StringBuffer();
                for (String fileType : fileTypes) {
                    fileTypeSB.append(fileType).append("、");
                }
                logger.info(fileTypeSB.toString() + "等类型文件数量为0，跳过多线程检测，走主线程检测。");
                return;
            }
            logger.info("【主线程】读取文件线程start。。。");
            logger.info("【主线程】额定线程数量：" + currentThreadSum + " (同时检测文件数量),执行类：" + service.getClass().getName());
            long startTime = System.currentTimeMillis();// 开始执行时间
            CountDownLatch countDownLatchAll = new CountDownLatch(size);// 用于告知所有线程执行完毕
            int openThreadNum = 0;
            for (int i = 0; i < size; i++) {// 当一个线程结束，再开启新线程，始终保持满线程运行
                int activeThreadNum = getActiveThreadSum(this.getClass().getSimpleName());// 当前活动线程数
                int canOpenThreadNum = 0;
                CountDownLatch countDownLatch = new CountDownLatch(1);// 用于告知有空闲线程
                if (activeThreadNum > 0) {// 有活动线程，需判断可开启线程数
                    canOpenThreadNum = currentThreadSum - activeThreadNum;// 可开启线程数
                } else if (activeThreadNum == 0) {// 所有通道均空闲
                    canOpenThreadNum = currentThreadSum;
                }
                if (canOpenThreadNum == 0) {
                    logger.info("【主线程】额定线程数量：" + currentThreadSum + "，正在运行线程数量：" + activeThreadNum + "，可开启线程数量："
                            + canOpenThreadNum + "，跳过开启新线程");
                    i--;// 倒退循环
                    continue;
                }
                logger.info("【主线程】目录" + filePath + "下文件数量：" + size + "，已检测文件数量：" + openThreadNum + "，需开启总线程数量：" + size
                        + "，已开启线程数量：" + openThreadNum + "，未开启线程数量：" + (size - openThreadNum));
                StringBuffer threadNames = new StringBuffer();
                int needOpenThreadNum = canOpenThreadNum;
                if (i + canOpenThreadNum >= size) {// 最后一批未检测数量不足以支撑每个空闲线程都开启时
                    needOpenThreadNum = size - openThreadNum;
                }
                logger.info("【主线程】额定线程数量：" + currentThreadSum + "，正在运行线程数量：" + activeThreadNum + "，可开启线程数量："
                        + canOpenThreadNum + "，实际开启线程数量：" + needOpenThreadNum);
                for (int j = 0; j < needOpenThreadNum; j++) {
                    String fileName = fileNameList.get(i + j);// 当前需检测文件
                    openThreadNum++;// 记录已开启线程数
                    Runnable task = () -> service.exec(fileName, params, countDownLatch, countDownLatchAll);
                    Thread thread = new Thread(task);
                    thread.setName(this.getClass().getSimpleName() + "-" + service.getClass().getSimpleName() + "-" + params.get("taskNumber") + "-" + openThreadNum);
                    thread.start();
                    threadNames.append("，线程[").append(thread.getName()).append("]已开启");
                }
                if (needOpenThreadNum > 0) {
                    i += (needOpenThreadNum - 1);// 记录已检测文件数
                }
                logger.info("【主线程】成功开启 " + needOpenThreadNum + "个线程" + threadNames);
                try {
                    logger.info("【主线程】正在等待任一线程执行完毕。。。");
                    countDownLatch.await();// 等待N个线程执行后再继续执行，配合countDownLatch.countDown()
                    if (openThreadNum < size) {// 非最后一个线程需等待有空闲线程
                        logger.info("【主线程】有空闲线程，可开启新线程。");
                    } else {
                        logger.info("【主线程】所有线程已开启，无文件需要做处理。");
                    }
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
            try {
                logger.info("【主线程】正在等待所有线程执行完毕。。。");
                countDownLatchAll.await();// 等待所有线程执行完毕
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            long endTime = System.currentTimeMillis();// 结束执行时间
            logger.info("【主线程】任务结束，总耗时：" + (endTime - startTime) / 1000 + "s(秒)");
            logger.info("【主线程】读取文件线程end。。。");
        } catch (Exception e) {
            throw e;
        }

    }

    /**
     * @param threadName：根据名字获取指定类型线程
     * @return int：线程数量
     * @Description：获取指定类型线程数量
     * <AUTHOR>
     * @date 2019年12月27日 下午5:33:03
     */
    public int getActiveThreadSum(String threadName) {
        int threadSum = 0;
        ThreadGroup currentGroup = Thread.currentThread().getThreadGroup();
        Thread[] lstThreads = new Thread[currentGroup.activeCount()];
        currentGroup.enumerate(lstThreads);
        for (Thread thread : lstThreads) {
            if (thread.getName().contains(threadName)) {// 对线程根据线程名进行过滤
                threadSum++;
            }
        }
        return threadSum;
    }

    /**
     * 获取文件夹下所有文件名称（包含子目录）
     *
     * @param path
     * @param fileNameList
     * <AUTHOR>
     * @date 2019年7月31日
     */
    public static void getAllFileName(String path, List<String> fileNameList, List<String> fileTypes) {
        File file = new File(path);
        File[] files = null;
        if (file.isDirectory()) {
            files = file.listFiles();
        }
        if (files != null) {
            for (File f : files) {
                if (f.isFile()) {
                    String fileName = f.getName();
                    for (String fileType : fileTypes) {
                        if (fileName.endsWith(fileType)) {
                            fileNameList.add(f.getAbsolutePath());
                            break;
                        }
                    }
                }
            }
        }
    }
}
