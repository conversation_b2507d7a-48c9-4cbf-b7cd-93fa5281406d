package com.wzsec.modules.ic.rest;

import cn.hutool.core.lang.Console;
import com.wzsec.aop.log.Log;
import com.wzsec.modules.ic.domain.IcResult;
import com.wzsec.modules.ic.service.IcResultService;
import com.wzsec.modules.ic.service.dto.IcResultQueryCriteria;
import com.wzsec.utils.DomainUtil;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

// import io.swagger.annotations.*;

/**
 * <AUTHOR>
 * @date 2021-04-26
 */
// @Api(tags = "接口日志概要结果管理")
@RestController
@RequestMapping("/api/icResult")
public class IcResultController {

    private final IcResultService icResultService;

    public IcResultController(IcResultService icResultService) {
        this.icResultService = icResultService;
    }

    @Log("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, IcResultQueryCriteria criteria) throws IOException {
        try {
            Timestamp dateStart = criteria.getDateStart();
            Timestamp dateEnd = criteria.getDateEnd();
            List<String> checkTime = new ArrayList<>();
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateStart));
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateEnd));
            criteria.setSparefield4(checkTime);
        } catch (Exception e) {
            Console.log("未填写查询时间");
        }

        // 根据申请部门名称分域
        List<String> domainAkList = DomainUtil.queryDomainAk();
        criteria.setAk(domainAkList);

        icResultService.download(icResultService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询接口日志概要结果")
    public ResponseEntity<Object> getIcResults(IcResultQueryCriteria criteria, Pageable pageable) {
        try {
            Timestamp dateStart = criteria.getDateStart();
            Timestamp dateEnd = criteria.getDateEnd();
            List<String> checkTime = new ArrayList<>();
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateStart));
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateEnd));
            criteria.setSparefield4(checkTime);
        } catch (Exception e) {
            Console.log("未填写查询时间");
        }

        // 根据申请部门名称分域
        List<String> domainAkList = DomainUtil.queryDomainAk();
        criteria.setAk(domainAkList);

        return new ResponseEntity<>(icResultService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增接口日志概要结果")
    public ResponseEntity<Object> create(@Validated @RequestBody IcResult resources) {
        return new ResponseEntity<>(icResultService.create(resources), HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改接口日志概要结果")
    public ResponseEntity<Object> update(@Validated @RequestBody IcResult resources) {
        icResultService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除接口日志概要结果")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        icResultService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
