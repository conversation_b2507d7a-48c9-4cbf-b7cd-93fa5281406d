package com.wzsec.modules.z.service.mapper;

import com.wzsec.modules.z.domain.ZSensitivedata;
import com.wzsec.modules.z.service.dto.ZSensitivedataDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:31+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ZSensitivedataMapperImpl implements ZSensitivedataMapper {

    @Override
    public ZSensitivedataDto toDto(ZSensitivedata entity) {
        if ( entity == null ) {
            return null;
        }

        ZSensitivedataDto zSensitivedataDto = new ZSensitivedataDto();

        zSensitivedataDto.setCreatetime( entity.getCreatetime() );
        zSensitivedataDto.setCreateuser( entity.getCreateuser() );
        zSensitivedataDto.setId( entity.getId() );
        zSensitivedataDto.setRemark( entity.getRemark() );
        zSensitivedataDto.setSensitivedata( entity.getSensitivedata() );
        zSensitivedataDto.setSensitivedataename( entity.getSensitivedataename() );
        zSensitivedataDto.setSensitivedatarule( entity.getSensitivedatarule() );
        zSensitivedataDto.setSensitivelevel( entity.getSensitivelevel() );
        zSensitivedataDto.setSparefield1( entity.getSparefield1() );
        zSensitivedataDto.setSparefield2( entity.getSparefield2() );
        zSensitivedataDto.setSparefield3( entity.getSparefield3() );
        zSensitivedataDto.setSparefield4( entity.getSparefield4() );
        zSensitivedataDto.setTestscenario( entity.getTestscenario() );
        zSensitivedataDto.setUpdatetime( entity.getUpdatetime() );
        zSensitivedataDto.setUpdateuser( entity.getUpdateuser() );

        return zSensitivedataDto;
    }

    @Override
    public List<ZSensitivedataDto> toDto(List<ZSensitivedata> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ZSensitivedataDto> list = new ArrayList<ZSensitivedataDto>( entityList.size() );
        for ( ZSensitivedata zSensitivedata : entityList ) {
            list.add( toDto( zSensitivedata ) );
        }

        return list;
    }

    @Override
    public ZSensitivedata toEntity(ZSensitivedataDto dto) {
        if ( dto == null ) {
            return null;
        }

        ZSensitivedata zSensitivedata = new ZSensitivedata();

        zSensitivedata.setCreatetime( dto.getCreatetime() );
        zSensitivedata.setCreateuser( dto.getCreateuser() );
        zSensitivedata.setId( dto.getId() );
        zSensitivedata.setRemark( dto.getRemark() );
        zSensitivedata.setSensitivedata( dto.getSensitivedata() );
        zSensitivedata.setSensitivedataename( dto.getSensitivedataename() );
        zSensitivedata.setSensitivedatarule( dto.getSensitivedatarule() );
        zSensitivedata.setSensitivelevel( dto.getSensitivelevel() );
        zSensitivedata.setSparefield1( dto.getSparefield1() );
        zSensitivedata.setSparefield2( dto.getSparefield2() );
        zSensitivedata.setSparefield3( dto.getSparefield3() );
        zSensitivedata.setSparefield4( dto.getSparefield4() );
        zSensitivedata.setTestscenario( dto.getTestscenario() );
        zSensitivedata.setUpdatetime( dto.getUpdatetime() );
        zSensitivedata.setUpdateuser( dto.getUpdateuser() );

        return zSensitivedata;
    }

    @Override
    public List<ZSensitivedata> toEntity(List<ZSensitivedataDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ZSensitivedata> list = new ArrayList<ZSensitivedata>( dtoList.size() );
        for ( ZSensitivedataDto zSensitivedataDto : dtoList ) {
            list.add( toEntity( zSensitivedataDto ) );
        }

        return list;
    }
}
