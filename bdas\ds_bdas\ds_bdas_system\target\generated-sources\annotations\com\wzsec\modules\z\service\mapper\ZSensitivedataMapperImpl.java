package com.wzsec.modules.z.service.mapper;

import com.wzsec.modules.z.domain.ZSensitivedata;
import com.wzsec.modules.z.service.dto.ZSensitivedataDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:30+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ZSensitivedataMapperImpl implements ZSensitivedataMapper {

    @Override
    public ZSensitivedata toEntity(ZSensitivedataDto dto) {
        if ( dto == null ) {
            return null;
        }

        ZSensitivedata zSensitivedata = new ZSensitivedata();

        zSensitivedata.setId( dto.getId() );
        zSensitivedata.setSensitivedata( dto.getSensitivedata() );
        zSensitivedata.setSensitivedataename( dto.getSensitivedataename() );
        zSensitivedata.setSensitivelevel( dto.getSensitivelevel() );
        zSensitivedata.setSensitivedatarule( dto.getSensitivedatarule() );
        zSensitivedata.setTestscenario( dto.getTestscenario() );
        zSensitivedata.setRemark( dto.getRemark() );
        zSensitivedata.setCreateuser( dto.getCreateuser() );
        zSensitivedata.setCreatetime( dto.getCreatetime() );
        zSensitivedata.setUpdateuser( dto.getUpdateuser() );
        zSensitivedata.setUpdatetime( dto.getUpdatetime() );
        zSensitivedata.setSparefield1( dto.getSparefield1() );
        zSensitivedata.setSparefield2( dto.getSparefield2() );
        zSensitivedata.setSparefield3( dto.getSparefield3() );
        zSensitivedata.setSparefield4( dto.getSparefield4() );

        return zSensitivedata;
    }

    @Override
    public ZSensitivedataDto toDto(ZSensitivedata entity) {
        if ( entity == null ) {
            return null;
        }

        ZSensitivedataDto zSensitivedataDto = new ZSensitivedataDto();

        zSensitivedataDto.setId( entity.getId() );
        zSensitivedataDto.setSensitivedata( entity.getSensitivedata() );
        zSensitivedataDto.setSensitivedataename( entity.getSensitivedataename() );
        zSensitivedataDto.setSensitivelevel( entity.getSensitivelevel() );
        zSensitivedataDto.setSensitivedatarule( entity.getSensitivedatarule() );
        zSensitivedataDto.setTestscenario( entity.getTestscenario() );
        zSensitivedataDto.setRemark( entity.getRemark() );
        zSensitivedataDto.setCreateuser( entity.getCreateuser() );
        zSensitivedataDto.setCreatetime( entity.getCreatetime() );
        zSensitivedataDto.setUpdateuser( entity.getUpdateuser() );
        zSensitivedataDto.setUpdatetime( entity.getUpdatetime() );
        zSensitivedataDto.setSparefield1( entity.getSparefield1() );
        zSensitivedataDto.setSparefield2( entity.getSparefield2() );
        zSensitivedataDto.setSparefield3( entity.getSparefield3() );
        zSensitivedataDto.setSparefield4( entity.getSparefield4() );

        return zSensitivedataDto;
    }

    @Override
    public List<ZSensitivedata> toEntity(List<ZSensitivedataDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ZSensitivedata> list = new ArrayList<ZSensitivedata>( dtoList.size() );
        for ( ZSensitivedataDto zSensitivedataDto : dtoList ) {
            list.add( toEntity( zSensitivedataDto ) );
        }

        return list;
    }

    @Override
    public List<ZSensitivedataDto> toDto(List<ZSensitivedata> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ZSensitivedataDto> list = new ArrayList<ZSensitivedataDto>( entityList.size() );
        for ( ZSensitivedata zSensitivedata : entityList ) {
            list.add( toDto( zSensitivedata ) );
        }

        return list;
    }
}
