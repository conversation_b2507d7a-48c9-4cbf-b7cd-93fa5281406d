package com.wzsec.modules.ic.service.dto;

import com.wzsec.annotation.Query;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-04-07
 */
@Data
public class ApprovaldetectionQueryCriteria {

    /** 支持精确查询和IN查询：单个值时精确查询，逗号分隔字符串或List时IN查询 */
    @Query(type = Query.Type.IN)
    private Object ak;

    @Query
    private String risk;

    @Query(blurry = "ak,sk,martsupplyorgname,name,applyorgname,applytime,sysname,shareapplyid,apicode,apiname,resourcename,actualoutput,applyoutput")
    private String blurry;
}
