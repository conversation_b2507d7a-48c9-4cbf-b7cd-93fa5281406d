package com.wzsec.utils.file;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

/**
 * txt文件类
 *
 * <AUTHOR>
 * @date 2020-4-26
 */
public class TxtUtil {

    /**
     * 读取文本内容(指定行数)
     * @param filePath 文件地址
     * @return
     * <AUTHOR>
     * @date 2020-4-26
     */
    public static String getTxtExtractStr(String filePath, List<Integer> ExtractLineNumList) {
        //ArrayList<String> datalinelist = new ArrayList<>();
        File file = new File(filePath);
        StringBuffer txtStr = new StringBuffer();
        BufferedReader reader = null;
        int count = 1;
        String temp = null;
        try {
            reader = new BufferedReader(new InputStreamReader(new FileInputStream(file), getFileCharset(file)));
            while ((temp = reader.readLine()) != null) {
//                if(ExtractLineNumList.contains(count)){
//                    txtStr.append(temp).append("\n");
//                }
//                datalinelist.add(temp);
                txtStr.append(temp).append("\n");
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return txtStr.toString();
    }

    /**
     * 读取文本内容
     * @param filePath 文件地址
     * @return
     * <AUTHOR>
     * @date 2020-4-26
     */
    public static String getTxtStr(String filePath) {
        File file = new File(filePath);
        StringBuffer txtStr = new StringBuffer();
        BufferedReader reader = null;
        String temp = null;
        try {
            reader = new BufferedReader(new InputStreamReader(new FileInputStream(file), getFileCharset(file)));
            while ((temp = reader.readLine()) != null) {
                txtStr.append(temp).append("\n");
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return txtStr.toString();
    }

    /**
     * @param sourceFile
     * @return String
     * @Description 判断txt编码格式方法
     * <AUTHOR>
     * @date 2020年2月27日
     */
    public static String getFileCharset(File sourceFile) {
        String charset = "GBK";
        byte[] first3Bytes = new byte[3];
        try {
            boolean checked = false;
            BufferedInputStream bis = new BufferedInputStream(new FileInputStream(sourceFile));
            bis.mark(0);
            int read = bis.read(first3Bytes, 0, 3);
            if (read == -1) {
                return charset; // 文件编码为 ANSI
            } else if (first3Bytes[0] == (byte) 0xFF && first3Bytes[1] == (byte) 0xFE) {
                charset = "UTF-16LE"; // 文件编码为 Unicode
                checked = true;
            } else if (first3Bytes[0] == (byte) 0xFE && first3Bytes[1] == (byte) 0xFF) {
                charset = "UTF-16BE"; // 文件编码为 Unicode big endian
                checked = true;
            } else if (first3Bytes[0] == (byte) 0xEF && first3Bytes[1] == (byte) 0xBB
                    && first3Bytes[2] == (byte) 0xBF) {
                charset = "UTF-8"; // 文件编码为 UTF-8
                checked = true;
            }
            bis.reset();
            if (!checked) {
                int loc = 0;
                while ((read = bis.read()) != -1) {
                    loc++;
                    if (read >= 0xF0)
                        break;
                    if (0x80 <= read && read <= 0xBF) // 单独出现BF以下的，也算是GBK
                        break;
                    if (0xC0 <= read && read <= 0xDF) {
                        read = bis.read();
                        if (0x80 <= read && read <= 0xBF) // 双字节 (0xC0 - 0xDF)
                            // (0x80
                            // - 0xBF),也可能在GB编码内
                            continue;
                        else
                            break;
                    } else if (0xE0 <= read && read <= 0xEF) {// 也有可能出错，但是几率较小
                        read = bis.read();
                        if (0x80 <= read && read <= 0xBF) {
                            read = bis.read();
                            if (0x80 <= read && read <= 0xBF) {
                                charset = "UTF-8";
                                break;
                            } else
                                break;
                        } else
                            break;
                    }
                }
            }
            bis.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return charset;
    }
}
