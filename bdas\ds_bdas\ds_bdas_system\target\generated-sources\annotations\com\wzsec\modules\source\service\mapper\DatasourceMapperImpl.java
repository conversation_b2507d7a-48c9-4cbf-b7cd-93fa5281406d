package com.wzsec.modules.source.service.mapper;

import com.wzsec.modules.source.domain.Datasource;
import com.wzsec.modules.source.service.dto.DatasourceDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:31+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class DatasourceMapperImpl implements DatasourceMapper {

    @Override
    public Datasource toEntity(DatasourceDto dto) {
        if ( dto == null ) {
            return null;
        }

        Datasource datasource = new Datasource();

        datasource.setId( dto.getId() );
        datasource.setSrcname( dto.getSrcname() );
        datasource.setType( dto.getType() );
        datasource.setClientcharset( dto.getClientcharset() );
        datasource.setDbcharset( dto.getDbcharset() );
        datasource.setDriverprogram( dto.getDriverprogram() );
        datasource.setSrcurl( dto.getSrcurl() );
        datasource.setDbname( dto.getDbname() );
        datasource.setUsername( dto.getUsername() );
        datasource.setPassword( dto.getPassword() );
        datasource.setIsvalid( dto.getIsvalid() );
        datasource.setNote( dto.getNote() );
        datasource.setCreateuser( dto.getCreateuser() );
        datasource.setCreatetime( dto.getCreatetime() );
        datasource.setUpdateuser( dto.getUpdateuser() );
        datasource.setUpdatetime( dto.getUpdatetime() );
        datasource.setSparefield1( dto.getSparefield1() );
        datasource.setSparefield2( dto.getSparefield2() );
        datasource.setSparefield3( dto.getSparefield3() );
        datasource.setSparefield4( dto.getSparefield4() );
        datasource.setSparefield5( dto.getSparefield5() );

        return datasource;
    }

    @Override
    public DatasourceDto toDto(Datasource entity) {
        if ( entity == null ) {
            return null;
        }

        DatasourceDto datasourceDto = new DatasourceDto();

        datasourceDto.setId( entity.getId() );
        datasourceDto.setSrcname( entity.getSrcname() );
        datasourceDto.setType( entity.getType() );
        datasourceDto.setClientcharset( entity.getClientcharset() );
        datasourceDto.setDbcharset( entity.getDbcharset() );
        datasourceDto.setDriverprogram( entity.getDriverprogram() );
        datasourceDto.setSrcurl( entity.getSrcurl() );
        datasourceDto.setDbname( entity.getDbname() );
        datasourceDto.setUsername( entity.getUsername() );
        datasourceDto.setPassword( entity.getPassword() );
        datasourceDto.setIsvalid( entity.getIsvalid() );
        datasourceDto.setNote( entity.getNote() );
        datasourceDto.setCreateuser( entity.getCreateuser() );
        datasourceDto.setCreatetime( entity.getCreatetime() );
        datasourceDto.setUpdateuser( entity.getUpdateuser() );
        datasourceDto.setUpdatetime( entity.getUpdatetime() );
        datasourceDto.setSparefield1( entity.getSparefield1() );
        datasourceDto.setSparefield2( entity.getSparefield2() );
        datasourceDto.setSparefield3( entity.getSparefield3() );
        datasourceDto.setSparefield4( entity.getSparefield4() );
        datasourceDto.setSparefield5( entity.getSparefield5() );

        return datasourceDto;
    }

    @Override
    public List<Datasource> toEntity(List<DatasourceDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Datasource> list = new ArrayList<Datasource>( dtoList.size() );
        for ( DatasourceDto datasourceDto : dtoList ) {
            list.add( toEntity( datasourceDto ) );
        }

        return list;
    }

    @Override
    public List<DatasourceDto> toDto(List<Datasource> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<DatasourceDto> list = new ArrayList<DatasourceDto>( entityList.size() );
        for ( Datasource datasource : entityList ) {
            list.add( toDto( datasource ) );
        }

        return list;
    }
}
