package com.wzsec.modules.z.service.mapper;

import com.wzsec.modules.z.domain.ZDeviceconfig;
import com.wzsec.modules.z.service.dto.ZDeviceconfigDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:31+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ZDeviceconfigMapperImpl implements ZDeviceconfigMapper {

    @Override
    public ZDeviceconfig toEntity(ZDeviceconfigDto dto) {
        if ( dto == null ) {
            return null;
        }

        ZDeviceconfig zDeviceconfig = new ZDeviceconfig();

        zDeviceconfig.setId( dto.getId() );
        zDeviceconfig.setHostname( dto.getHostname() );
        zDeviceconfig.setIp( dto.getIp() );
        zDeviceconfig.setUsername( dto.getUsername() );
        zDeviceconfig.setPassword( dto.getPassword() );
        zDeviceconfig.setFilepath( dto.getFilepath() );
        zDeviceconfig.setType( dto.getType() );
        zDeviceconfig.setPollingstatus( dto.getPollingstatus() );
        zDeviceconfig.setSparefield1( dto.getSparefield1() );
        zDeviceconfig.setSparefield2( dto.getSparefield2() );

        return zDeviceconfig;
    }

    @Override
    public ZDeviceconfigDto toDto(ZDeviceconfig entity) {
        if ( entity == null ) {
            return null;
        }

        ZDeviceconfigDto zDeviceconfigDto = new ZDeviceconfigDto();

        zDeviceconfigDto.setId( entity.getId() );
        zDeviceconfigDto.setHostname( entity.getHostname() );
        zDeviceconfigDto.setIp( entity.getIp() );
        zDeviceconfigDto.setUsername( entity.getUsername() );
        zDeviceconfigDto.setPassword( entity.getPassword() );
        zDeviceconfigDto.setFilepath( entity.getFilepath() );
        zDeviceconfigDto.setType( entity.getType() );
        zDeviceconfigDto.setPollingstatus( entity.getPollingstatus() );
        zDeviceconfigDto.setSparefield1( entity.getSparefield1() );
        zDeviceconfigDto.setSparefield2( entity.getSparefield2() );

        return zDeviceconfigDto;
    }

    @Override
    public List<ZDeviceconfig> toEntity(List<ZDeviceconfigDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ZDeviceconfig> list = new ArrayList<ZDeviceconfig>( dtoList.size() );
        for ( ZDeviceconfigDto zDeviceconfigDto : dtoList ) {
            list.add( toEntity( zDeviceconfigDto ) );
        }

        return list;
    }

    @Override
    public List<ZDeviceconfigDto> toDto(List<ZDeviceconfig> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ZDeviceconfigDto> list = new ArrayList<ZDeviceconfigDto>( entityList.size() );
        for ( ZDeviceconfig zDeviceconfig : entityList ) {
            list.add( toDto( zDeviceconfig ) );
        }

        return list;
    }
}
