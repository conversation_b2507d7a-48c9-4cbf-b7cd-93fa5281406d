package com.wzsec.modules.z.service.mapper;

import com.wzsec.modules.z.domain.ZDeviceconfig;
import com.wzsec.modules.z.service.dto.ZDeviceconfigDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:29+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ZDeviceconfigMapperImpl implements ZDeviceconfigMapper {

    @Override
    public ZDeviceconfigDto toDto(ZDeviceconfig entity) {
        if ( entity == null ) {
            return null;
        }

        ZDeviceconfigDto zDeviceconfigDto = new ZDeviceconfigDto();

        zDeviceconfigDto.setFilepath( entity.getFilepath() );
        zDeviceconfigDto.setHostname( entity.getHostname() );
        zDeviceconfigDto.setId( entity.getId() );
        zDeviceconfigDto.setIp( entity.getIp() );
        zDeviceconfigDto.setPassword( entity.getPassword() );
        zDeviceconfigDto.setPollingstatus( entity.getPollingstatus() );
        zDeviceconfigDto.setSparefield1( entity.getSparefield1() );
        zDeviceconfigDto.setSparefield2( entity.getSparefield2() );
        zDeviceconfigDto.setType( entity.getType() );
        zDeviceconfigDto.setUsername( entity.getUsername() );

        return zDeviceconfigDto;
    }

    @Override
    public List<ZDeviceconfigDto> toDto(List<ZDeviceconfig> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ZDeviceconfigDto> list = new ArrayList<ZDeviceconfigDto>( entityList.size() );
        for ( ZDeviceconfig zDeviceconfig : entityList ) {
            list.add( toDto( zDeviceconfig ) );
        }

        return list;
    }

    @Override
    public ZDeviceconfig toEntity(ZDeviceconfigDto dto) {
        if ( dto == null ) {
            return null;
        }

        ZDeviceconfig zDeviceconfig = new ZDeviceconfig();

        zDeviceconfig.setFilepath( dto.getFilepath() );
        zDeviceconfig.setHostname( dto.getHostname() );
        zDeviceconfig.setId( dto.getId() );
        zDeviceconfig.setIp( dto.getIp() );
        zDeviceconfig.setPassword( dto.getPassword() );
        zDeviceconfig.setPollingstatus( dto.getPollingstatus() );
        zDeviceconfig.setSparefield1( dto.getSparefield1() );
        zDeviceconfig.setSparefield2( dto.getSparefield2() );
        zDeviceconfig.setType( dto.getType() );
        zDeviceconfig.setUsername( dto.getUsername() );

        return zDeviceconfig;
    }

    @Override
    public List<ZDeviceconfig> toEntity(List<ZDeviceconfigDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ZDeviceconfig> list = new ArrayList<ZDeviceconfig>( dtoList.size() );
        for ( ZDeviceconfigDto zDeviceconfigDto : dtoList ) {
            list.add( toEntity( zDeviceconfigDto ) );
        }

        return list;
    }
}
