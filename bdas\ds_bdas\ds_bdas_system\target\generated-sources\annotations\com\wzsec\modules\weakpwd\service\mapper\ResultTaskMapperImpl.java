package com.wzsec.modules.weakpwd.service.mapper;

import com.wzsec.modules.weakpwd.domain.ResultTask;
import com.wzsec.modules.weakpwd.service.dto.ResultTaskDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:29+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ResultTaskMapperImpl implements ResultTaskMapper {

    @Override
    public ResultTaskDto toDto(ResultTask entity) {
        if ( entity == null ) {
            return null;
        }

        ResultTaskDto resultTaskDto = new ResultTaskDto();

        resultTaskDto.setCreatetime( entity.getCreatetime() );
        resultTaskDto.setDbcount( entity.getDbcount() );
        resultTaskDto.setEndtime( entity.getEndtime() );
        resultTaskDto.setId( entity.getId() );
        resultTaskDto.setSparefield1( entity.getSparefield1() );
        resultTaskDto.setSparefield2( entity.getSparefield2() );
        resultTaskDto.setSparefield3( entity.getSparefield3() );
        resultTaskDto.setSparefield4( entity.getSparefield4() );
        resultTaskDto.setSparefield5( entity.getSparefield5() );
        resultTaskDto.setStarttime( entity.getStarttime() );
        resultTaskDto.setState( entity.getState() );
        resultTaskDto.setSubmituser( entity.getSubmituser() );
        resultTaskDto.setTaskno( entity.getTaskno() );
        resultTaskDto.setUsetime( entity.getUsetime() );
        resultTaskDto.setWpcount( entity.getWpcount() );
        resultTaskDto.setWpdbcount( entity.getWpdbcount() );

        return resultTaskDto;
    }

    @Override
    public List<ResultTaskDto> toDto(List<ResultTask> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ResultTaskDto> list = new ArrayList<ResultTaskDto>( entityList.size() );
        for ( ResultTask resultTask : entityList ) {
            list.add( toDto( resultTask ) );
        }

        return list;
    }

    @Override
    public ResultTask toEntity(ResultTaskDto dto) {
        if ( dto == null ) {
            return null;
        }

        ResultTask resultTask = new ResultTask();

        resultTask.setCreatetime( dto.getCreatetime() );
        resultTask.setDbcount( dto.getDbcount() );
        resultTask.setEndtime( dto.getEndtime() );
        resultTask.setId( dto.getId() );
        resultTask.setSparefield1( dto.getSparefield1() );
        resultTask.setSparefield2( dto.getSparefield2() );
        resultTask.setSparefield3( dto.getSparefield3() );
        resultTask.setSparefield4( dto.getSparefield4() );
        resultTask.setSparefield5( dto.getSparefield5() );
        resultTask.setStarttime( dto.getStarttime() );
        resultTask.setState( dto.getState() );
        resultTask.setSubmituser( dto.getSubmituser() );
        resultTask.setTaskno( dto.getTaskno() );
        resultTask.setUsetime( dto.getUsetime() );
        resultTask.setWpcount( dto.getWpcount() );
        resultTask.setWpdbcount( dto.getWpdbcount() );

        return resultTask;
    }

    @Override
    public List<ResultTask> toEntity(List<ResultTaskDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ResultTask> list = new ArrayList<ResultTask>( dtoList.size() );
        for ( ResultTaskDto resultTaskDto : dtoList ) {
            list.add( toEntity( resultTaskDto ) );
        }

        return list;
    }
}
