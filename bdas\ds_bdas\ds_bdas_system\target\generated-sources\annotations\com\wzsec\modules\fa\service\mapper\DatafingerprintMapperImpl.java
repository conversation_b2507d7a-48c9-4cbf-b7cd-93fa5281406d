package com.wzsec.modules.fa.service.mapper;

import com.wzsec.modules.fa.domain.Datafingerprint;
import com.wzsec.modules.fa.service.dto.DatafingerprintDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:30+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class DatafingerprintMapperImpl implements DatafingerprintMapper {

    @Override
    public Datafingerprint toEntity(DatafingerprintDto dto) {
        if ( dto == null ) {
            return null;
        }

        Datafingerprint datafingerprint = new Datafingerprint();

        datafingerprint.setId( dto.getId() );
        datafingerprint.setDobject( dto.getDobject() );
        datafingerprint.setDname( dto.getDname() );
        datafingerprint.setDatatype( dto.getDatatype() );
        datafingerprint.setDsize( dto.getDsize() );
        datafingerprint.setDatafingerprint( dto.getDatafingerprint() );
        datafingerprint.setFingerprintsimilarity( dto.getFingerprintsimilarity() );
        datafingerprint.setDescription( dto.getDescription() );
        datafingerprint.setDlevel( dto.getDlevel() );
        datafingerprint.setCreatetime( dto.getCreatetime() );
        datafingerprint.setSparefield1( dto.getSparefield1() );
        datafingerprint.setSparefield2( dto.getSparefield2() );
        datafingerprint.setSparefield3( dto.getSparefield3() );
        datafingerprint.setSparefield4( dto.getSparefield4() );

        return datafingerprint;
    }

    @Override
    public DatafingerprintDto toDto(Datafingerprint entity) {
        if ( entity == null ) {
            return null;
        }

        DatafingerprintDto datafingerprintDto = new DatafingerprintDto();

        datafingerprintDto.setId( entity.getId() );
        datafingerprintDto.setDobject( entity.getDobject() );
        datafingerprintDto.setDname( entity.getDname() );
        datafingerprintDto.setDatatype( entity.getDatatype() );
        datafingerprintDto.setDsize( entity.getDsize() );
        datafingerprintDto.setDatafingerprint( entity.getDatafingerprint() );
        datafingerprintDto.setFingerprintsimilarity( entity.getFingerprintsimilarity() );
        datafingerprintDto.setDescription( entity.getDescription() );
        datafingerprintDto.setDlevel( entity.getDlevel() );
        datafingerprintDto.setCreatetime( entity.getCreatetime() );
        datafingerprintDto.setSparefield1( entity.getSparefield1() );
        datafingerprintDto.setSparefield2( entity.getSparefield2() );
        datafingerprintDto.setSparefield3( entity.getSparefield3() );
        datafingerprintDto.setSparefield4( entity.getSparefield4() );

        return datafingerprintDto;
    }

    @Override
    public List<Datafingerprint> toEntity(List<DatafingerprintDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Datafingerprint> list = new ArrayList<Datafingerprint>( dtoList.size() );
        for ( DatafingerprintDto datafingerprintDto : dtoList ) {
            list.add( toEntity( datafingerprintDto ) );
        }

        return list;
    }

    @Override
    public List<DatafingerprintDto> toDto(List<Datafingerprint> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<DatafingerprintDto> list = new ArrayList<DatafingerprintDto>( entityList.size() );
        for ( Datafingerprint datafingerprint : entityList ) {
            list.add( toDto( datafingerprint ) );
        }

        return list;
    }
}
