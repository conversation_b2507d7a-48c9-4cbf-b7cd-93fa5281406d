package com.wzsec.modules.sdd.discover.service.mapper;

import com.wzsec.modules.sdd.discover.domain.Outlineresult;
import com.wzsec.modules.sdd.discover.service.dto.OutlineresultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:51+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class OutlineresultMapperImpl implements OutlineresultMapper {

    @Override
    public OutlineresultDto toDto(Outlineresult entity) {
        if ( entity == null ) {
            return null;
        }

        OutlineresultDto outlineresultDto = new OutlineresultDto();

        outlineresultDto.setCreatetime( entity.getCreatetime() );
        outlineresultDto.setDatasourcetype( entity.getDatasourcetype() );
        outlineresultDto.setEndtime( entity.getEndtime() );
        outlineresultDto.setId( entity.getId() );
        outlineresultDto.setLocation( entity.getLocation() );
        if ( entity.getSparefield1() != null ) {
            outlineresultDto.setSparefield1( String.valueOf( entity.getSparefield1() ) );
        }
        outlineresultDto.setSparefield2( entity.getSparefield2() );
        outlineresultDto.setSparefield3( entity.getSparefield3() );
        outlineresultDto.setSparefield4( entity.getSparefield4() );
        outlineresultDto.setSparefield5( entity.getSparefield5() );
        outlineresultDto.setStarttime( entity.getStarttime() );
        outlineresultDto.setSubmituser( entity.getSubmituser() );
        outlineresultDto.setTaskname( entity.getTaskname() );
        outlineresultDto.setTypenum( entity.getTypenum() );
        outlineresultDto.setUsetime( entity.getUsetime() );

        return outlineresultDto;
    }

    @Override
    public List<OutlineresultDto> toDto(List<Outlineresult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<OutlineresultDto> list = new ArrayList<OutlineresultDto>( entityList.size() );
        for ( Outlineresult outlineresult : entityList ) {
            list.add( toDto( outlineresult ) );
        }

        return list;
    }

    @Override
    public Outlineresult toEntity(OutlineresultDto dto) {
        if ( dto == null ) {
            return null;
        }

        Outlineresult outlineresult = new Outlineresult();

        outlineresult.setCreatetime( dto.getCreatetime() );
        outlineresult.setDatasourcetype( dto.getDatasourcetype() );
        outlineresult.setEndtime( dto.getEndtime() );
        outlineresult.setId( dto.getId() );
        outlineresult.setLocation( dto.getLocation() );
        if ( dto.getSparefield1() != null ) {
            outlineresult.setSparefield1( Long.parseLong( dto.getSparefield1() ) );
        }
        outlineresult.setSparefield2( dto.getSparefield2() );
        outlineresult.setSparefield3( dto.getSparefield3() );
        outlineresult.setSparefield4( dto.getSparefield4() );
        outlineresult.setSparefield5( dto.getSparefield5() );
        outlineresult.setStarttime( dto.getStarttime() );
        outlineresult.setSubmituser( dto.getSubmituser() );
        outlineresult.setTaskname( dto.getTaskname() );
        outlineresult.setTypenum( dto.getTypenum() );
        outlineresult.setUsetime( dto.getUsetime() );

        return outlineresult;
    }

    @Override
    public List<Outlineresult> toEntity(List<OutlineresultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Outlineresult> list = new ArrayList<Outlineresult>( dtoList.size() );
        for ( OutlineresultDto outlineresultDto : dtoList ) {
            list.add( toEntity( outlineresultDto ) );
        }

        return list;
    }
}
