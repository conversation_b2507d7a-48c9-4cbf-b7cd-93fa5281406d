package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcApprovallog;
import com.wzsec.modules.ic.service.dto.IcApprovallogDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:01+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class IcApprovallogMapperImpl implements IcApprovallogMapper {

    @Override
    public IcApprovallog toEntity(IcApprovallogDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcApprovallog icApprovallog = new IcApprovallog();

        icApprovallog.setId( dto.getId() );
        if ( dto.getLogid() != null ) {
            icApprovallog.setLogid( String.valueOf( dto.getLogid() ) );
        }
        icApprovallog.setAk( dto.getAk() );
        icApprovallog.setSk( dto.getSk() );
        icApprovallog.setOpen( dto.getOpen() );
        icApprovallog.setInfoid( dto.getInfoid() );
        icApprovallog.setAudittime( dto.getAudittime() );
        icApprovallog.setMartsupplyorgname( dto.getMartsupplyorgname() );
        icApprovallog.setName( dto.getName() );
        icApprovallog.setApplyorgname( dto.getApplyorgname() );
        icApprovallog.setApplytime( dto.getApplytime() );
        icApprovallog.setSharetype( dto.getSharetype() );
        icApprovallog.setSysname( dto.getSysname() );
        icApprovallog.setResourceapplyid( dto.getResourceapplyid() );
        icApprovallog.setShareapplyid( dto.getShareapplyid() );
        icApprovallog.setKeyid( dto.getKeyid() );
        icApprovallog.setResourcename( dto.getResourcename() );
        icApprovallog.setResourcetype( dto.getResourcetype() );
        icApprovallog.setCreatetime( dto.getCreatetime() );
        icApprovallog.setSparefield1( dto.getSparefield1() );
        icApprovallog.setSparefield2( dto.getSparefield2() );
        icApprovallog.setSparefield3( dto.getSparefield3() );
        icApprovallog.setSparefield4( dto.getSparefield4() );
        icApprovallog.setSparefield5( dto.getSparefield5() );

        return icApprovallog;
    }

    @Override
    public IcApprovallogDto toDto(IcApprovallog entity) {
        if ( entity == null ) {
            return null;
        }

        IcApprovallogDto icApprovallogDto = new IcApprovallogDto();

        icApprovallogDto.setId( entity.getId() );
        if ( entity.getLogid() != null ) {
            icApprovallogDto.setLogid( Integer.parseInt( entity.getLogid() ) );
        }
        icApprovallogDto.setAk( entity.getAk() );
        icApprovallogDto.setSk( entity.getSk() );
        icApprovallogDto.setOpen( entity.getOpen() );
        icApprovallogDto.setInfoid( entity.getInfoid() );
        icApprovallogDto.setAudittime( entity.getAudittime() );
        icApprovallogDto.setMartsupplyorgname( entity.getMartsupplyorgname() );
        icApprovallogDto.setName( entity.getName() );
        icApprovallogDto.setApplyorgname( entity.getApplyorgname() );
        icApprovallogDto.setApplytime( entity.getApplytime() );
        icApprovallogDto.setSharetype( entity.getSharetype() );
        icApprovallogDto.setSysname( entity.getSysname() );
        icApprovallogDto.setResourceapplyid( entity.getResourceapplyid() );
        icApprovallogDto.setShareapplyid( entity.getShareapplyid() );
        icApprovallogDto.setKeyid( entity.getKeyid() );
        icApprovallogDto.setResourcename( entity.getResourcename() );
        icApprovallogDto.setResourcetype( entity.getResourcetype() );
        icApprovallogDto.setCreatetime( entity.getCreatetime() );
        icApprovallogDto.setSparefield1( entity.getSparefield1() );
        icApprovallogDto.setSparefield2( entity.getSparefield2() );
        icApprovallogDto.setSparefield3( entity.getSparefield3() );
        icApprovallogDto.setSparefield4( entity.getSparefield4() );
        icApprovallogDto.setSparefield5( entity.getSparefield5() );

        return icApprovallogDto;
    }

    @Override
    public List<IcApprovallog> toEntity(List<IcApprovallogDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcApprovallog> list = new ArrayList<IcApprovallog>( dtoList.size() );
        for ( IcApprovallogDto icApprovallogDto : dtoList ) {
            list.add( toEntity( icApprovallogDto ) );
        }

        return list;
    }

    @Override
    public List<IcApprovallogDto> toDto(List<IcApprovallog> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcApprovallogDto> list = new ArrayList<IcApprovallogDto>( entityList.size() );
        for ( IcApprovallog icApprovallog : entityList ) {
            list.add( toDto( icApprovallog ) );
        }

        return list;
    }
}
