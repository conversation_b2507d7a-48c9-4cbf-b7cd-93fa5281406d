package com.wzsec.modules.weakpwd.service.mapper;

import com.wzsec.modules.weakpwd.domain.Subtask;
import com.wzsec.modules.weakpwd.service.dto.SubtaskDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:30+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class SubtaskMapperImpl implements SubtaskMapper {

    @Override
    public SubtaskDto toDto(Subtask entity) {
        if ( entity == null ) {
            return null;
        }

        SubtaskDto subtaskDto = new SubtaskDto();

        subtaskDto.setCreatetime( entity.getCreatetime() );
        subtaskDto.setCreateuser( entity.getCreateuser() );
        subtaskDto.setDatasourceid( entity.getDatasourceid() );
        subtaskDto.setId( entity.getId() );
        subtaskDto.setLoginnamefield( entity.getLoginnamefield() );
        subtaskDto.setNote( entity.getNote() );
        subtaskDto.setPwdfield( entity.getPwdfield() );
        subtaskDto.setSparefield1( entity.getSparefield1() );
        subtaskDto.setSparefield2( entity.getSparefield2() );
        subtaskDto.setSparefield3( entity.getSparefield3() );
        subtaskDto.setSparefield4( entity.getSparefield4() );
        subtaskDto.setSparefield5( entity.getSparefield5() );
        subtaskDto.setSubtaskno( entity.getSubtaskno() );
        subtaskDto.setTablename( entity.getTablename() );
        subtaskDto.setTaskno( entity.getTaskno() );
        subtaskDto.setUpdatetime( entity.getUpdatetime() );
        subtaskDto.setUpdateuser( entity.getUpdateuser() );
        subtaskDto.setUsernamefield( entity.getUsernamefield() );

        return subtaskDto;
    }

    @Override
    public List<SubtaskDto> toDto(List<Subtask> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<SubtaskDto> list = new ArrayList<SubtaskDto>( entityList.size() );
        for ( Subtask subtask : entityList ) {
            list.add( toDto( subtask ) );
        }

        return list;
    }

    @Override
    public Subtask toEntity(SubtaskDto dto) {
        if ( dto == null ) {
            return null;
        }

        Subtask subtask = new Subtask();

        subtask.setCreatetime( dto.getCreatetime() );
        subtask.setCreateuser( dto.getCreateuser() );
        subtask.setDatasourceid( dto.getDatasourceid() );
        subtask.setId( dto.getId() );
        subtask.setLoginnamefield( dto.getLoginnamefield() );
        subtask.setNote( dto.getNote() );
        subtask.setPwdfield( dto.getPwdfield() );
        subtask.setSparefield1( dto.getSparefield1() );
        subtask.setSparefield2( dto.getSparefield2() );
        subtask.setSparefield3( dto.getSparefield3() );
        subtask.setSparefield4( dto.getSparefield4() );
        subtask.setSparefield5( dto.getSparefield5() );
        subtask.setSubtaskno( dto.getSubtaskno() );
        subtask.setTablename( dto.getTablename() );
        subtask.setTaskno( dto.getTaskno() );
        subtask.setUpdatetime( dto.getUpdatetime() );
        subtask.setUpdateuser( dto.getUpdateuser() );
        subtask.setUsernamefield( dto.getUsernamefield() );

        return subtask;
    }

    @Override
    public List<Subtask> toEntity(List<SubtaskDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Subtask> list = new ArrayList<Subtask>( dtoList.size() );
        for ( SubtaskDto subtaskDto : dtoList ) {
            list.add( toEntity( subtaskDto ) );
        }

        return list;
    }
}
