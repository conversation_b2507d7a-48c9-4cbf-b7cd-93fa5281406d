package com.wzsec.modules.ic.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.modules.ic.domain.Approvaldetection;
import com.wzsec.modules.ic.service.ApprovaldetectionService;
import com.wzsec.modules.ic.service.dto.ApprovaldetectionQueryCriteria;
import com.wzsec.utils.DomainUtil;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-04-07
 */
// @Api(tags = "接口数据合规检测结果管理")
@RestController
@RequestMapping("/api/approvaldetection")
public class ApprovaldetectionController {

    private final ApprovaldetectionService approvaldetectionService;

    public ApprovaldetectionController(ApprovaldetectionService approvaldetectionService) {
        this.approvaldetectionService = approvaldetectionService;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, ApprovaldetectionQueryCriteria criteria) throws IOException {

        // 根据申请部门名称分域
        List<String> domainAkList = DomainUtil.queryDomainAk();
        criteria.setAk(domainAkList);

        approvaldetectionService.download(approvaldetectionService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询接口数据合规检测结果")
    // @ApiOperation("查询接口数据合规检测结果")
    public ResponseEntity<Object> getApprovaldetections(ApprovaldetectionQueryCriteria criteria, Pageable pageable) {

        // 根据申请部门名称分域
        List<String> domainAkList = DomainUtil.queryDomainAk();
        criteria.setAk(domainAkList);

        return new ResponseEntity<>(approvaldetectionService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增接口数据合规检测结果")
    // @ApiOperation("新增接口数据合规检测结果")
    public ResponseEntity<Object> create(@Validated @RequestBody Approvaldetection resources) {
        return new ResponseEntity<>(approvaldetectionService.create(resources), HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改接口数据合规检测结果")
    // @ApiOperation("修改接口数据合规检测结果")
    public ResponseEntity<Object> update(@Validated @RequestBody Approvaldetection resources) {
        approvaldetectionService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除接口数据合规检测结果")
    // @ApiOperation("删除接口数据合规检测结果")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        approvaldetectionService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
