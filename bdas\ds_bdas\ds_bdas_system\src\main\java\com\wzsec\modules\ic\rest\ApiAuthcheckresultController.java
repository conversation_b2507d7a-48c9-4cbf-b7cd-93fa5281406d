package com.wzsec.modules.ic.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.modules.ic.domain.ApiAuthcheckresult;
import com.wzsec.modules.ic.service.ApiAuthcheckresultService;
import com.wzsec.modules.ic.service.dto.ApiAuthcheckresultQueryCriteria;
import com.wzsec.utils.DomainUtil;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-08-29
 */
@RestController
@RequestMapping("/api/apiAuthcheckresult")
public class ApiAuthcheckresultController {

    private final ApiAuthcheckresultService apiAuthcheckresultService;

    public ApiAuthcheckresultController(ApiAuthcheckresultService apiAuthcheckresultService) {
        this.apiAuthcheckresultService = apiAuthcheckresultService;
    }


    @Log("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, ApiAuthcheckresultQueryCriteria criteria) throws IOException {

        // 根据申请部门名称分域
        List<String> domainAkList = DomainUtil.queryDomainAk();
        criteria.setAk(domainAkList);

        apiAuthcheckresultService.download(apiAuthcheckresultService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询接口鉴权")
    // @ApiOperation("查询接口鉴权")
//   // @PreAuthorize("@el.check('apiAuthcheckresult:list')")
    public ResponseEntity<Object> getApiAuthcheckresults(ApiAuthcheckresultQueryCriteria criteria, Pageable pageable) {

        // 根据申请部门名称分域
        List<String> domainAkList = DomainUtil.queryDomainAk();
        criteria.setAk(domainAkList);

        return new ResponseEntity<>(apiAuthcheckresultService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增接口鉴权")
    public ResponseEntity<Object> create(@Validated @RequestBody ApiAuthcheckresult resources) {
        return new ResponseEntity<>(apiAuthcheckresultService.create(resources), HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改接口鉴权")
    public ResponseEntity<Object> update(@Validated @RequestBody ApiAuthcheckresult resources) {
        apiAuthcheckresultService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除接口鉴权")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        apiAuthcheckresultService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
