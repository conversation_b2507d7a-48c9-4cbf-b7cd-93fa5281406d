package com.wzsec.modules.z.service.mapper;

import com.wzsec.modules.z.domain.ZSpriderRule;
import com.wzsec.modules.z.service.dto.ZSpriderRuleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:31+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ZSpriderRuleMapperImpl implements ZSpriderRuleMapper {

    @Override
    public ZSpriderRule toEntity(ZSpriderRuleDto dto) {
        if ( dto == null ) {
            return null;
        }

        ZSpriderRule zSpriderRule = new ZSpriderRule();

        zSpriderRule.setId( dto.getId() );
        zSpriderRule.setRuleName( dto.getRuleName() );
        zSpriderRule.setRegex( dto.getRegex() );
        zSpriderRule.setClassName( dto.getClassName() );
        zSpriderRule.setMethodName( dto.getMethodName() );
        zSpriderRule.setInformationLeakage( dto.getInformationLeakage() );
        zSpriderRule.setScanFlag( dto.getScanFlag() );
        zSpriderRule.setDangerLevel( dto.getDangerLevel() );
        zSpriderRule.setClassify( dto.getClassify() );
        zSpriderRule.setSuspectedLevel( dto.getSuspectedLevel() );
        zSpriderRule.setCreateDate( dto.getCreateDate() );
        zSpriderRule.setCreateUserId( dto.getCreateUserId() );
        zSpriderRule.setUpdateDate( dto.getUpdateDate() );
        zSpriderRule.setUpdateUserId( dto.getUpdateUserId() );
        zSpriderRule.setBz( dto.getBz() );

        return zSpriderRule;
    }

    @Override
    public ZSpriderRuleDto toDto(ZSpriderRule entity) {
        if ( entity == null ) {
            return null;
        }

        ZSpriderRuleDto zSpriderRuleDto = new ZSpriderRuleDto();

        zSpriderRuleDto.setId( entity.getId() );
        zSpriderRuleDto.setRuleName( entity.getRuleName() );
        zSpriderRuleDto.setRegex( entity.getRegex() );
        zSpriderRuleDto.setClassName( entity.getClassName() );
        zSpriderRuleDto.setMethodName( entity.getMethodName() );
        zSpriderRuleDto.setInformationLeakage( entity.getInformationLeakage() );
        zSpriderRuleDto.setScanFlag( entity.getScanFlag() );
        zSpriderRuleDto.setDangerLevel( entity.getDangerLevel() );
        zSpriderRuleDto.setClassify( entity.getClassify() );
        zSpriderRuleDto.setSuspectedLevel( entity.getSuspectedLevel() );
        zSpriderRuleDto.setCreateDate( entity.getCreateDate() );
        zSpriderRuleDto.setCreateUserId( entity.getCreateUserId() );
        zSpriderRuleDto.setUpdateDate( entity.getUpdateDate() );
        zSpriderRuleDto.setUpdateUserId( entity.getUpdateUserId() );
        zSpriderRuleDto.setBz( entity.getBz() );

        return zSpriderRuleDto;
    }

    @Override
    public List<ZSpriderRule> toEntity(List<ZSpriderRuleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ZSpriderRule> list = new ArrayList<ZSpriderRule>( dtoList.size() );
        for ( ZSpriderRuleDto zSpriderRuleDto : dtoList ) {
            list.add( toEntity( zSpriderRuleDto ) );
        }

        return list;
    }

    @Override
    public List<ZSpriderRuleDto> toDto(List<ZSpriderRule> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ZSpriderRuleDto> list = new ArrayList<ZSpriderRuleDto>( entityList.size() );
        for ( ZSpriderRule zSpriderRule : entityList ) {
            list.add( toDto( zSpriderRule ) );
        }

        return list;
    }
}
