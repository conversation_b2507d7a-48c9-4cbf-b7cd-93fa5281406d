package com.wzsec.modules.z.service.mapper;

import com.wzsec.modules.z.domain.ZSpriderRule;
import com.wzsec.modules.z.service.dto.ZSpriderRuleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:29+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ZSpriderRuleMapperImpl implements ZSpriderRuleMapper {

    @Override
    public ZSpriderRuleDto toDto(ZSpriderRule entity) {
        if ( entity == null ) {
            return null;
        }

        ZSpriderRuleDto zSpriderRuleDto = new ZSpriderRuleDto();

        zSpriderRuleDto.setBz( entity.getBz() );
        zSpriderRuleDto.setClassName( entity.getClassName() );
        zSpriderRuleDto.setClassify( entity.getClassify() );
        zSpriderRuleDto.setCreateDate( entity.getCreateDate() );
        zSpriderRuleDto.setCreateUserId( entity.getCreateUserId() );
        zSpriderRuleDto.setDangerLevel( entity.getDangerLevel() );
        zSpriderRuleDto.setId( entity.getId() );
        zSpriderRuleDto.setInformationLeakage( entity.getInformationLeakage() );
        zSpriderRuleDto.setMethodName( entity.getMethodName() );
        zSpriderRuleDto.setRegex( entity.getRegex() );
        zSpriderRuleDto.setRuleName( entity.getRuleName() );
        zSpriderRuleDto.setScanFlag( entity.getScanFlag() );
        zSpriderRuleDto.setSuspectedLevel( entity.getSuspectedLevel() );
        zSpriderRuleDto.setUpdateDate( entity.getUpdateDate() );
        zSpriderRuleDto.setUpdateUserId( entity.getUpdateUserId() );

        return zSpriderRuleDto;
    }

    @Override
    public List<ZSpriderRuleDto> toDto(List<ZSpriderRule> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ZSpriderRuleDto> list = new ArrayList<ZSpriderRuleDto>( entityList.size() );
        for ( ZSpriderRule zSpriderRule : entityList ) {
            list.add( toDto( zSpriderRule ) );
        }

        return list;
    }

    @Override
    public ZSpriderRule toEntity(ZSpriderRuleDto dto) {
        if ( dto == null ) {
            return null;
        }

        ZSpriderRule zSpriderRule = new ZSpriderRule();

        zSpriderRule.setBz( dto.getBz() );
        zSpriderRule.setClassName( dto.getClassName() );
        zSpriderRule.setClassify( dto.getClassify() );
        zSpriderRule.setCreateDate( dto.getCreateDate() );
        zSpriderRule.setCreateUserId( dto.getCreateUserId() );
        zSpriderRule.setDangerLevel( dto.getDangerLevel() );
        zSpriderRule.setId( dto.getId() );
        zSpriderRule.setInformationLeakage( dto.getInformationLeakage() );
        zSpriderRule.setMethodName( dto.getMethodName() );
        zSpriderRule.setRegex( dto.getRegex() );
        zSpriderRule.setRuleName( dto.getRuleName() );
        zSpriderRule.setScanFlag( dto.getScanFlag() );
        zSpriderRule.setSuspectedLevel( dto.getSuspectedLevel() );
        zSpriderRule.setUpdateDate( dto.getUpdateDate() );
        zSpriderRule.setUpdateUserId( dto.getUpdateUserId() );

        return zSpriderRule;
    }

    @Override
    public List<ZSpriderRule> toEntity(List<ZSpriderRuleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ZSpriderRule> list = new ArrayList<ZSpriderRule>( dtoList.size() );
        for ( ZSpriderRuleDto zSpriderRuleDto : dtoList ) {
            list.add( toEntity( zSpriderRuleDto ) );
        }

        return list;
    }
}
