package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcCustomerinformation;
import com.wzsec.modules.ic.service.dto.IcCustomerinformationDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:30+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class IcCustomerinformationMapperImpl implements IcCustomerinformationMapper {

    @Override
    public IcCustomerinformation toEntity(IcCustomerinformationDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcCustomerinformation icCustomerinformation = new IcCustomerinformation();

        icCustomerinformation.setId( dto.getId() );
        icCustomerinformation.setClientname( dto.getClientname() );
        icCustomerinformation.setClientsign( dto.getClientsign() );
        icCustomerinformation.setInterfacecode( dto.getInterfacecode() );
        icCustomerinformation.setInterfacesign( dto.getInterfacesign() );
        icCustomerinformation.setInterfacename( dto.getInterfacename() );
        icCustomerinformation.setOpeningtime( dto.getOpeningtime() );
        icCustomerinformation.setClosetime( dto.getClosetime() );
        icCustomerinformation.setCreatetime( dto.getCreatetime() );
        icCustomerinformation.setType( dto.getType() );
        icCustomerinformation.setApiKey( dto.getApiKey() );
        icCustomerinformation.setOrdernumber( dto.getOrdernumber() );
        icCustomerinformation.setProductform( dto.getProductform() );
        icCustomerinformation.setReservedfield1( dto.getReservedfield1() );
        icCustomerinformation.setReservedfield2( dto.getReservedfield2() );
        icCustomerinformation.setReservedfield3( dto.getReservedfield3() );
        icCustomerinformation.setReservedfield4( dto.getReservedfield4() );
        icCustomerinformation.setNote( dto.getNote() );

        return icCustomerinformation;
    }

    @Override
    public IcCustomerinformationDto toDto(IcCustomerinformation entity) {
        if ( entity == null ) {
            return null;
        }

        IcCustomerinformationDto icCustomerinformationDto = new IcCustomerinformationDto();

        icCustomerinformationDto.setId( entity.getId() );
        icCustomerinformationDto.setClientname( entity.getClientname() );
        icCustomerinformationDto.setClientsign( entity.getClientsign() );
        icCustomerinformationDto.setInterfacecode( entity.getInterfacecode() );
        icCustomerinformationDto.setInterfacesign( entity.getInterfacesign() );
        icCustomerinformationDto.setInterfacename( entity.getInterfacename() );
        icCustomerinformationDto.setOpeningtime( entity.getOpeningtime() );
        icCustomerinformationDto.setClosetime( entity.getClosetime() );
        icCustomerinformationDto.setCreatetime( entity.getCreatetime() );
        icCustomerinformationDto.setType( entity.getType() );
        icCustomerinformationDto.setApiKey( entity.getApiKey() );
        icCustomerinformationDto.setOrdernumber( entity.getOrdernumber() );
        icCustomerinformationDto.setProductform( entity.getProductform() );
        icCustomerinformationDto.setReservedfield1( entity.getReservedfield1() );
        icCustomerinformationDto.setReservedfield2( entity.getReservedfield2() );
        icCustomerinformationDto.setReservedfield3( entity.getReservedfield3() );
        icCustomerinformationDto.setReservedfield4( entity.getReservedfield4() );
        icCustomerinformationDto.setNote( entity.getNote() );

        return icCustomerinformationDto;
    }

    @Override
    public List<IcCustomerinformation> toEntity(List<IcCustomerinformationDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcCustomerinformation> list = new ArrayList<IcCustomerinformation>( dtoList.size() );
        for ( IcCustomerinformationDto icCustomerinformationDto : dtoList ) {
            list.add( toEntity( icCustomerinformationDto ) );
        }

        return list;
    }

    @Override
    public List<IcCustomerinformationDto> toDto(List<IcCustomerinformation> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcCustomerinformationDto> list = new ArrayList<IcCustomerinformationDto>( entityList.size() );
        for ( IcCustomerinformation icCustomerinformation : entityList ) {
            list.add( toDto( icCustomerinformation ) );
        }

        return list;
    }
}
