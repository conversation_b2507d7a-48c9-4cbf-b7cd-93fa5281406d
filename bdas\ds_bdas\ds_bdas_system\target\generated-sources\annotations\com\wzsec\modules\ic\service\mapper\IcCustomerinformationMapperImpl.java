package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.IcCustomerinformation;
import com.wzsec.modules.ic.service.dto.IcCustomerinformationDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:26:48+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class IcCustomerinformationMapperImpl implements IcCustomerinformationMapper {

    @Override
    public IcCustomerinformationDto toDto(IcCustomerinformation entity) {
        if ( entity == null ) {
            return null;
        }

        IcCustomerinformationDto icCustomerinformationDto = new IcCustomerinformationDto();

        icCustomerinformationDto.setApiKey( entity.getApiKey() );
        icCustomerinformationDto.setClientname( entity.getClientname() );
        icCustomerinformationDto.setClientsign( entity.getClientsign() );
        icCustomerinformationDto.setClosetime( entity.getClosetime() );
        icCustomerinformationDto.setCreatetime( entity.getCreatetime() );
        icCustomerinformationDto.setId( entity.getId() );
        icCustomerinformationDto.setInterfacecode( entity.getInterfacecode() );
        icCustomerinformationDto.setInterfacename( entity.getInterfacename() );
        icCustomerinformationDto.setInterfacesign( entity.getInterfacesign() );
        icCustomerinformationDto.setNote( entity.getNote() );
        icCustomerinformationDto.setOpeningtime( entity.getOpeningtime() );
        icCustomerinformationDto.setOrdernumber( entity.getOrdernumber() );
        icCustomerinformationDto.setProductform( entity.getProductform() );
        icCustomerinformationDto.setReservedfield1( entity.getReservedfield1() );
        icCustomerinformationDto.setReservedfield2( entity.getReservedfield2() );
        icCustomerinformationDto.setReservedfield3( entity.getReservedfield3() );
        icCustomerinformationDto.setReservedfield4( entity.getReservedfield4() );
        icCustomerinformationDto.setType( entity.getType() );

        return icCustomerinformationDto;
    }

    @Override
    public List<IcCustomerinformationDto> toDto(List<IcCustomerinformation> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<IcCustomerinformationDto> list = new ArrayList<IcCustomerinformationDto>( entityList.size() );
        for ( IcCustomerinformation icCustomerinformation : entityList ) {
            list.add( toDto( icCustomerinformation ) );
        }

        return list;
    }

    @Override
    public IcCustomerinformation toEntity(IcCustomerinformationDto dto) {
        if ( dto == null ) {
            return null;
        }

        IcCustomerinformation icCustomerinformation = new IcCustomerinformation();

        icCustomerinformation.setApiKey( dto.getApiKey() );
        icCustomerinformation.setClientname( dto.getClientname() );
        icCustomerinformation.setClientsign( dto.getClientsign() );
        icCustomerinformation.setClosetime( dto.getClosetime() );
        icCustomerinformation.setCreatetime( dto.getCreatetime() );
        icCustomerinformation.setId( dto.getId() );
        icCustomerinformation.setInterfacecode( dto.getInterfacecode() );
        icCustomerinformation.setInterfacename( dto.getInterfacename() );
        icCustomerinformation.setInterfacesign( dto.getInterfacesign() );
        icCustomerinformation.setNote( dto.getNote() );
        icCustomerinformation.setOpeningtime( dto.getOpeningtime() );
        icCustomerinformation.setOrdernumber( dto.getOrdernumber() );
        icCustomerinformation.setProductform( dto.getProductform() );
        icCustomerinformation.setReservedfield1( dto.getReservedfield1() );
        icCustomerinformation.setReservedfield2( dto.getReservedfield2() );
        icCustomerinformation.setReservedfield3( dto.getReservedfield3() );
        icCustomerinformation.setReservedfield4( dto.getReservedfield4() );
        icCustomerinformation.setType( dto.getType() );

        return icCustomerinformation;
    }

    @Override
    public List<IcCustomerinformation> toEntity(List<IcCustomerinformationDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<IcCustomerinformation> list = new ArrayList<IcCustomerinformation>( dtoList.size() );
        for ( IcCustomerinformationDto icCustomerinformationDto : dtoList ) {
            list.add( toEntity( icCustomerinformationDto ) );
        }

        return list;
    }
}
