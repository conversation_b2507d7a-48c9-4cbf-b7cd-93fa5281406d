package com.wzsec.modules.apiproxy.service.mapper;

import com.wzsec.modules.apiproxy.domain.ApiInterfaceinfo;
import com.wzsec.modules.apiproxy.service.dto.ApiInterfaceinfoDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:31+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ApiInterfaceinfoMapperImpl implements ApiInterfaceinfoMapper {

    @Override
    public ApiInterfaceinfoDto toDto(ApiInterfaceinfo entity) {
        if ( entity == null ) {
            return null;
        }

        ApiInterfaceinfoDto apiInterfaceinfoDto = new ApiInterfaceinfoDto();

        apiInterfaceinfoDto.setApicode( entity.getApicode() );
        apiInterfaceinfoDto.setApides( entity.getApides() );
        apiInterfaceinfoDto.setApimethod( entity.getApimethod() );
        apiInterfaceinfoDto.setApiname( entity.getApiname() );
        apiInterfaceinfoDto.setApitype( entity.getApitype() );
        apiInterfaceinfoDto.setApiurl( entity.getApiurl() );
        apiInterfaceinfoDto.setBegindate( entity.getBegindate() );
        apiInterfaceinfoDto.setCreatetime( entity.getCreatetime() );
        apiInterfaceinfoDto.setCreateuser( entity.getCreateuser() );
        apiInterfaceinfoDto.setEnddate( entity.getEnddate() );
        apiInterfaceinfoDto.setId( entity.getId() );
        apiInterfaceinfoDto.setInterfaceStatus( entity.getInterfaceStatus() );
        apiInterfaceinfoDto.setLinkMan( entity.getLinkMan() );
        apiInterfaceinfoDto.setLinkTel( entity.getLinkTel() );
        apiInterfaceinfoDto.setSparefield1( entity.getSparefield1() );
        apiInterfaceinfoDto.setSparefield2( entity.getSparefield2() );
        apiInterfaceinfoDto.setSparefield3( entity.getSparefield3() );
        apiInterfaceinfoDto.setSparefield4( entity.getSparefield4() );
        apiInterfaceinfoDto.setSparefield5( entity.getSparefield5() );
        apiInterfaceinfoDto.setUpdatetime( entity.getUpdatetime() );
        apiInterfaceinfoDto.setUpdateuser( entity.getUpdateuser() );

        return apiInterfaceinfoDto;
    }

    @Override
    public List<ApiInterfaceinfoDto> toDto(List<ApiInterfaceinfo> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiInterfaceinfoDto> list = new ArrayList<ApiInterfaceinfoDto>( entityList.size() );
        for ( ApiInterfaceinfo apiInterfaceinfo : entityList ) {
            list.add( toDto( apiInterfaceinfo ) );
        }

        return list;
    }

    @Override
    public ApiInterfaceinfo toEntity(ApiInterfaceinfoDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiInterfaceinfo apiInterfaceinfo = new ApiInterfaceinfo();

        apiInterfaceinfo.setApicode( dto.getApicode() );
        apiInterfaceinfo.setApides( dto.getApides() );
        apiInterfaceinfo.setApimethod( dto.getApimethod() );
        apiInterfaceinfo.setApiname( dto.getApiname() );
        apiInterfaceinfo.setApitype( dto.getApitype() );
        apiInterfaceinfo.setApiurl( dto.getApiurl() );
        apiInterfaceinfo.setBegindate( dto.getBegindate() );
        apiInterfaceinfo.setCreatetime( dto.getCreatetime() );
        apiInterfaceinfo.setCreateuser( dto.getCreateuser() );
        apiInterfaceinfo.setEnddate( dto.getEnddate() );
        apiInterfaceinfo.setId( dto.getId() );
        apiInterfaceinfo.setInterfaceStatus( dto.getInterfaceStatus() );
        apiInterfaceinfo.setLinkMan( dto.getLinkMan() );
        apiInterfaceinfo.setLinkTel( dto.getLinkTel() );
        apiInterfaceinfo.setSparefield1( dto.getSparefield1() );
        apiInterfaceinfo.setSparefield2( dto.getSparefield2() );
        apiInterfaceinfo.setSparefield3( dto.getSparefield3() );
        apiInterfaceinfo.setSparefield4( dto.getSparefield4() );
        apiInterfaceinfo.setSparefield5( dto.getSparefield5() );
        apiInterfaceinfo.setUpdatetime( dto.getUpdatetime() );
        apiInterfaceinfo.setUpdateuser( dto.getUpdateuser() );

        return apiInterfaceinfo;
    }

    @Override
    public List<ApiInterfaceinfo> toEntity(List<ApiInterfaceinfoDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiInterfaceinfo> list = new ArrayList<ApiInterfaceinfo>( dtoList.size() );
        for ( ApiInterfaceinfoDto apiInterfaceinfoDto : dtoList ) {
            list.add( toEntity( apiInterfaceinfoDto ) );
        }

        return list;
    }
}
