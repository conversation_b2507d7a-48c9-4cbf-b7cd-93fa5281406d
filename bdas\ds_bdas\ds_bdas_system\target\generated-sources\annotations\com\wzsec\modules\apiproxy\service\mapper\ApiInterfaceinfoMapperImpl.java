package com.wzsec.modules.apiproxy.service.mapper;

import com.wzsec.modules.apiproxy.domain.ApiInterfaceinfo;
import com.wzsec.modules.apiproxy.service.dto.ApiInterfaceinfoDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:30+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ApiInterfaceinfoMapperImpl implements ApiInterfaceinfoMapper {

    @Override
    public ApiInterfaceinfo toEntity(ApiInterfaceinfoDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiInterfaceinfo apiInterfaceinfo = new ApiInterfaceinfo();

        apiInterfaceinfo.setId( dto.getId() );
        apiInterfaceinfo.setApicode( dto.getApicode() );
        apiInterfaceinfo.setApiname( dto.getApiname() );
        apiInterfaceinfo.setApiurl( dto.getApiurl() );
        apiInterfaceinfo.setApimethod( dto.getApimethod() );
        apiInterfaceinfo.setApides( dto.getApides() );
        apiInterfaceinfo.setApitype( dto.getApitype() );
        apiInterfaceinfo.setInterfaceStatus( dto.getInterfaceStatus() );
        apiInterfaceinfo.setBegindate( dto.getBegindate() );
        apiInterfaceinfo.setEnddate( dto.getEnddate() );
        apiInterfaceinfo.setLinkMan( dto.getLinkMan() );
        apiInterfaceinfo.setLinkTel( dto.getLinkTel() );
        apiInterfaceinfo.setCreateuser( dto.getCreateuser() );
        apiInterfaceinfo.setCreatetime( dto.getCreatetime() );
        apiInterfaceinfo.setUpdateuser( dto.getUpdateuser() );
        apiInterfaceinfo.setUpdatetime( dto.getUpdatetime() );
        apiInterfaceinfo.setSparefield1( dto.getSparefield1() );
        apiInterfaceinfo.setSparefield2( dto.getSparefield2() );
        apiInterfaceinfo.setSparefield3( dto.getSparefield3() );
        apiInterfaceinfo.setSparefield4( dto.getSparefield4() );
        apiInterfaceinfo.setSparefield5( dto.getSparefield5() );

        return apiInterfaceinfo;
    }

    @Override
    public ApiInterfaceinfoDto toDto(ApiInterfaceinfo entity) {
        if ( entity == null ) {
            return null;
        }

        ApiInterfaceinfoDto apiInterfaceinfoDto = new ApiInterfaceinfoDto();

        apiInterfaceinfoDto.setId( entity.getId() );
        apiInterfaceinfoDto.setApicode( entity.getApicode() );
        apiInterfaceinfoDto.setApiname( entity.getApiname() );
        apiInterfaceinfoDto.setApiurl( entity.getApiurl() );
        apiInterfaceinfoDto.setApimethod( entity.getApimethod() );
        apiInterfaceinfoDto.setApides( entity.getApides() );
        apiInterfaceinfoDto.setApitype( entity.getApitype() );
        apiInterfaceinfoDto.setInterfaceStatus( entity.getInterfaceStatus() );
        apiInterfaceinfoDto.setBegindate( entity.getBegindate() );
        apiInterfaceinfoDto.setEnddate( entity.getEnddate() );
        apiInterfaceinfoDto.setLinkMan( entity.getLinkMan() );
        apiInterfaceinfoDto.setLinkTel( entity.getLinkTel() );
        apiInterfaceinfoDto.setCreateuser( entity.getCreateuser() );
        apiInterfaceinfoDto.setCreatetime( entity.getCreatetime() );
        apiInterfaceinfoDto.setUpdateuser( entity.getUpdateuser() );
        apiInterfaceinfoDto.setUpdatetime( entity.getUpdatetime() );
        apiInterfaceinfoDto.setSparefield1( entity.getSparefield1() );
        apiInterfaceinfoDto.setSparefield2( entity.getSparefield2() );
        apiInterfaceinfoDto.setSparefield3( entity.getSparefield3() );
        apiInterfaceinfoDto.setSparefield4( entity.getSparefield4() );
        apiInterfaceinfoDto.setSparefield5( entity.getSparefield5() );

        return apiInterfaceinfoDto;
    }

    @Override
    public List<ApiInterfaceinfo> toEntity(List<ApiInterfaceinfoDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiInterfaceinfo> list = new ArrayList<ApiInterfaceinfo>( dtoList.size() );
        for ( ApiInterfaceinfoDto apiInterfaceinfoDto : dtoList ) {
            list.add( toEntity( apiInterfaceinfoDto ) );
        }

        return list;
    }

    @Override
    public List<ApiInterfaceinfoDto> toDto(List<ApiInterfaceinfo> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiInterfaceinfoDto> list = new ArrayList<ApiInterfaceinfoDto>( entityList.size() );
        for ( ApiInterfaceinfo apiInterfaceinfo : entityList ) {
            list.add( toDto( apiInterfaceinfo ) );
        }

        return list;
    }
}
