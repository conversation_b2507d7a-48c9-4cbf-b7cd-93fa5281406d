package com.wzsec.modules.emailsend.service.mapper;

import com.wzsec.modules.emailsend.domain.EmailSendConfig;
import com.wzsec.modules.emailsend.service.dto.EmailSendConfigDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:30+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class EmailSendConfigMapperImpl implements EmailSendConfigMapper {

    @Override
    public EmailSendConfig toEntity(EmailSendConfigDto dto) {
        if ( dto == null ) {
            return null;
        }

        EmailSendConfig emailSendConfig = new EmailSendConfig();

        emailSendConfig.setId( dto.getId() );
        emailSendConfig.setFromUser( dto.getFromUser() );
        emailSendConfig.setHost( dto.getHost() );
        emailSendConfig.setPass( dto.getPass() );
        emailSendConfig.setPort( dto.getPort() );
        emailSendConfig.setUsers( dto.getUsers() );
        emailSendConfig.setChecktime( dto.getChecktime() );
        emailSendConfig.setReceiver( dto.getReceiver() );
        emailSendConfig.setMotif( dto.getMotif() );
        emailSendConfig.setTaskstate( dto.getTaskstate() );
        emailSendConfig.setSubmittype( dto.getSubmittype() );
        emailSendConfig.setExecutestate( dto.getExecutestate() );

        return emailSendConfig;
    }

    @Override
    public EmailSendConfigDto toDto(EmailSendConfig entity) {
        if ( entity == null ) {
            return null;
        }

        EmailSendConfigDto emailSendConfigDto = new EmailSendConfigDto();

        emailSendConfigDto.setId( entity.getId() );
        emailSendConfigDto.setFromUser( entity.getFromUser() );
        emailSendConfigDto.setHost( entity.getHost() );
        emailSendConfigDto.setPass( entity.getPass() );
        emailSendConfigDto.setPort( entity.getPort() );
        emailSendConfigDto.setUsers( entity.getUsers() );
        emailSendConfigDto.setChecktime( entity.getChecktime() );
        emailSendConfigDto.setReceiver( entity.getReceiver() );
        emailSendConfigDto.setMotif( entity.getMotif() );
        emailSendConfigDto.setTaskstate( entity.getTaskstate() );
        emailSendConfigDto.setSubmittype( entity.getSubmittype() );
        emailSendConfigDto.setExecutestate( entity.getExecutestate() );

        return emailSendConfigDto;
    }

    @Override
    public List<EmailSendConfig> toEntity(List<EmailSendConfigDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<EmailSendConfig> list = new ArrayList<EmailSendConfig>( dtoList.size() );
        for ( EmailSendConfigDto emailSendConfigDto : dtoList ) {
            list.add( toEntity( emailSendConfigDto ) );
        }

        return list;
    }

    @Override
    public List<EmailSendConfigDto> toDto(List<EmailSendConfig> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<EmailSendConfigDto> list = new ArrayList<EmailSendConfigDto>( entityList.size() );
        for ( EmailSendConfig emailSendConfig : entityList ) {
            list.add( toDto( emailSendConfig ) );
        }

        return list;
    }
}
