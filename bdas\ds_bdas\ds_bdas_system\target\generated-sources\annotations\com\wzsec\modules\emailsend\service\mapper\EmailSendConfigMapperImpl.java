package com.wzsec.modules.emailsend.service.mapper;

import com.wzsec.modules.emailsend.domain.EmailSendConfig;
import com.wzsec.modules.emailsend.service.dto.EmailSendConfigDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:30+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class EmailSendConfigMapperImpl implements EmailSendConfigMapper {

    @Override
    public EmailSendConfigDto toDto(EmailSendConfig entity) {
        if ( entity == null ) {
            return null;
        }

        EmailSendConfigDto emailSendConfigDto = new EmailSendConfigDto();

        emailSendConfigDto.setChecktime( entity.getChecktime() );
        emailSendConfigDto.setExecutestate( entity.getExecutestate() );
        emailSendConfigDto.setFromUser( entity.getFromUser() );
        emailSendConfigDto.setHost( entity.getHost() );
        emailSendConfigDto.setId( entity.getId() );
        emailSendConfigDto.setMotif( entity.getMotif() );
        emailSendConfigDto.setPass( entity.getPass() );
        emailSendConfigDto.setPort( entity.getPort() );
        emailSendConfigDto.setReceiver( entity.getReceiver() );
        emailSendConfigDto.setSubmittype( entity.getSubmittype() );
        emailSendConfigDto.setTaskstate( entity.getTaskstate() );
        emailSendConfigDto.setUsers( entity.getUsers() );

        return emailSendConfigDto;
    }

    @Override
    public List<EmailSendConfigDto> toDto(List<EmailSendConfig> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<EmailSendConfigDto> list = new ArrayList<EmailSendConfigDto>( entityList.size() );
        for ( EmailSendConfig emailSendConfig : entityList ) {
            list.add( toDto( emailSendConfig ) );
        }

        return list;
    }

    @Override
    public EmailSendConfig toEntity(EmailSendConfigDto dto) {
        if ( dto == null ) {
            return null;
        }

        EmailSendConfig emailSendConfig = new EmailSendConfig();

        emailSendConfig.setChecktime( dto.getChecktime() );
        emailSendConfig.setExecutestate( dto.getExecutestate() );
        emailSendConfig.setFromUser( dto.getFromUser() );
        emailSendConfig.setHost( dto.getHost() );
        emailSendConfig.setId( dto.getId() );
        emailSendConfig.setMotif( dto.getMotif() );
        emailSendConfig.setPass( dto.getPass() );
        emailSendConfig.setPort( dto.getPort() );
        emailSendConfig.setReceiver( dto.getReceiver() );
        emailSendConfig.setSubmittype( dto.getSubmittype() );
        emailSendConfig.setTaskstate( dto.getTaskstate() );
        emailSendConfig.setUsers( dto.getUsers() );

        return emailSendConfig;
    }

    @Override
    public List<EmailSendConfig> toEntity(List<EmailSendConfigDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<EmailSendConfig> list = new ArrayList<EmailSendConfig>( dtoList.size() );
        for ( EmailSendConfigDto emailSendConfigDto : dtoList ) {
            list.add( toEntity( emailSendConfigDto ) );
        }

        return list;
    }
}
