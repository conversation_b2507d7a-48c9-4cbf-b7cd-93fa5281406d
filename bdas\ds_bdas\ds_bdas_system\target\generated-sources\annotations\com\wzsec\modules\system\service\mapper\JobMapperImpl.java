package com.wzsec.modules.system.service.mapper;

import com.wzsec.modules.system.domain.Job;
import com.wzsec.modules.system.service.dto.JobDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:21:31+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class JobMapperImpl implements JobMapper {

    @Autowired
    private DeptMapper deptMapper;

    @Override
    public JobDto toDto(Job entity) {
        if ( entity == null ) {
            return null;
        }

        JobDto jobDto = new JobDto();

        jobDto.setCreateTime( entity.getCreateTime() );
        jobDto.setDept( deptMapper.toDto( entity.getDept() ) );
        jobDto.setEnabled( entity.getEnabled() );
        jobDto.setId( entity.getId() );
        jobDto.setName( entity.getName() );
        jobDto.setSort( entity.getSort() );

        return jobDto;
    }

    @Override
    public List<JobDto> toDto(List<Job> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<JobDto> list = new ArrayList<JobDto>( entityList.size() );
        for ( Job job : entityList ) {
            list.add( toDto( job ) );
        }

        return list;
    }

    @Override
    public Job toEntity(JobDto dto) {
        if ( dto == null ) {
            return null;
        }

        Job job = new Job();

        job.setCreateTime( dto.getCreateTime() );
        job.setDept( deptMapper.toEntity( dto.getDept() ) );
        job.setEnabled( dto.getEnabled() );
        job.setId( dto.getId() );
        job.setName( dto.getName() );
        job.setSort( dto.getSort() );

        return job;
    }

    @Override
    public List<Job> toEntity(List<JobDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Job> list = new ArrayList<Job>( dtoList.size() );
        for ( JobDto jobDto : dtoList ) {
            list.add( toEntity( jobDto ) );
        }

        return list;
    }

    @Override
    public JobDto toDto(Job job, String deptSuperiorName) {
        if ( job == null && deptSuperiorName == null ) {
            return null;
        }

        JobDto jobDto = new JobDto();

        if ( job != null ) {
            jobDto.setCreateTime( job.getCreateTime() );
            jobDto.setDept( deptMapper.toDto( job.getDept() ) );
            jobDto.setEnabled( job.getEnabled() );
            jobDto.setId( job.getId() );
            jobDto.setName( job.getName() );
            jobDto.setSort( job.getSort() );
        }
        if ( deptSuperiorName != null ) {
            jobDto.setDeptSuperiorName( deptSuperiorName );
        }

        return jobDto;
    }
}
