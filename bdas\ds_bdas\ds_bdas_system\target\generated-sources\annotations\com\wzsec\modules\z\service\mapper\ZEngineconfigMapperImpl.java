package com.wzsec.modules.z.service.mapper;

import com.wzsec.modules.z.domain.ZEngineconfig;
import com.wzsec.modules.z.service.dto.ZEngineconfigDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:26:47+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ZEngineconfigMapperImpl implements ZEngineconfigMapper {

    @Override
    public ZEngineconfigDto toDto(ZEngineconfig entity) {
        if ( entity == null ) {
            return null;
        }

        ZEngineconfigDto zEngineconfigDto = new ZEngineconfigDto();

        zEngineconfigDto.setCname( entity.getCname() );
        zEngineconfigDto.setId( entity.getId() );
        zEngineconfigDto.setMachineip( entity.getMachineip() );
        zEngineconfigDto.setMachineport( entity.getMachineport() );
        zEngineconfigDto.setNote( entity.getNote() );
        zEngineconfigDto.setProcessname( entity.getProcessname() );
        zEngineconfigDto.setSparefield1( entity.getSparefield1() );
        zEngineconfigDto.setSparefield2( entity.getSparefield2() );
        zEngineconfigDto.setSparefield3( entity.getSparefield3() );
        zEngineconfigDto.setSparefield4( entity.getSparefield4() );
        zEngineconfigDto.setSparefield5( entity.getSparefield5() );
        zEngineconfigDto.setStatus( entity.getStatus() );

        return zEngineconfigDto;
    }

    @Override
    public List<ZEngineconfigDto> toDto(List<ZEngineconfig> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ZEngineconfigDto> list = new ArrayList<ZEngineconfigDto>( entityList.size() );
        for ( ZEngineconfig zEngineconfig : entityList ) {
            list.add( toDto( zEngineconfig ) );
        }

        return list;
    }

    @Override
    public ZEngineconfig toEntity(ZEngineconfigDto dto) {
        if ( dto == null ) {
            return null;
        }

        ZEngineconfig zEngineconfig = new ZEngineconfig();

        zEngineconfig.setCname( dto.getCname() );
        zEngineconfig.setId( dto.getId() );
        zEngineconfig.setMachineip( dto.getMachineip() );
        zEngineconfig.setMachineport( dto.getMachineport() );
        zEngineconfig.setNote( dto.getNote() );
        zEngineconfig.setProcessname( dto.getProcessname() );
        zEngineconfig.setSparefield1( dto.getSparefield1() );
        zEngineconfig.setSparefield2( dto.getSparefield2() );
        zEngineconfig.setSparefield3( dto.getSparefield3() );
        zEngineconfig.setSparefield4( dto.getSparefield4() );
        zEngineconfig.setSparefield5( dto.getSparefield5() );
        zEngineconfig.setStatus( dto.getStatus() );

        return zEngineconfig;
    }

    @Override
    public List<ZEngineconfig> toEntity(List<ZEngineconfigDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ZEngineconfig> list = new ArrayList<ZEngineconfig>( dtoList.size() );
        for ( ZEngineconfigDto zEngineconfigDto : dtoList ) {
            list.add( toEntity( zEngineconfigDto ) );
        }

        return list;
    }
}
