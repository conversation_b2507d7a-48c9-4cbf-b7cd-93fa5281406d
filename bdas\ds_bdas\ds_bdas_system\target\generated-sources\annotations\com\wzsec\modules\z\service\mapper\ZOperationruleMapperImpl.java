package com.wzsec.modules.z.service.mapper;

import com.wzsec.modules.z.domain.ZOperationrule;
import com.wzsec.modules.z.service.dto.ZOperationruleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:26:46+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ZOperationruleMapperImpl implements ZOperationruleMapper {

    @Override
    public ZOperationruleDto toDto(ZOperationrule entity) {
        if ( entity == null ) {
            return null;
        }

        ZOperationruleDto zOperationruleDto = new ZOperationruleDto();

        zOperationruleDto.setContent( entity.getContent() );
        zOperationruleDto.setCreatetime( entity.getCreatetime() );
        zOperationruleDto.setCreateuser( entity.getCreateuser() );
        zOperationruleDto.setEventcode( entity.getEventcode() );
        zOperationruleDto.setEventname( entity.getEventname() );
        zOperationruleDto.setId( entity.getId() );
        zOperationruleDto.setIsvalid( entity.getIsvalid() );
        zOperationruleDto.setNote( entity.getNote() );
        zOperationruleDto.setRisk( entity.getRisk() );
        zOperationruleDto.setSparefield1( entity.getSparefield1() );
        zOperationruleDto.setSparefield2( entity.getSparefield2() );
        zOperationruleDto.setUpdatetime( entity.getUpdatetime() );
        zOperationruleDto.setUpdateuser( entity.getUpdateuser() );

        return zOperationruleDto;
    }

    @Override
    public List<ZOperationruleDto> toDto(List<ZOperationrule> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ZOperationruleDto> list = new ArrayList<ZOperationruleDto>( entityList.size() );
        for ( ZOperationrule zOperationrule : entityList ) {
            list.add( toDto( zOperationrule ) );
        }

        return list;
    }

    @Override
    public ZOperationrule toEntity(ZOperationruleDto dto) {
        if ( dto == null ) {
            return null;
        }

        ZOperationrule zOperationrule = new ZOperationrule();

        zOperationrule.setContent( dto.getContent() );
        zOperationrule.setCreatetime( dto.getCreatetime() );
        zOperationrule.setCreateuser( dto.getCreateuser() );
        zOperationrule.setEventcode( dto.getEventcode() );
        zOperationrule.setEventname( dto.getEventname() );
        zOperationrule.setId( dto.getId() );
        zOperationrule.setIsvalid( dto.getIsvalid() );
        zOperationrule.setNote( dto.getNote() );
        zOperationrule.setRisk( dto.getRisk() );
        zOperationrule.setSparefield1( dto.getSparefield1() );
        zOperationrule.setSparefield2( dto.getSparefield2() );
        zOperationrule.setUpdatetime( dto.getUpdatetime() );
        zOperationrule.setUpdateuser( dto.getUpdateuser() );

        return zOperationrule;
    }

    @Override
    public List<ZOperationrule> toEntity(List<ZOperationruleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ZOperationrule> list = new ArrayList<ZOperationrule>( dtoList.size() );
        for ( ZOperationruleDto zOperationruleDto : dtoList ) {
            list.add( toEntity( zOperationruleDto ) );
        }

        return list;
    }
}
