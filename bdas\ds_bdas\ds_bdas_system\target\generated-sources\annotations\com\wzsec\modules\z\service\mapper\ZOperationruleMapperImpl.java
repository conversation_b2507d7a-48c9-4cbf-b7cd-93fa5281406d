package com.wzsec.modules.z.service.mapper;

import com.wzsec.modules.z.domain.ZOperationrule;
import com.wzsec.modules.z.service.dto.ZOperationruleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:31+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ZOperationruleMapperImpl implements ZOperationruleMapper {

    @Override
    public ZOperationrule toEntity(ZOperationruleDto dto) {
        if ( dto == null ) {
            return null;
        }

        ZOperationrule zOperationrule = new ZOperationrule();

        zOperationrule.setId( dto.getId() );
        zOperationrule.setEventcode( dto.getEventcode() );
        zOperationrule.setEventname( dto.getEventname() );
        zOperationrule.setContent( dto.getContent() );
        zOperationrule.setRisk( dto.getRisk() );
        zOperationrule.setIsvalid( dto.getIsvalid() );
        zOperationrule.setCreatetime( dto.getCreatetime() );
        zOperationrule.setCreateuser( dto.getCreateuser() );
        zOperationrule.setUpdatetime( dto.getUpdatetime() );
        zOperationrule.setUpdateuser( dto.getUpdateuser() );
        zOperationrule.setNote( dto.getNote() );
        zOperationrule.setSparefield1( dto.getSparefield1() );
        zOperationrule.setSparefield2( dto.getSparefield2() );

        return zOperationrule;
    }

    @Override
    public ZOperationruleDto toDto(ZOperationrule entity) {
        if ( entity == null ) {
            return null;
        }

        ZOperationruleDto zOperationruleDto = new ZOperationruleDto();

        zOperationruleDto.setId( entity.getId() );
        zOperationruleDto.setEventcode( entity.getEventcode() );
        zOperationruleDto.setEventname( entity.getEventname() );
        zOperationruleDto.setContent( entity.getContent() );
        zOperationruleDto.setRisk( entity.getRisk() );
        zOperationruleDto.setIsvalid( entity.getIsvalid() );
        zOperationruleDto.setCreatetime( entity.getCreatetime() );
        zOperationruleDto.setCreateuser( entity.getCreateuser() );
        zOperationruleDto.setUpdatetime( entity.getUpdatetime() );
        zOperationruleDto.setUpdateuser( entity.getUpdateuser() );
        zOperationruleDto.setNote( entity.getNote() );
        zOperationruleDto.setSparefield1( entity.getSparefield1() );
        zOperationruleDto.setSparefield2( entity.getSparefield2() );

        return zOperationruleDto;
    }

    @Override
    public List<ZOperationrule> toEntity(List<ZOperationruleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ZOperationrule> list = new ArrayList<ZOperationrule>( dtoList.size() );
        for ( ZOperationruleDto zOperationruleDto : dtoList ) {
            list.add( toEntity( zOperationruleDto ) );
        }

        return list;
    }

    @Override
    public List<ZOperationruleDto> toDto(List<ZOperationrule> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ZOperationruleDto> list = new ArrayList<ZOperationruleDto>( entityList.size() );
        for ( ZOperationrule zOperationrule : entityList ) {
            list.add( toDto( zOperationrule ) );
        }

        return list;
    }
}
