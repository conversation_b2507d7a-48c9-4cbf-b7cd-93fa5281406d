package com.wzsec.modules.z.service.mapper;

import com.wzsec.modules.z.domain.ZSpriderJob;
import com.wzsec.modules.z.service.dto.ZSpriderJobDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:31+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ZSpriderJobMapperImpl implements ZSpriderJobMapper {

    @Override
    public ZSpriderJob toEntity(ZSpriderJobDto dto) {
        if ( dto == null ) {
            return null;
        }

        ZSpriderJob zSpriderJob = new ZSpriderJob();

        zSpriderJob.setId( dto.getId() );
        zSpriderJob.setSpriderUrl( dto.getSpriderUrl() );
        zSpriderJob.setScanFlag( dto.getScanFlag() );
        zSpriderJob.setCron( dto.getCron() );
        zSpriderJob.setStatus( dto.getStatus() );
        zSpriderJob.setJobName( dto.getJobName() );
        zSpriderJob.setCreateDate( dto.getCreateDate() );
        zSpriderJob.setCreateUserId( dto.getCreateUserId() );
        zSpriderJob.setUpdateDate( dto.getUpdateDate() );
        zSpriderJob.setUpdateUserId( dto.getUpdateUserId() );
        zSpriderJob.setBz( dto.getBz() );

        return zSpriderJob;
    }

    @Override
    public ZSpriderJobDto toDto(ZSpriderJob entity) {
        if ( entity == null ) {
            return null;
        }

        ZSpriderJobDto zSpriderJobDto = new ZSpriderJobDto();

        zSpriderJobDto.setId( entity.getId() );
        zSpriderJobDto.setSpriderUrl( entity.getSpriderUrl() );
        zSpriderJobDto.setScanFlag( entity.getScanFlag() );
        zSpriderJobDto.setCron( entity.getCron() );
        zSpriderJobDto.setStatus( entity.getStatus() );
        zSpriderJobDto.setJobName( entity.getJobName() );
        zSpriderJobDto.setCreateDate( entity.getCreateDate() );
        zSpriderJobDto.setCreateUserId( entity.getCreateUserId() );
        zSpriderJobDto.setUpdateDate( entity.getUpdateDate() );
        zSpriderJobDto.setUpdateUserId( entity.getUpdateUserId() );
        zSpriderJobDto.setBz( entity.getBz() );

        return zSpriderJobDto;
    }

    @Override
    public List<ZSpriderJob> toEntity(List<ZSpriderJobDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ZSpriderJob> list = new ArrayList<ZSpriderJob>( dtoList.size() );
        for ( ZSpriderJobDto zSpriderJobDto : dtoList ) {
            list.add( toEntity( zSpriderJobDto ) );
        }

        return list;
    }

    @Override
    public List<ZSpriderJobDto> toDto(List<ZSpriderJob> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ZSpriderJobDto> list = new ArrayList<ZSpriderJobDto>( entityList.size() );
        for ( ZSpriderJob zSpriderJob : entityList ) {
            list.add( toDto( zSpriderJob ) );
        }

        return list;
    }
}
