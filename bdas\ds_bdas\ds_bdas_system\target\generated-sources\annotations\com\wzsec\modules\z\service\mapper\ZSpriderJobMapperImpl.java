package com.wzsec.modules.z.service.mapper;

import com.wzsec.modules.z.domain.ZSpriderJob;
import com.wzsec.modules.z.service.dto.ZSpriderJobDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T16:26:48+0800",
    comments = "version: 1.2.0.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ZSpriderJobMapperImpl implements ZSpriderJobMapper {

    @Override
    public ZSpriderJobDto toDto(ZSpriderJob entity) {
        if ( entity == null ) {
            return null;
        }

        ZSpriderJobDto zSpriderJobDto = new ZSpriderJobDto();

        zSpriderJobDto.setBz( entity.getBz() );
        zSpriderJobDto.setCreateDate( entity.getCreateDate() );
        zSpriderJobDto.setCreateUserId( entity.getCreateUserId() );
        zSpriderJobDto.setCron( entity.getCron() );
        zSpriderJobDto.setId( entity.getId() );
        zSpriderJobDto.setJobName( entity.getJobName() );
        zSpriderJobDto.setScanFlag( entity.getScanFlag() );
        zSpriderJobDto.setSpriderUrl( entity.getSpriderUrl() );
        zSpriderJobDto.setStatus( entity.getStatus() );
        zSpriderJobDto.setUpdateDate( entity.getUpdateDate() );
        zSpriderJobDto.setUpdateUserId( entity.getUpdateUserId() );

        return zSpriderJobDto;
    }

    @Override
    public List<ZSpriderJobDto> toDto(List<ZSpriderJob> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ZSpriderJobDto> list = new ArrayList<ZSpriderJobDto>( entityList.size() );
        for ( ZSpriderJob zSpriderJob : entityList ) {
            list.add( toDto( zSpriderJob ) );
        }

        return list;
    }

    @Override
    public ZSpriderJob toEntity(ZSpriderJobDto dto) {
        if ( dto == null ) {
            return null;
        }

        ZSpriderJob zSpriderJob = new ZSpriderJob();

        zSpriderJob.setBz( dto.getBz() );
        zSpriderJob.setCreateDate( dto.getCreateDate() );
        zSpriderJob.setCreateUserId( dto.getCreateUserId() );
        zSpriderJob.setCron( dto.getCron() );
        zSpriderJob.setId( dto.getId() );
        zSpriderJob.setJobName( dto.getJobName() );
        zSpriderJob.setScanFlag( dto.getScanFlag() );
        zSpriderJob.setSpriderUrl( dto.getSpriderUrl() );
        zSpriderJob.setStatus( dto.getStatus() );
        zSpriderJob.setUpdateDate( dto.getUpdateDate() );
        zSpriderJob.setUpdateUserId( dto.getUpdateUserId() );

        return zSpriderJob;
    }

    @Override
    public List<ZSpriderJob> toEntity(List<ZSpriderJobDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ZSpriderJob> list = new ArrayList<ZSpriderJob>( dtoList.size() );
        for ( ZSpriderJobDto zSpriderJobDto : dtoList ) {
            list.add( toEntity( zSpriderJobDto ) );
        }

        return list;
    }
}
