package com.wzsec.modules.discover.service.mapper;

import com.wzsec.modules.discover.domain.Outlineresult;
import com.wzsec.modules.discover.service.dto.OutlineresultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:30+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class OutlineresultMapperImpl implements OutlineresultMapper {

    @Override
    public Outlineresult toEntity(OutlineresultDto dto) {
        if ( dto == null ) {
            return null;
        }

        Outlineresult outlineresult = new Outlineresult();

        outlineresult.setId( dto.getId() );
        outlineresult.setTaskname( dto.getTaskname() );
        outlineresult.setLocation( dto.getLocation() );
        outlineresult.setTypenum( dto.getTypenum() );
        outlineresult.setStarttime( dto.getStarttime() );
        outlineresult.setEndtime( dto.getEndtime() );
        outlineresult.setUsetime( dto.getUsetime() );
        outlineresult.setSubmituser( dto.getSubmituser() );
        outlineresult.setDatasourcetype( dto.getDatasourcetype() );
        outlineresult.setCreatetime( dto.getCreatetime() );
        outlineresult.setSparefield1( dto.getSparefield1() );
        outlineresult.setSparefield2( dto.getSparefield2() );
        outlineresult.setSparefield3( dto.getSparefield3() );
        outlineresult.setSparefield4( dto.getSparefield4() );
        outlineresult.setSparefield5( dto.getSparefield5() );

        return outlineresult;
    }

    @Override
    public OutlineresultDto toDto(Outlineresult entity) {
        if ( entity == null ) {
            return null;
        }

        OutlineresultDto outlineresultDto = new OutlineresultDto();

        outlineresultDto.setId( entity.getId() );
        outlineresultDto.setTaskname( entity.getTaskname() );
        outlineresultDto.setLocation( entity.getLocation() );
        outlineresultDto.setTypenum( entity.getTypenum() );
        outlineresultDto.setStarttime( entity.getStarttime() );
        outlineresultDto.setEndtime( entity.getEndtime() );
        outlineresultDto.setUsetime( entity.getUsetime() );
        outlineresultDto.setSubmituser( entity.getSubmituser() );
        outlineresultDto.setDatasourcetype( entity.getDatasourcetype() );
        outlineresultDto.setCreatetime( entity.getCreatetime() );
        outlineresultDto.setSparefield1( entity.getSparefield1() );
        outlineresultDto.setSparefield2( entity.getSparefield2() );
        outlineresultDto.setSparefield3( entity.getSparefield3() );
        outlineresultDto.setSparefield4( entity.getSparefield4() );
        outlineresultDto.setSparefield5( entity.getSparefield5() );

        return outlineresultDto;
    }

    @Override
    public List<Outlineresult> toEntity(List<OutlineresultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<Outlineresult> list = new ArrayList<Outlineresult>( dtoList.size() );
        for ( OutlineresultDto outlineresultDto : dtoList ) {
            list.add( toEntity( outlineresultDto ) );
        }

        return list;
    }

    @Override
    public List<OutlineresultDto> toDto(List<Outlineresult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<OutlineresultDto> list = new ArrayList<OutlineresultDto>( entityList.size() );
        for ( Outlineresult outlineresult : entityList ) {
            list.add( toDto( outlineresult ) );
        }

        return list;
    }
}
