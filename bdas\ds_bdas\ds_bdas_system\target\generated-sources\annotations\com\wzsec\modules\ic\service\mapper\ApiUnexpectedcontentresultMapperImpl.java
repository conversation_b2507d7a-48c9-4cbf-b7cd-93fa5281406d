package com.wzsec.modules.ic.service.mapper;

import com.wzsec.modules.ic.domain.ApiUnexpectedcontentresult;
import com.wzsec.modules.ic.service.dto.ApiUnexpectedcontentresultDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-04T14:00:31+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class ApiUnexpectedcontentresultMapperImpl implements ApiUnexpectedcontentresultMapper {

    @Override
    public ApiUnexpectedcontentresult toEntity(ApiUnexpectedcontentresultDto dto) {
        if ( dto == null ) {
            return null;
        }

        ApiUnexpectedcontentresult apiUnexpectedcontentresult = new ApiUnexpectedcontentresult();

        apiUnexpectedcontentresult.setId( dto.getId() );
        apiUnexpectedcontentresult.setApicode( dto.getApicode() );
        apiUnexpectedcontentresult.setCheckobject( dto.getCheckobject() );
        apiUnexpectedcontentresult.setUnexpectedcontent( dto.getUnexpectedcontent() );
        apiUnexpectedcontentresult.setRisk( dto.getRisk() );
        apiUnexpectedcontentresult.setCalltime( dto.getCalltime() );
        apiUnexpectedcontentresult.setChecktime( dto.getChecktime() );
        apiUnexpectedcontentresult.setSparefield1( dto.getSparefield1() );
        apiUnexpectedcontentresult.setSparefield2( dto.getSparefield2() );
        apiUnexpectedcontentresult.setSparefield3( dto.getSparefield3() );
        apiUnexpectedcontentresult.setSparefield4( dto.getSparefield4() );
        apiUnexpectedcontentresult.setApiurl( dto.getApiurl() );

        return apiUnexpectedcontentresult;
    }

    @Override
    public ApiUnexpectedcontentresultDto toDto(ApiUnexpectedcontentresult entity) {
        if ( entity == null ) {
            return null;
        }

        ApiUnexpectedcontentresultDto apiUnexpectedcontentresultDto = new ApiUnexpectedcontentresultDto();

        apiUnexpectedcontentresultDto.setId( entity.getId() );
        apiUnexpectedcontentresultDto.setApicode( entity.getApicode() );
        apiUnexpectedcontentresultDto.setCheckobject( entity.getCheckobject() );
        apiUnexpectedcontentresultDto.setUnexpectedcontent( entity.getUnexpectedcontent() );
        apiUnexpectedcontentresultDto.setRisk( entity.getRisk() );
        apiUnexpectedcontentresultDto.setCalltime( entity.getCalltime() );
        apiUnexpectedcontentresultDto.setChecktime( entity.getChecktime() );
        apiUnexpectedcontentresultDto.setSparefield1( entity.getSparefield1() );
        apiUnexpectedcontentresultDto.setSparefield2( entity.getSparefield2() );
        apiUnexpectedcontentresultDto.setSparefield3( entity.getSparefield3() );
        apiUnexpectedcontentresultDto.setSparefield4( entity.getSparefield4() );
        apiUnexpectedcontentresultDto.setApiurl( entity.getApiurl() );

        return apiUnexpectedcontentresultDto;
    }

    @Override
    public List<ApiUnexpectedcontentresult> toEntity(List<ApiUnexpectedcontentresultDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<ApiUnexpectedcontentresult> list = new ArrayList<ApiUnexpectedcontentresult>( dtoList.size() );
        for ( ApiUnexpectedcontentresultDto apiUnexpectedcontentresultDto : dtoList ) {
            list.add( toEntity( apiUnexpectedcontentresultDto ) );
        }

        return list;
    }

    @Override
    public List<ApiUnexpectedcontentresultDto> toDto(List<ApiUnexpectedcontentresult> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<ApiUnexpectedcontentresultDto> list = new ArrayList<ApiUnexpectedcontentresultDto>( entityList.size() );
        for ( ApiUnexpectedcontentresult apiUnexpectedcontentresult : entityList ) {
            list.add( toDto( apiUnexpectedcontentresult ) );
        }

        return list;
    }
}
